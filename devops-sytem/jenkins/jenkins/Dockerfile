FROM jenkins/jenkins:lts
MAINTAINER "<EMAIL>"
EXPOSE 8080 50000
ARG user=jenkins
ENV KUBERNETES_VERSION=v1.20.1 \
    HELM_VERSION=v3.5.2

USER root

#  安装常用软件
RUN apt-get update && apt-get install -y git \
    curl \
    vim \
    rsync \
    nano \
    unzip \
    mlocate \
    mycli \
    sudo \
    maven \
    iptables \
    apt-transport-https \
    ca-certificates \
    curl \
    gnupg2 \
    software-properties-common \
    python3-pip \
    tree \
    wget \
    apt-utils

# 安装Docker, 用于实现Docker in Docker
RUN curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null \
    && apt-get update \
    && apt-get install docker-ce docker-ce-cli containerd.io  -y\
    && ln -s  /usr/share/maven/bin/mvn /bin/maven \
    && rm -rf /var/lib/apt/lists/*

# 安装ansible
RUN pip3 install ansible


## 保留设置，Kubernetes 管理工具
RUN wget https://storage.googleapis.com/kubernetes-release/release/$KUBERNETES_VERSION/bin/linux/amd64/kubectl -O /usr/bin/kubectl\
	&& chmod +x /usr/bin/kubectl

RUN wget https://get.helm.sh/helm-${HELM_VERSION}-linux-amd64.tar.gz  -O /tmp/old.tar.gz \
    && tar xvf /tmp/old.tar.gz -C /tmp/ \
    && cp /tmp/linux-amd64/old /usr/bin/ \
    && rm -rf /tmp/*

# 启用ROOT调试容器
RUN echo "jenkins ALL=(ALL)   NOPASSWD: ALL" >> /etc/sudoers

USER ${user}
