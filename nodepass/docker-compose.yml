services:
  nodepass-ui:
    image: ghcr.io/nodepassproject/nodepassdash:beta
    container_name: nodepass-ui
    # network_mode: "host" # 如需要ipv6，可考虑使用host模式
    ports:
      - "3000:3000"
    volumes:
      - ./config:/app/config:ro
      - ./public:/app/public
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://127.0.0.1:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  nodepass:
    image: ghcr.io/yosebyte/nodepass:latest
    container_name: nodepass
    network_mode: "host"
    expose:
      - 9090
    restart: unless-stopped
    command:
      - "master://:9090/api?log=info&tls=0"
