apiVersion: apps/v1
kind: Deployment
metadata:
  name: nodepass-ui
  namespace: default
  labels:
    app: nodepass-ui
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nodepass-ui
  template:
    metadata:
      labels:
        app: nodepass-ui
    spec:
      containers:
      - name: nodepass-ui
        image: ghcr.io/nodepassproject/nodepassdash:beta
        ports:
        - containerPort: 3000
        env:
        - name: TZ
          value: "Asia/Shanghai"
        volumeMounts:
        - name: nodepass-ui-data
          mountPath: "/app/config"
          subPath: "config"
        - name: nodepass-ui-data
          mountPath: "/app/public"
          subPath: "public"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 30
          failureThreshold: 3
          successThreshold: 1
          timeoutSeconds: 1
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 30
      volumes:
      - name: nodepass-ui-data
        persistentVolumeClaim:
          claimName: nodepass-ui-data

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: nodepass-ui-data
  namespace: default
spec:
  storageClassName: local-path
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 512Mi

---
apiVersion: v1
kind: Service
metadata:
  name: nodepass-ui-service
  namespace: default
spec:
  selector:
    app: nodepass-ui
  ports:
    - protocol: TCP
      port: 3000
      targetPort: 3000

#---
#apiVersion: networking.k8s.io/v1
#kind: Ingress
#metadata:
#  name: nodepass-ui-ingress
#  namespace: default
#  annotations:
#    cert-manager.io/cluster-issuer: cloudflare-issuer
#spec:
#  ingressClassName: nginx
#  tls:
#  - hosts:
#    - nodepass-ui.k8s.flynix.one
#    secretName: nodepass-ui-tls
#  rules:
#  - host: nodepass-ui.k8s.flynix.one
#    http:
#      paths:
#      - path: /
#        pathType: Prefix
#        backend:
#          service:
#            name: nodepass-ui-service
#            port:
#              number: 8090
#

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nodepass-ui-ingress-cloudflare
  namespace: default
spec:
  ingressClassName: cloudflare-tunnel
  rules:
    - host: nodepass.flynix.one
      http:
        paths:
          - backend:
              service:
                name: nodepass-ui-service
                port:
                  number: 3000
            path: /
            pathType: Prefix

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nodepass
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: nodepass
  template:
    metadata:
      labels:
        app: nodepass
    spec:
      hostNetwork: true
      containers:
        - name: nodepass
          image: ghcr.io/yosebyte/nodepass:latest
          args:
            - "master://:9090/api?log=info&tls=0"
          ports:
            - containerPort: 9090
        volumeMounts:
          - name: nodepass-data
            mountPath: ""
            subPath: "config"
      volumes:
        - name: nodepass-data
          persistentVolumeClaim:
            claimName: nodepass-data
---
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: nodepass-data
  namespace: default
spec:
  storageClassName: local-path
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 512Mi
