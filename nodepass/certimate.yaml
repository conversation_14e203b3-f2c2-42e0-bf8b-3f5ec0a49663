apiVersion: apps/v1
kind: Deployment
metadata:
  name: certimate
  namespace: default
  labels:
    app: certimate
spec:
  replicas: 1
  selector:
    matchLabels:
      app: certimate
  template:
    metadata:
      labels:
        app: certimate
    spec:
      containers:
      - name: certimate
        image: registry.cn-shanghai.aliyuncs.com/usual2970/certimate:latest
        ports:
        - containerPort: 8090
        env:
        - name: TZ
          value: "Asia/Shanghai"
        volumeMounts:
        - name: certimate-data
          mountPath: "/app/pb_data"
      volumes:
      - name: certimate-data
        persistentVolumeClaim:
          claimName: certimate-data

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: certimate-data
  namespace: default
spec:
  storageClassName: local-path
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 128Mi

---
apiVersion: v1
kind: Service
metadata:
  name: certimate-service
  namespace: default
spec:
  selector:
    app: certimate
  ports:
    - protocol: TCP
      port: 8090
      targetPort: 8090

#---
#apiVersion: networking.k8s.io/v1
#kind: Ingress
#metadata:
#  name: certimate-ingress
#  namespace: default
#  annotations:
#    cert-manager.io/cluster-issuer: cloudflare-issuer
#spec:
#  ingressClassName: nginx
#  tls:
#  - hosts:
#    - certimate.k8s.flynix.one
#    secretName: certimate-tls
#  rules:
#  - host: certimate.k8s.flynix.one
#    http:
#      paths:
#      - path: /
#        pathType: Prefix
#        backend:
#          service:
#            name: certimate-service
#            port:
#              number: 8090
#

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: certimate-ingress-cloudflare
  namespace: default
spec:
  ingressClassName: cloudflare-tunnel
  rules:
    - host: certimate.flynix.one
      http:
        paths:
          - backend:
              service:
                name: certimate-service
                port:
                  number: 8090
            path: /
            pathType: Prefix
