.PHONY: help build dev

# Docker image name and tag
IMAGE:=registry.hak3.today/infra/apisix
TAG?=latest
# Shell that make should use
SHELL:=bash

# Namespace to deploy to
NAMESPACE:=default

help:
# http://marmelab.com/blog/2016/02/29/auto-documented-makefile.html
	@grep -E '^[a-zA-Z0-9_%/-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

build: DARGS?=
build: ## Make the latest build of the image
	cd dockerfile && docker build $(DARGS) --rm --force-rm -t $(IMAGE):$(TAG) .

push:
	docker push $(IMAGE):$(TAG)

release: build push

dev: ARGS?=
dev: PORT?=8090
dev: ## Make a container from a tagged image image
	docker run -it --rm -p $(PORT):$(PORT) $(IMAGE):$(TAG) $(ARGS)

clean:
	docker rmi -f $(IMAGE):$(TAG)

test:
	kubectl apply -f nodepass.yaml --dry-run=client -o yaml --namespace $(NAMESPACE)

install:
	kubectl apply -f nodepass.yaml --namespace $(NAMESPACE)

uninstall:
	kubectl delete -f nodepass.yaml --namespace $(NAMESPACE)
