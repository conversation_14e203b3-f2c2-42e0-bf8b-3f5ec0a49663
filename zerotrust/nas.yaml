apiVersion: v1
kind: Service
metadata:
  name: external-nas
  namespace: ea
spec:
  ports:
    - port: 80
      targetPort: 80
      protocol: TCP
---
apiVersion: v1
kind: Endpoints
metadata:
  name: external-nas
  namespace: ea
subsets:
  - addresses:
      - ip: *********
    ports:
      - port: 80
        protocol: TCP

---
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nas-ingress
  namespace: ea
spec:
  ingressClassName: nginx
  rules:
  - host: nas.k8s.hak3.today
    http:
      paths:
      - backend:
          service:
            name: external-nas
            port:
              number: 80
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - nas.k8s.hak3.today
    secretName: hak3-today-cert
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nas-ingress-cloudflare
  namespace: ea
spec:
  ingressClassName: cloudflare-tunnel
  rules:
  - host: nas.flynix.one
    http:
      paths:
      - backend:
          service:
            name:  external-nas
            port:
              number: 80
        path: /
        pathType: Prefix
