version: "3.8"
services:
   watchtower: #用于自动更新easytier镜像，若不需要请删除这部分
         command: --interval 3600 --cleanup --label-enable
         container_name: watchtower
         environment:
               - TZ=Asia/Shanghai
               - WATCHTOWER_NO_STARTUP_MESSAGE
         image: containrrr/watchtower
         restart: always
         volumes:
               - /var/run/docker.sock:/var/run/docker.sock
   easytier:
         restart: always
         labels:
               com.centurylinklabs.watchtower.enable: "true"
         privileged: true
         container_name: easytier
         hostname: easytier
         network_mode: host
         volumes:
               - /etc/easytier:/root
         environment:
               - TZ=Asia/Shanghai
         image: easytier/easytier:latest
         command: -i <ip> --network-name <用户> --network-secret <密码> -e tcp://<服务器地址>:11010 -l <监听地址>
