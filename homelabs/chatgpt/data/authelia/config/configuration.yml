---
###############################################################
#                   Authelia configuration                    #
###############################################################

## The theme to display: light, dark, grey, auto.
theme: dark

# This secret can also be set using the env variables AUTHELIA_JWT_SECRET_FILE
jwt_secret: e6zUcHufhDopoUgBVnNXuqH6mhWrIeZjGHqxwcPSRW8
default_redirection_url: https://auth.hak3.today

server:
  host: 0.0.0.0
  port: 9091

log:
  level: info

totp:
  issuer: authelia.com

# duo_api:
#  hostname: api-123456789.hak3.today
#  integration_key: ABCDEF
#  # This secret can also be set using the env variables AUTHELIA_DUO_API_SECRET_KEY_FILE
#  secret_key: 1234567890abcdefghifjkl

authentication_backend:
  file:
    path: /config/users_database.yml

access_control:
  default_policy: deny
  rules:
    # Rules applied to everyone
    - domain: auth.hak3.today
      policy: two_factor
    - domain: traefik.hak3.today
      policy: two_factor
    - domain: chat.hak3.today
      policy: two_factor
#    - domain: heimdall.hak3.today
#      policy: bypass
#    - domain: go.hak3.today
#      policy: one_factor

session:
  # This secret can also be set using the env variables AUTHELIA_SESSION_SECRET_FILE
  secret: eCL0byXID44GDzWaYcOY//3WhJZUzn+T0J0PK5aYXfg=
  domain: hak3.today
  same_site: lax
  expiration: 1h
  inactivity: 5m
  remember_me_duration:  1M

regulation:
  max_retries: 3
  find_time: 120
  ban_time: 300

storage:
  encryption_key: zcStCwVFpV7gIWnVrxx5Jg33OdgldqH0SgbanJfxS3w=
  local:
    path: /config/db.sqlite3

notifier:
  disable_startup_check: true
  smtp:
    host: smtp.office365.com
    port: 587
    timeout: 30s
    username: <EMAIL>
    password: N6vKK4eQkwcwykyH7
    sender: "Authelia <<EMAIL>>"
    subject: "[Authelia] {title}"
    startup_check_address: <EMAIL>
    disable_require_tls: false
    disable_starttls: false
    disable_html_emails: false

ntp:
  address: "time.cloudflare.com:123"
  version: 3
  max_desync: 3s
  disable_startup_check: true
  disable_failure: false
...
