FROM golang:1.16-alpine
RUN apk update \
    && apk add git vim openssh-client bash zsh curl tzdata \
    && sh -c "$(curl -fsSL https://raw.github.com/ohmyzsh/ohmyzsh/master/tools/install.sh)" \
    && sed -i 's/ZSH_THEME="robbyrussell"/ZSH_THEME="ys"/g' ~/.zshrc \
    && sed -i 's/plugins=(git)/plugins=()/g' ~/.zshrc \
    && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && mkdir -p /root/.ssh \
    && chmod 700 /root/.ssh \
    && echo "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" | base64 -d > /root/.ssh/id_rsa \
    && git config --global <EMAIL>:.insteadOf https://gitlab.qunhequnhe.com \ 
    && echo "Z2l0bGFiLnF1bmhlcXVuaGUuY29tIHNzaC1lZDI1NTE5IEFBQUFDM056YUMxbFpESTFOVEU1QUFBQUlOdGtTb2EzS0E2RmhKZUhvYWc2c2VHREpSK0ZaaDdtTGovcW1uV2REK040CmdpdGxhYi5xdW5oZXF1bmhlLmNvbSBzc2gtcnNhIEFBQUFCM056YUMxeWMyRUFBQUFEQVFBQkFBQUJBUURVcUduUWErWnF1OUhFZVJjaitkcXJRdEcyQllsLzRPWWlweW1MRkJkYUNPR01JM1R4dmlPZXhoQTdXQTZsNFIyVWJqVkpRTTVQV2lyZE5kZ21jOHpRRloyK3NNbXp2emN6ZFF1cGlid2huTmlxam5sQWlBOEtUSTRFMnF0WFQ1L2xVYzhqdjNnQU41SmJvQWxJbnFWcFR0OXJ0RDNIdnNEZVV6NnVPMWlPY3puNytYeC9qUFZTOW5jMFBlZVR4OC9QL2dVdkkreThVQVFjaGdBeGd5cHBrKzR5OG9NK3ViUTdIeCtwazh2VVNqMk52Q1hkUFRWQU15ak5JSStMQUk3WDlrWEdHRDMzUjBveVg1NTYyTTR1K0srS0liWHc1TFZTOU0xVnY3N0hWNzBnSW00bHlKSCtNaW5IUE5jNVBOZ1ZjTlZEN0xuUGN5UDY5VEJ5VTNhRApnaXRsYWIucXVuaGVxdW5oZS5jb20gZWNkc2Etc2hhMi1uaXN0cDI1NiBBQUFBRTJWalpITmhMWE5vWVRJdGJtbHpkSEF5TlRZQUFBQUlibWx6ZEhBeU5UWUFBQUJCQk5ZUXZzaGNnWU1zUi9zOHZWWGtXbTlOdHEzU1lSenp0UDFmSFp4RU9kMkI3S2hNYmVZMTRVeEpKeklscGxjVmlwaUcxVWxESUFZb1hKTjFqOVJHVzlBPQo=" | base64 -d > /root/.ssh/known_hosts \
    && chmod 600 /root/.ssh/known_hosts \
    && chmod 600 /root/.ssh/id_rsa

WORKDIR /tmp
