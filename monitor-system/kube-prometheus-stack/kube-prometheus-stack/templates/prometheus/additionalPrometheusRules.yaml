{{- if or .Values.additionalPrometheusRules .Values.additionalPrometheusRulesMap}}
apiVersion: v1
kind: List
metadata:
  name: {{ include "kube-prometheus-stack.fullname" $ }}-additional-prometheus-rules
  namespace: {{ template "kube-prometheus-stack.namespace" . }}
items:
{{- if .Values.additionalPrometheusRulesMap }}
{{- range $prometheusRuleName, $prometheusRule := .Values.additionalPrometheusRulesMap }}
  - apiVersion: monitoring.coreos.com/v1
    kind: PrometheusRule
    metadata:
      name: {{ template "kube-prometheus-stack.name" $ }}-{{ $prometheusRuleName }}
      namespace: {{ template "kube-prometheus-stack.namespace" $ }}
      labels:
        app: {{ template "kube-prometheus-stack.name" $ }}
{{ include "kube-prometheus-stack.labels" $ | indent 8 }}
    {{- if $prometheusRule.additionalLabels }}
{{ toYaml $prometheusRule.additionalLabels | indent 8 }}
    {{- end }}
    spec:
      groups:
{{ toYaml $prometheusRule.groups| indent 8 }}
{{- end }}
{{- else }}
{{- range .Values.additionalPrometheusRules }}
  - apiVersion: monitoring.coreos.com/v1
    kind: PrometheusRule
    metadata:
      name: {{ template "kube-prometheus-stack.name" $ }}-{{ .name }}
      namespace: {{ template "kube-prometheus-stack.namespace" $ }}
      labels:
        app: {{ template "kube-prometheus-stack.name" $ }}
{{ include "kube-prometheus-stack.labels" $ | indent 8 }}
    {{- if .additionalLabels }}
{{ toYaml .additionalLabels | indent 8 }}
    {{- end }}
    spec:
      groups:
{{ toYaml .groups| indent 8 }}
{{- end }}
{{- end }}
{{- end }}
