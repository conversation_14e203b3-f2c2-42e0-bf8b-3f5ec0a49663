{{- if and .Values.prometheus.enabled .Values.prometheus.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "kube-prometheus-stack.prometheus.serviceAccountName" . }}
  namespace: {{ template "kube-prometheus-stack.namespace" . }}
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-prometheus
    app.kubernetes.io/name: {{ template "kube-prometheus-stack.name" . }}-prometheus
    app.kubernetes.io/component: prometheus
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
{{- with .Values.prometheus.serviceAccount.annotations }}
  annotations:
{{ tpl (toYaml .) $ | indent 4 }}
{{- end }}
automountServiceAccountToken: {{ .Values.prometheus.serviceAccount.automountServiceAccountToken }}
{{- if .Values.global.imagePullSecrets }}
imagePullSecrets:
{{ include "kube-prometheus-stack.imagePullSecrets" . | trim | indent 2 }}
{{- end }}
{{- end }}
