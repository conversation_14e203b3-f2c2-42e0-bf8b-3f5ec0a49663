{{- if and .Values.prometheus.enabled .Values.global.rbac.create .Values.global.rbac.pspEnabled }}
{{- if .Capabilities.APIVersions.Has "policy/v1beta1/PodSecurityPolicy" }}
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: {{ template "kube-prometheus-stack.fullname" . }}-prometheus
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-prometheus
{{- if .Values.global.rbac.pspAnnotations }}
  annotations:
{{ toYaml .Values.global.rbac.pspAnnotations | indent 4 }}
{{- end }}
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
spec:
  privileged: false
  # Allow core volume types.
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
{{- if .Values.prometheus.podSecurityPolicy.volumes }}
{{ toYaml .Values.prometheus.podSecurityPolicy.volumes | indent 4 }}
{{- end }}
  hostNetwork: false
  hostIPC: false
  hostPID: false
  runAsUser:
    # Permits the container to run with root privileges as well.
    rule: 'RunAsAny'
  seLinux:
    # This policy assumes the nodes are using AppArmor rather than SELinux.
    rule: 'RunAsAny'
  supplementalGroups:
    rule: 'MustRunAs'
    ranges:
      # Allow adding the root group.
      - min: 0
        max: 65535
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      # Allow adding the root group.
      - min: 0
        max: 65535
  readOnlyRootFilesystem: false
{{- if .Values.prometheus.podSecurityPolicy.allowedCapabilities }}
  allowedCapabilities:
{{ toYaml .Values.prometheus.podSecurityPolicy.allowedCapabilities | indent 4 }}
{{- end }}
{{- if .Values.prometheus.podSecurityPolicy.allowedHostPaths }}
  allowedHostPaths:
{{ toYaml .Values.prometheus.podSecurityPolicy.allowedHostPaths | indent 4 }}
{{- end }}
{{- end }}
{{- end }}
