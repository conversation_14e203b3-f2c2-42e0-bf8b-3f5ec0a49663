{{- if and .Values.prometheus.enabled .Values.global.rbac.create .Values.global.rbac.pspEnabled }}
{{- if .Capabilities.APIVersions.Has "policy/v1beta1/PodSecurityPolicy" }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ template "kube-prometheus-stack.fullname" . }}-prometheus-psp
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-prometheus
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ template "kube-prometheus-stack.fullname" . }}-prometheus-psp
subjects:
  - kind: ServiceAccount
    name: {{ template "kube-prometheus-stack.prometheus.serviceAccountName" . }}
    namespace: {{ template "kube-prometheus-stack.namespace" . }}
{{- end }}
{{- end }}
