{{- if and .Values.alertmanager.enabled .Values.alertmanager.servicePerReplica.enabled .Values.alertmanager.ingressPerReplica.enabled }}
{{- $pathType := .Values.alertmanager.ingressPerReplica.pathType | default "" }}
{{- $count := .Values.alertmanager.alertmanagerSpec.replicas | int -}}
{{- $servicePort := .Values.alertmanager.service.port -}}
{{- $ingressValues := .Values.alertmanager.ingressPerReplica -}}
{{- $apiIsStable := eq (include "kube-prometheus-stack.ingress.isStable" .) "true" -}}
{{- $ingressSupportsPathType := eq (include "kube-prometheus-stack.ingress.supportsPathType" .) "true" -}}
apiVersion: v1
kind: List
metadata:
  name: {{ include "kube-prometheus-stack.fullname" $ }}-alertmanager-ingressperreplica
  namespace: {{ template "kube-prometheus-stack.namespace" . }}
items:
{{ range $i, $e := until $count }}
  - kind: Ingress
    apiVersion: {{ include "kube-prometheus-stack.ingress.apiVersion" $ }}
    metadata:
      name: {{ include "kube-prometheus-stack.fullname" $ }}-alertmanager-{{ $i }}
      namespace: {{ template "kube-prometheus-stack.namespace" $ }}
      labels:
        app: {{ include "kube-prometheus-stack.name" $ }}-alertmanager
      {{ include "kube-prometheus-stack.labels" $ | indent 8 }}
      {{- if $ingressValues.labels }}
{{ toYaml $ingressValues.labels | indent 8 }}
      {{- end }}
      {{- if $ingressValues.annotations }}
      annotations:
        {{- tpl (toYaml $ingressValues.annotations) $ | nindent 8 }}
      {{- end }}
    spec:
      {{- if $apiIsStable }}
      {{- if $ingressValues.ingressClassName }}
      ingressClassName: {{ $ingressValues.ingressClassName }}
      {{- end }}
      {{- end }}
      rules:
        - host: {{ $ingressValues.hostPrefix }}-{{ $i }}.{{ $ingressValues.hostDomain }}
          http:
            paths:
      {{- range $p := $ingressValues.paths }}
              - path: {{ tpl $p $ }}
                {{- if and $pathType $ingressSupportsPathType }}
                pathType: {{ $pathType }}
                {{- end }}
                backend:
                  {{- if $apiIsStable }}
                  service:
                    name: {{ include "kube-prometheus-stack.fullname" $ }}-alertmanager-{{ $i }}
                    port:
                      number: {{ $servicePort }}
                  {{- else }}
                  serviceName: {{ include "kube-prometheus-stack.fullname" $ }}-alertmanager-{{ $i }}
                  servicePort: {{ $servicePort }}
      {{- end }}
      {{- end -}}
      {{- if or $ingressValues.tlsSecretName $ingressValues.tlsSecretPerReplica.enabled }}
      tls:
        - hosts:
            - {{ $ingressValues.hostPrefix }}-{{ $i }}.{{ $ingressValues.hostDomain }}
          {{- if $ingressValues.tlsSecretPerReplica.enabled }}
          secretName: {{ $ingressValues.tlsSecretPerReplica.prefix }}-{{ $i }}
          {{- else }}
          secretName: {{ $ingressValues.tlsSecretName }}
          {{- end }}
      {{- end }}
{{- end -}}
{{- end -}}
