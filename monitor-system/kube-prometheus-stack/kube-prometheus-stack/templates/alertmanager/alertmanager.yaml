{{- if .Values.alertmanager.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: Alertmanager
metadata:
  name: {{ template "kube-prometheus-stack.alertmanager.crname" . }}
  namespace: {{ template "kube-prometheus-stack.namespace" . }}
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-alertmanager
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
{{- if .Values.alertmanager.annotations }}
  annotations:
{{ toYaml .Values.alertmanager.annotations | indent 4 }}
{{- end }}
spec:
{{- if .Values.alertmanager.alertmanagerSpec.image }}
  {{- $registry := .Values.global.imageRegistry | default .Values.alertmanager.alertmanagerSpec.image.registry -}}
  {{- if and .Values.alertmanager.alertmanagerSpec.image.tag .Values.alertmanager.alertmanagerSpec.image.sha }}
  image: "{{ $registry }}/{{ .Values.alertmanager.alertmanagerSpec.image.repository }}:{{ .Values.alertmanager.alertmanagerSpec.image.tag }}@sha256:{{ .Values.alertmanager.alertmanagerSpec.image.sha }}"
  {{- else if .Values.alertmanager.alertmanagerSpec.image.sha }}
  image: "{{ $registry }}/{{ .Values.alertmanager.alertmanagerSpec.image.repository }}@sha256:{{ .Values.alertmanager.alertmanagerSpec.image.sha }}"
  {{- else if .Values.alertmanager.alertmanagerSpec.image.tag }}
  image: "{{ $registry }}/{{ .Values.alertmanager.alertmanagerSpec.image.repository }}:{{ .Values.alertmanager.alertmanagerSpec.image.tag }}"
  {{- else }}
  image: "{{ $registry }}/{{ .Values.alertmanager.alertmanagerSpec.image.repository }}"
  {{- end }}
  version: {{ default .Values.alertmanager.alertmanagerSpec.image.tag .Values.alertmanager.alertmanagerSpec.version }}
  {{- if .Values.alertmanager.alertmanagerSpec.image.sha }}
  sha: {{ .Values.alertmanager.alertmanagerSpec.image.sha }}
  {{- end }}
{{- end }}
  replicas: {{ .Values.alertmanager.alertmanagerSpec.replicas }}
  listenLocal: {{ .Values.alertmanager.alertmanagerSpec.listenLocal }}
  serviceAccountName: {{ template "kube-prometheus-stack.alertmanager.serviceAccountName" . }}
  automountServiceAccountToken: {{ .Values.alertmanager.alertmanagerSpec.automountServiceAccountToken }}
{{- if .Values.alertmanager.alertmanagerSpec.externalUrl }}
  externalUrl: "{{ tpl .Values.alertmanager.alertmanagerSpec.externalUrl . }}"
{{- else if and .Values.alertmanager.ingress.enabled .Values.alertmanager.ingress.hosts }}
  externalUrl: "http://{{ tpl (index .Values.alertmanager.ingress.hosts 0) . }}{{ .Values.alertmanager.alertmanagerSpec.routePrefix }}"
{{- else }}
  externalUrl: http://{{ template "kube-prometheus-stack.fullname" . }}-alertmanager.{{ template "kube-prometheus-stack.namespace" . }}:{{ .Values.alertmanager.service.port }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.nodeSelector }}
  nodeSelector:
{{ toYaml .Values.alertmanager.alertmanagerSpec.nodeSelector | indent 4 }}
{{- end }}
  paused: {{ .Values.alertmanager.alertmanagerSpec.paused }}
  logFormat: {{ .Values.alertmanager.alertmanagerSpec.logFormat | quote  }}
  logLevel:  {{ .Values.alertmanager.alertmanagerSpec.logLevel | quote  }}
  retention: {{ .Values.alertmanager.alertmanagerSpec.retention | quote  }}
  {{- with .Values.alertmanager.enableFeatures }}
  enableFeatures:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.secrets }}
  secrets:
{{ toYaml .Values.alertmanager.alertmanagerSpec.secrets | indent 4 }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.configSecret }}
  configSecret: {{ .Values.alertmanager.alertmanagerSpec.configSecret }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.configMaps }}
  configMaps:
{{ toYaml .Values.alertmanager.alertmanagerSpec.configMaps | indent 4 }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.alertmanagerConfigSelector }}
  alertmanagerConfigSelector:
{{ tpl (toYaml .Values.alertmanager.alertmanagerSpec.alertmanagerConfigSelector | indent 4) . }}
{{ else }}
  alertmanagerConfigSelector: {}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.alertmanagerConfigNamespaceSelector }}
  alertmanagerConfigNamespaceSelector:
{{ tpl (toYaml .Values.alertmanager.alertmanagerSpec.alertmanagerConfigNamespaceSelector | indent 4) . }}
{{ else }}
  alertmanagerConfigNamespaceSelector: {}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.web }}
  web:
{{ toYaml .Values.alertmanager.alertmanagerSpec.web | indent 4 }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.alertmanagerConfiguration }}
  alertmanagerConfiguration:
{{ toYaml .Values.alertmanager.alertmanagerSpec.alertmanagerConfiguration | indent 4 }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.alertmanagerConfigMatcherStrategy }}
  alertmanagerConfigMatcherStrategy:
{{ toYaml .Values.alertmanager.alertmanagerSpec.alertmanagerConfigMatcherStrategy | indent 4 }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.resources }}
  resources:
{{ toYaml .Values.alertmanager.alertmanagerSpec.resources | indent 4 }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.routePrefix }}
  routePrefix: "{{ .Values.alertmanager.alertmanagerSpec.routePrefix }}"
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.securityContext }}
  securityContext:
{{ toYaml .Values.alertmanager.alertmanagerSpec.securityContext | indent 4 }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.storage }}
  storage:
{{ tpl (toYaml .Values.alertmanager.alertmanagerSpec.storage | indent 4) . }}
{{- end }}
{{- with .Values.alertmanager.alertmanagerSpec.persistentVolumeClaimRetentionPolicy }}
  persistentVolumeClaimRetentionPolicy:
    {{- toYaml . | nindent 4 }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.podMetadata }}
  podMetadata:
{{ toYaml .Values.alertmanager.alertmanagerSpec.podMetadata | indent 4 }}
{{- end }}
{{- if or .Values.alertmanager.alertmanagerSpec.podAntiAffinity .Values.alertmanager.alertmanagerSpec.affinity }}
  affinity:
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.affinity }}
{{ toYaml .Values.alertmanager.alertmanagerSpec.affinity | indent 4 }}
{{- end }}
{{- if eq .Values.alertmanager.alertmanagerSpec.podAntiAffinity "hard" }}
    podAntiAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
      - topologyKey: {{ .Values.alertmanager.alertmanagerSpec.podAntiAffinityTopologyKey }}
        labelSelector:
          matchExpressions:
            - {key: app.kubernetes.io/name, operator: In, values: [alertmanager]}
            - {key: alertmanager, operator: In, values: [{{ template "kube-prometheus-stack.alertmanager.crname" . }}]}
{{- else if eq .Values.alertmanager.alertmanagerSpec.podAntiAffinity "soft" }}
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          topologyKey: {{ .Values.alertmanager.alertmanagerSpec.podAntiAffinityTopologyKey }}
          labelSelector:
            matchExpressions:
              - {key: app.kubernetes.io/name, operator: In, values: [alertmanager]}
              - {key: alertmanager, operator: In, values: [{{ template "kube-prometheus-stack.alertmanager.crname" . }}]}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.tolerations }}
  tolerations:
{{ toYaml .Values.alertmanager.alertmanagerSpec.tolerations | indent 4 }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.topologySpreadConstraints }}
  topologySpreadConstraints:
{{ toYaml .Values.alertmanager.alertmanagerSpec.topologySpreadConstraints | indent 4 }}
{{- end }}
{{- if .Values.global.imagePullSecrets }}
  imagePullSecrets:
{{ include "kube-prometheus-stack.imagePullSecrets" . | trim | indent 4 }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.containers }}
  containers:
{{ toYaml .Values.alertmanager.alertmanagerSpec.containers | indent 4 }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.initContainers }}
  initContainers:
{{ toYaml .Values.alertmanager.alertmanagerSpec.initContainers | indent 4 }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.priorityClassName }}
  priorityClassName: {{.Values.alertmanager.alertmanagerSpec.priorityClassName }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.additionalPeers }}
  additionalPeers:
{{ toYaml .Values.alertmanager.alertmanagerSpec.additionalPeers | indent 4 }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.volumes }}
  volumes:
{{ toYaml .Values.alertmanager.alertmanagerSpec.volumes | indent 4 }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.volumeMounts }}
  volumeMounts:
{{ toYaml .Values.alertmanager.alertmanagerSpec.volumeMounts | indent 4 }}
{{- end }}
  portName: {{ .Values.alertmanager.alertmanagerSpec.portName }}
{{- if .Values.alertmanager.alertmanagerSpec.clusterAdvertiseAddress }}
  clusterAdvertiseAddress: {{ .Values.alertmanager.alertmanagerSpec.clusterAdvertiseAddress }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.clusterGossipInterval }}
  clusterGossipInterval: {{ .Values.alertmanager.alertmanagerSpec.clusterGossipInterval }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.clusterPeerTimeout }}
  clusterPeerTimeout: {{ .Values.alertmanager.alertmanagerSpec.clusterPeerTimeout }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.clusterPushpullInterval }}
  clusterPushpullInterval: {{ .Values.alertmanager.alertmanagerSpec.clusterPushpullInterval }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.clusterLabel }}
  clusterLabel: {{ .Values.alertmanager.alertmanagerSpec.clusterLabel }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.forceEnableClusterMode }}
  forceEnableClusterMode: {{ .Values.alertmanager.alertmanagerSpec.forceEnableClusterMode }}
{{- end }}
{{- if .Values.alertmanager.alertmanagerSpec.minReadySeconds }}
  minReadySeconds: {{ .Values.alertmanager.alertmanagerSpec.minReadySeconds }}
{{- end }}
{{- with .Values.alertmanager.alertmanagerSpec.additionalConfig }}
  {{- tpl (toYaml .) $ | nindent 2 }}
{{- end }}
{{- with .Values.alertmanager.alertmanagerSpec.additionalConfigString }}
  {{- tpl . $ | nindent 2 }}
{{- end }}
{{- end }}
