{{- if and .Values.prometheusOperator.enabled .Values.prometheusOperator.admissionWebhooks.enabled .Values.prometheusOperator.admissionWebhooks.patch.enabled .Values.global.rbac.create (not .Values.prometheusOperator.admissionWebhooks.certManager.enabled) }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name:  {{ template "kube-prometheus-stack.fullname" . }}-admission
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade,post-install,post-upgrade
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  labels:
    app: {{ template "kube-prometheus-stack.name" $ }}-admission
    {{- include "kube-prometheus-stack.prometheus-operator-webhook.labels" $ | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ template "kube-prometheus-stack.fullname" . }}-admission
subjects:
  - kind: ServiceAccount
    name: {{ template "kube-prometheus-stack.fullname" . }}-admission
    namespace: {{ template "kube-prometheus-stack.namespace" . }}
{{- end }}
