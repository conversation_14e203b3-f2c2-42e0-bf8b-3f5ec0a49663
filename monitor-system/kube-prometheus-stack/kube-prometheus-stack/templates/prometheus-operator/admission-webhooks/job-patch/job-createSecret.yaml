{{- if and .Values.prometheusOperator.enabled .Values.prometheusOperator.admissionWebhooks.enabled .Values.prometheusOperator.admissionWebhooks.patch.enabled (not .Values.prometheusOperator.admissionWebhooks.certManager.enabled) }}
apiVersion: batch/v1
kind: Job
metadata:
  name:  {{ template "kube-prometheus-stack.fullname" . }}-admission-create
  namespace: {{ template "kube-prometheus-stack.namespace" . }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
{{- with .Values.prometheusOperator.admissionWebhooks.annotations }}
{{ toYaml . | indent 4 }}
{{- end }}
  labels:
    app: {{ template "kube-prometheus-stack.name" $ }}-admission-create
    {{- include "kube-prometheus-stack.prometheus-operator-webhook.labels" $ | nindent 4 }}
spec:
  ttlSecondsAfterFinished: {{ .Values.prometheusOperator.admissionWebhooks.patch.ttlSecondsAfterFinished }}
  template:
    metadata:
      name:  {{ template "kube-prometheus-stack.fullname" . }}-admission-create
{{- with .Values.prometheusOperator.admissionWebhooks.patch.podAnnotations }}
      annotations:
{{ toYaml .  | indent 8 }}
{{- end }}
      labels:
        app: {{ template "kube-prometheus-stack.name" $ }}-admission-create
        {{- include "kube-prometheus-stack.prometheus-operator-webhook.labels" $ | nindent 8 }}
    spec:
      {{- if .Values.prometheusOperator.admissionWebhooks.patch.priorityClassName }}
      priorityClassName: {{ .Values.prometheusOperator.admissionWebhooks.patch.priorityClassName }}
      {{- end }}
      containers:
        - name: create
          {{- $registry := .Values.global.imageRegistry | default .Values.prometheusOperator.admissionWebhooks.patch.image.registry -}}
          {{- if .Values.prometheusOperator.admissionWebhooks.patch.image.sha }}
          image: {{ $registry }}/{{ .Values.prometheusOperator.admissionWebhooks.patch.image.repository }}:{{ .Values.prometheusOperator.admissionWebhooks.patch.image.tag }}@sha256:{{ .Values.prometheusOperator.admissionWebhooks.patch.image.sha }}
          {{- else }}
          image: {{ $registry }}/{{ .Values.prometheusOperator.admissionWebhooks.patch.image.repository }}:{{ .Values.prometheusOperator.admissionWebhooks.patch.image.tag }}
          {{- end }}
          imagePullPolicy: {{ .Values.prometheusOperator.admissionWebhooks.patch.image.pullPolicy }}
          args:
            - create
            - --host={{- include "kube-prometheus-stack.operator.admission-webhook.dnsNames" . | replace "\n" "," }}
            - --namespace={{ template "kube-prometheus-stack.namespace" . }}
            - --secret-name={{ template "kube-prometheus-stack.fullname" . }}-admission
          {{- with .Values.prometheusOperator.admissionWebhooks.createSecretJob }}
          securityContext:
          {{ toYaml .securityContext | nindent 12 }}
          {{- end }}
          resources:
{{ toYaml .Values.prometheusOperator.admissionWebhooks.patch.resources | indent 12 }}
      restartPolicy: OnFailure
      serviceAccountName: {{ template "kube-prometheus-stack.fullname" . }}-admission
      {{- with .Values.prometheusOperator.admissionWebhooks.patch.nodeSelector }}
      nodeSelector:
{{ toYaml . | indent 8 }}
      {{- end }}
      {{- with .Values.prometheusOperator.admissionWebhooks.patch.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
      {{- end }}
      {{- with .Values.prometheusOperator.admissionWebhooks.patch.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
      {{- end }}
{{- if .Values.prometheusOperator.admissionWebhooks.patch.securityContext }}
      securityContext:
{{ toYaml .Values.prometheusOperator.admissionWebhooks.patch.securityContext | indent 8 }}
{{- end }}
{{- end }}
