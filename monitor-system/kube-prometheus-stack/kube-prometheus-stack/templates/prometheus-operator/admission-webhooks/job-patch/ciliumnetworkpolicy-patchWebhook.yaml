{{- if and .Values.prometheusOperator.networkPolicy.enabled (eq .Values.prometheusOperator.networkPolicy.flavor "cilium") }}
{{- if and .Values.prometheusOperator.enabled .Values.prometheusOperator.admissionWebhooks.enabled .Values.prometheusOperator.admissionWebhooks.patch.enabled (not .Values.prometheusOperator.admissionWebhooks.certManager.enabled) }}
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name:  {{ template "kube-prometheus-stack.fullname" . }}-admission-patch
  namespace: {{ template "kube-prometheus-stack.namespace" . }}
  annotations:
    helm.sh/hook: post-install,post-upgrade
    helm.sh/hook-delete-policy: before-hook-creation,hook-succeeded
    ## Ensure this is run before the job
    helm.sh/hook-weight: "-5"
    {{- with .Values.prometheusOperator.admissionWebhooks.patch.annotations }}
    {{ toYaml . | nindent 4 }}
    {{- end }}
  labels:
    app: {{ template "kube-prometheus-stack.name" $ }}-admission-patch
    {{- include "kube-prometheus-stack.prometheus-operator-webhook.labels" $ | nindent 4 }}
spec:
  endpointSelector:
    matchLabels:
      app: {{ template "kube-prometheus-stack.name" $ }}-admission-patch
      {{- if .Values.prometheusOperator.networkPolicy.matchLabels }}
      {{ toYaml .Values.prometheusOperator.networkPolicy.matchLabels | nindent 6 }}
      {{- else }}
      {{- include "kube-prometheus-stack.prometheus-operator-webhook.labels" $ | nindent 6 }}
      {{- end }}
  egress:
    {{- if and .Values.prometheusOperator.networkPolicy.cilium .Values.prometheusOperator.networkPolicy.cilium.egress }}
      {{ toYaml .Values.prometheusOperator.networkPolicy.cilium.egress | nindent 6 }}
    {{- else }}
    - toEntities:
      - kube-apiserver
    {{- end }}
{{- end }}
{{- end }}
