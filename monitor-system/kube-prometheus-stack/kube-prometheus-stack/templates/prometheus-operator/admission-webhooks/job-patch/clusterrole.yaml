{{- if and .Values.prometheusOperator.enabled .Values.prometheusOperator.admissionWebhooks.enabled .Values.prometheusOperator.admissionWebhooks.patch.enabled .Values.global.rbac.create (not .Values.prometheusOperator.admissionWebhooks.certManager.enabled) }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name:  {{ template "kube-prometheus-stack.fullname" . }}-admission
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade,post-install,post-upgrade
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  labels:
    app: {{ template "kube-prometheus-stack.name" $ }}-admission
    {{- include "kube-prometheus-stack.prometheus-operator-webhook.labels" $ | nindent 4 }}
rules:
  - apiGroups:
      - admissionregistration.k8s.io
    resources:
      - validatingwebhookconfigurations
      - mutatingwebhookconfigurations
    verbs:
      - get
      - update
{{- if and (.Capabilities.APIVersions.Has "policy/v1beta1/PodSecurityPolicy") .Values.global.rbac.pspEnabled }}
{{- $kubeTargetVersion := default .Capabilities.KubeVersion.GitVersion .Values.kubeTargetVersionOverride }}
{{- if semverCompare "> 1.15.0-0" $kubeTargetVersion }}
  - apiGroups: ['policy']
{{- else }}
  - apiGroups: ['extensions']
{{- end }}
    resources: ['podsecuritypolicies']
    verbs:     ['use']
    resourceNames:
    - {{ template "kube-prometheus-stack.fullname" . }}-admission
{{- end }}
{{- end }}
