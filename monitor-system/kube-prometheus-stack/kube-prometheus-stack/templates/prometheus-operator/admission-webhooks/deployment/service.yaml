{{- if and .Values.prometheusOperator.enabled .Values.prometheusOperator.admissionWebhooks.deployment.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "kube-prometheus-stack.operator.fullname" . }}-webhook
  namespace: {{ template "kube-prometheus-stack.namespace" . }}
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-operator-webhook
    {{- include "kube-prometheus-stack.prometheus-operator-webhook.labels" . | nindent 4 }}
{{- if .Values.prometheusOperator.admissionWebhooks.deployment.service.labels }}
{{ toYaml .Values.prometheusOperator.admissionWebhooks.deployment.service.labels | indent 4 }}
{{- end }}
{{- if .Values.prometheusOperator.admissionWebhooks.deployment.service.annotations }}
  annotations:
{{ toYaml .Values.prometheusOperator.admissionWebhooks.deployment.service.annotations | indent 4 }}
{{- end }}
spec:
{{- if .Values.prometheusOperator.admissionWebhooks.deployment.service.clusterIP }}
  clusterIP: {{ .Values.prometheusOperator.admissionWebhooks.deployment.service.clusterIP }}
{{- end }}
{{- if .Values.prometheusOperator.admissionWebhooks.deployment.service.ipDualStack.enabled }}
  ipFamilies: {{ toYaml .Values.prometheusOperator.admissionWebhooks.deployment.service.ipDualStack.ipFamilies | nindent 4 }}
  ipFamilyPolicy: {{ .Values.prometheusOperator.admissionWebhooks.deployment.service.ipDualStack.ipFamilyPolicy }}
{{- end }}
{{- if .Values.prometheusOperator.admissionWebhooks.deployment.service.externalIPs }}
  externalIPs:
{{ toYaml .Values.prometheusOperator.admissionWebhooks.deployment.service.externalIPs | indent 4 }}
{{- end }}
{{- if .Values.prometheusOperator.admissionWebhooks.deployment.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.prometheusOperator.admissionWebhooks.deployment.service.loadBalancerIP }}
{{- end }}
{{- if .Values.prometheusOperator.admissionWebhooks.deployment.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
  {{- range $cidr := .Values.prometheusOperator.admissionWebhooks.deployment.service.loadBalancerSourceRanges }}
    - {{ $cidr }}
  {{- end }}
{{- end }}
{{- if ne .Values.prometheusOperator.admissionWebhooks.deployment.service.type "ClusterIP" }}
  externalTrafficPolicy: {{ .Values.prometheusOperator.admissionWebhooks.deployment.service.externalTrafficPolicy }}
{{- end }}
  ports:
  {{- if not .Values.prometheusOperator.admissionWebhooks.deployment.tls.enabled }}
  - name: http
    {{- if eq .Values.prometheusOperator.admissionWebhooks.deployment.service.type "NodePort" }}
    nodePort: {{ .Values.prometheusOperator.admissionWebhooks.deployment.service.nodePort }}
    {{- end }}
    port: 8080
    targetPort: http
  {{- end }}
  {{- if .Values.prometheusOperator.admissionWebhooks.deployment.tls.enabled }}
  - name: https
    {{- if eq .Values.prometheusOperator.admissionWebhooks.deployment.service.type "NodePort"}}
    nodePort: {{ .Values.prometheusOperator.admissionWebhooks.deployment.service.nodePortTls }}
    {{- end }}
    port: 443
    targetPort: https
  {{- end }}
  selector:
    app: {{ template "kube-prometheus-stack.name" . }}-operator-webhook
    release: {{ $.Release.Name | quote }}
  type: "{{ .Values.prometheusOperator.admissionWebhooks.deployment.service.type }}"
{{- end }}
