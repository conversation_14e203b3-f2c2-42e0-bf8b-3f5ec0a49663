{{- if and .Values.prometheusOperator.enabled .Values.prometheusOperator.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "kube-prometheus-stack.operator.serviceAccountName" . }}
  namespace: {{ template "kube-prometheus-stack.namespace" . }}
  labels:
    {{- include "kube-prometheus-stack.prometheus-operator.labels" . | nindent 4 }}
  {{- with .Values.prometheusOperator.serviceAccount.annotations }}
  annotations: {{- toYaml . | nindent 4 }}
  {{- end }}
automountServiceAccountToken: {{ .Values.prometheusOperator.serviceAccount.automountServiceAccountToken }}
{{- if .Values.global.imagePullSecrets }}
imagePullSecrets:
{{ include "kube-prometheus-stack.imagePullSecrets" . | trim | indent 2 }}
{{- end }}
{{- end }}
