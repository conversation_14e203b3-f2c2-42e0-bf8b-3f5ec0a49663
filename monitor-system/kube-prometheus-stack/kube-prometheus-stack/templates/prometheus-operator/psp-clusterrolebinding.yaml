{{- if and .Values.prometheusOperator.enabled .Values.global.rbac.create .Values.global.rbac.pspEnabled }}
{{- if .Capabilities.APIVersions.Has "policy/v1beta1/PodSecurityPolicy" }}
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ template "kube-prometheus-stack.operator.fullname" . }}-psp
  labels:
    {{- include "kube-prometheus-stack.prometheus-operator.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ template "kube-prometheus-stack.operator.fullname" . }}-psp
subjects:
  - kind: ServiceAccount
    name: {{ template "kube-prometheus-stack.operator.serviceAccountName" . }}
    namespace: {{ template "kube-prometheus-stack.namespace" . }}
{{- end }}
{{- end }}
