{{- $namespace := printf "%s" (include "kube-prometheus-stack.namespace" .) }}
{{- $defaultKubeletSvcName := printf "%s-kubelet" (include "kube-prometheus-stack.fullname" .) }}
{{- if .Values.prometheusOperator.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "kube-prometheus-stack.operator.fullname" . }}
  namespace: {{ template "kube-prometheus-stack.namespace" . }}
  labels:
    {{- include "kube-prometheus-stack.prometheus-operator.labels" . | nindent 4 }}
{{- if .Values.prometheusOperator.labels }}
{{ toYaml .Values.prometheusOperator.labels | indent 4 }}
{{- end }}
{{- if .Values.prometheusOperator.annotations }}
  annotations:
{{ toYaml .Values.prometheusOperator.annotations | indent 4 }}
{{- end }}
spec:
  replicas: 1
  revisionHistoryLimit: {{ .Values.prometheusOperator.revisionHistoryLimit }}
  selector:
    matchLabels:
      app: {{ template "kube-prometheus-stack.name" . }}-operator
      release: {{ $.Release.Name | quote }}
  {{- with .Values.prometheusOperator.strategy }}
  strategy:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  template:
    metadata:
      labels:
        {{- include "kube-prometheus-stack.prometheus-operator.labels" . | nindent 8 }}
{{- if .Values.prometheusOperator.podLabels }}
{{ toYaml .Values.prometheusOperator.podLabels | indent 8 }}
{{- end }}
{{- if .Values.prometheusOperator.podAnnotations }}
      annotations:
{{ toYaml .Values.prometheusOperator.podAnnotations | indent 8 }}
{{- end }}
    spec:
    {{- if .Values.prometheusOperator.priorityClassName }}
      priorityClassName: {{ .Values.prometheusOperator.priorityClassName }}
    {{- end }}
    {{- if .Values.global.imagePullSecrets }}
      imagePullSecrets:
      {{- include "kube-prometheus-stack.imagePullSecrets" . | indent 8 }}
    {{- end }}
      containers:
        - name: {{ template "kube-prometheus-stack.name" . }}
          {{- $configReloaderRegistry := .Values.global.imageRegistry | default .Values.prometheusOperator.prometheusConfigReloader.image.registry -}}
          {{- $operatorRegistry := .Values.global.imageRegistry | default .Values.prometheusOperator.image.registry -}}
          {{- $thanosRegistry := .Values.global.imageRegistry | default .Values.prometheusOperator.thanosImage.registry -}}
          {{- if .Values.prometheusOperator.image.sha }}
          image: "{{ $operatorRegistry }}/{{ .Values.prometheusOperator.image.repository }}:{{ .Values.prometheusOperator.image.tag | default .Chart.AppVersion }}@sha256:{{ .Values.prometheusOperator.image.sha }}"
          {{- else }}
          image: "{{ $operatorRegistry }}/{{ .Values.prometheusOperator.image.repository }}:{{ .Values.prometheusOperator.image.tag | default .Chart.AppVersion }}"
          {{- end }}
          imagePullPolicy: "{{ .Values.prometheusOperator.image.pullPolicy }}"
          args:
            {{- if .Values.prometheusOperator.kubeletService.enabled }}
            - --kubelet-service={{ .Values.prometheusOperator.kubeletService.namespace }}/{{ default $defaultKubeletSvcName .Values.prometheusOperator.kubeletService.name  }}
            {{- if .Values.prometheusOperator.kubeletService.selector }}
            - --kubelet-selector={{ .Values.prometheusOperator.kubeletService.selector }}
            {{- end }}
            {{- end }}
            - --kubelet-endpoints={{ .Values.prometheusOperator.kubeletEndpointsEnabled }}
            - --kubelet-endpointslice={{ .Values.prometheusOperator.kubeletEndpointSliceEnabled }}
            {{- if .Values.prometheusOperator.logFormat }}
            - --log-format={{ .Values.prometheusOperator.logFormat }}
            {{- end }}
            {{- if .Values.prometheusOperator.logLevel }}
            - --log-level={{ .Values.prometheusOperator.logLevel }}
            {{- end }}
            {{- if .Values.prometheusOperator.denyNamespaces }}
            - --deny-namespaces={{ tpl (.Values.prometheusOperator.denyNamespaces | join ",") $ }}
            {{- end }}
            {{- with $.Values.prometheusOperator.namespaces }}
            {{- $namespaces := list }}
            {{- if .releaseNamespace }}
            {{- $namespaces = append $namespaces $namespace }}
            {{- end }}
            {{- if .additional }}
            {{- range $ns := .additional }}
            {{- $namespaces = append $namespaces (tpl $ns $) }}
            {{- end }}
            {{- end }}
            - --namespaces={{ $namespaces | mustUniq | join "," }}
            {{- end }}
            - --localhost=127.0.0.1
            {{- if .Values.prometheusOperator.prometheusDefaultBaseImage }}
            - --prometheus-default-base-image={{ .Values.global.imageRegistry | default .Values.prometheusOperator.prometheusDefaultBaseImageRegistry }}/{{ .Values.prometheusOperator.prometheusDefaultBaseImage }}
            {{- end }}
            {{- if .Values.prometheusOperator.alertmanagerDefaultBaseImage }}
            - --alertmanager-default-base-image={{ .Values.global.imageRegistry | default .Values.prometheusOperator.alertmanagerDefaultBaseImageRegistry }}/{{ .Values.prometheusOperator.alertmanagerDefaultBaseImage }}
            {{- end }}
            {{- if .Values.prometheusOperator.prometheusConfigReloader.image.sha }}
            - --prometheus-config-reloader={{ $configReloaderRegistry }}/{{ .Values.prometheusOperator.prometheusConfigReloader.image.repository }}:{{ .Values.prometheusOperator.prometheusConfigReloader.image.tag | default .Chart.AppVersion }}@sha256:{{ .Values.prometheusOperator.prometheusConfigReloader.image.sha }}
            {{- else }}
            - --prometheus-config-reloader={{ $configReloaderRegistry }}/{{ .Values.prometheusOperator.prometheusConfigReloader.image.repository }}:{{ .Values.prometheusOperator.prometheusConfigReloader.image.tag | default .Chart.AppVersion }}
            {{- end }}
            - --config-reloader-cpu-request={{ (((.Values.prometheusOperator.prometheusConfigReloader.resources).requests).cpu) | default 0 }}
            - --config-reloader-cpu-limit={{ (((.Values.prometheusOperator.prometheusConfigReloader.resources).limits).cpu) | default 0 }}
            - --config-reloader-memory-request={{ (((.Values.prometheusOperator.prometheusConfigReloader.resources).requests).memory) | default 0 }}
            - --config-reloader-memory-limit={{ (((.Values.prometheusOperator.prometheusConfigReloader.resources).limits).memory) | default 0 }}
            {{- if .Values.prometheusOperator.prometheusConfigReloader.enableProbe }}
            - --enable-config-reloader-probes=true
            {{- end }}
            {{- if .Values.prometheusOperator.alertmanagerInstanceNamespaces }}
            - --alertmanager-instance-namespaces={{ .Values.prometheusOperator.alertmanagerInstanceNamespaces | join "," }}
            {{- end }}
            {{- if .Values.prometheusOperator.alertmanagerInstanceSelector }}
            - --alertmanager-instance-selector={{ .Values.prometheusOperator.alertmanagerInstanceSelector }}
            {{- end }}
            {{- if .Values.prometheusOperator.alertmanagerConfigNamespaces }}
            - --alertmanager-config-namespaces={{ .Values.prometheusOperator.alertmanagerConfigNamespaces | join "," }}
            {{- end }}
            {{- if .Values.prometheusOperator.prometheusInstanceNamespaces }}
            - --prometheus-instance-namespaces={{ .Values.prometheusOperator.prometheusInstanceNamespaces | join "," }}
            {{- end }}
            {{- if .Values.prometheusOperator.prometheusInstanceSelector }}
            - --prometheus-instance-selector={{ .Values.prometheusOperator.prometheusInstanceSelector }}
            {{- end }}
            {{- if .Values.prometheusOperator.thanosImage.sha }}
            - --thanos-default-base-image={{ $thanosRegistry }}/{{ .Values.prometheusOperator.thanosImage.repository }}:{{ .Values.prometheusOperator.thanosImage.tag }}@sha256:{{ .Values.prometheusOperator.thanosImage.sha }}
            {{- else }}
            - --thanos-default-base-image={{ $thanosRegistry }}/{{ .Values.prometheusOperator.thanosImage.repository }}:{{ .Values.prometheusOperator.thanosImage.tag }}
            {{- end }}
            {{- if .Values.prometheusOperator.thanosRulerInstanceNamespaces }}
            - --thanos-ruler-instance-namespaces={{ .Values.prometheusOperator.thanosRulerInstanceNamespaces | join "," }}
            {{- end }}
            {{- if .Values.prometheusOperator.thanosRulerInstanceSelector }}
            - --thanos-ruler-instance-selector={{ .Values.prometheusOperator.thanosRulerInstanceSelector }}
            {{- end }}
            {{- if .Values.prometheusOperator.secretFieldSelector }}
            - --secret-field-selector={{ tpl (.Values.prometheusOperator.secretFieldSelector) $ }}
            {{- end }}
            {{- if .Values.prometheusOperator.clusterDomain }}
            - --cluster-domain={{ .Values.prometheusOperator.clusterDomain }}
            {{- end }}
            {{- if .Values.prometheusOperator.tls.enabled }}
            - --web.enable-tls=true
            - --web.cert-file=/cert/{{ if .Values.prometheusOperator.admissionWebhooks.certManager.enabled }}tls.crt{{ else }}cert{{ end }}
            - --web.key-file=/cert/{{ if .Values.prometheusOperator.admissionWebhooks.certManager.enabled }}tls.key{{ else }}key{{ end }}
            - --web.listen-address=:{{ .Values.prometheusOperator.tls.internalPort }}
            - --web.tls-min-version={{ .Values.prometheusOperator.tls.tlsMinVersion }}
            {{- with .Values.prometheusOperator.extraArgs }}
            {{- tpl (toYaml .) $ | nindent 12 }}
            {{- end }}
          {{- with .Values.prometheusOperator.lifecycle }}
          lifecycle: {{ toYaml . | nindent 12 }}
          {{- end }}
          ports:
            - containerPort: {{ .Values.prometheusOperator.tls.internalPort }}
              name: https
          {{- else }}
          ports:
            - containerPort: 8080
              name: http
          {{- end }}
          env:
          {{- range $key, $value := .Values.prometheusOperator.env }}
          - name: {{ $key }}
            value: {{ $value | quote }}
          {{- end }}
          resources:
{{ toYaml .Values.prometheusOperator.resources | indent 12 }}
          securityContext:
{{ toYaml .Values.prometheusOperator.containerSecurityContext | indent 12 }}
          volumeMounts:
          {{- if .Values.prometheusOperator.tls.enabled }}
            - name: tls-secret
              mountPath: /cert
              readOnly: true
          {{- end }}
          {{- with .Values.prometheusOperator.extraVolumeMounts }}
          {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- if .Values.prometheusOperator.readinessProbe.enabled }}
          readinessProbe:
            httpGet:
              path: /healthz
              port: {{ .Values.prometheusOperator.tls.enabled | ternary "https" "http" }}
              scheme: {{ .Values.prometheusOperator.tls.enabled | ternary "HTTPS" "HTTP" }}
            initialDelaySeconds: {{ .Values.prometheusOperator.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.prometheusOperator.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.prometheusOperator.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.prometheusOperator.readinessProbe.successThreshold }}
            failureThreshold: {{ .Values.prometheusOperator.readinessProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.prometheusOperator.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: /healthz
              port: {{ .Values.prometheusOperator.tls.enabled | ternary "https" "http" }}
              scheme: {{ .Values.prometheusOperator.tls.enabled | ternary "HTTPS" "HTTP" }}
            initialDelaySeconds: {{ .Values.prometheusOperator.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.prometheusOperator.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.prometheusOperator.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.prometheusOperator.livenessProbe.successThreshold }}
            failureThreshold: {{ .Values.prometheusOperator.livenessProbe.failureThreshold }}
          {{- end }}
      volumes:
        {{- if .Values.prometheusOperator.tls.enabled }}
        - name: tls-secret
          secret:
            defaultMode: 420
            secretName: {{ template "kube-prometheus-stack.fullname" . }}-admission
        {{- end }}
        {{- with .Values.prometheusOperator.extraVolumes }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    {{- with .Values.prometheusOperator.dnsConfig }}
      dnsConfig:
{{ toYaml . | indent 8 }}
    {{- end }}
{{- if .Values.prometheusOperator.securityContext }}
      securityContext:
{{ toYaml .Values.prometheusOperator.securityContext | indent 8 }}
{{- end }}
      serviceAccountName: {{ template "kube-prometheus-stack.operator.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.prometheusOperator.automountServiceAccountToken }}
{{- if .Values.prometheusOperator.hostNetwork }}
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
{{- end }}
    {{- with .Values.prometheusOperator.nodeSelector }}
      nodeSelector:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.prometheusOperator.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.prometheusOperator.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ . }}
    {{- end }}
    {{- with .Values.prometheusOperator.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
    {{- end }}
{{- end }}
