{{- if and .Values.kubelet.enabled .Values.kubernetesServiceMonitors.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "kube-prometheus-stack.fullname" . }}-kubelet
  {{- if .Values.prometheus.prometheusSpec.ignoreNamespaceSelectors }}
  namespace: {{ .Values.kubelet.namespace }}
  {{- else }}
  namespace: {{ template "kube-prometheus-stack.namespace" . }}
  {{- end }}
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-kubelet
  {{- with .Values.kubelet.serviceMonitor.additionalLabels }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- include "kube-prometheus-stack.labels" . | indent 4 }}
spec:
  {{- include "servicemonitor.scrapeLimits" .Values.kubelet.serviceMonitor | nindent 2 }}
  {{- with .Values.kubelet.serviceMonitor.attachMetadata }}
  attachMetadata:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  jobLabel: k8s-app
  {{- with .Values.kubelet.serviceMonitor.targetLabels }}
  targetLabels:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  namespaceSelector:
    matchNames:
    - {{ .Values.kubelet.namespace }}
  selector:
    matchLabels:
      app.kubernetes.io/name: kubelet
      k8s-app: kubelet
  endpoints:
  - port: {{ template "kube-prometheus-stack.kubelet.scheme" . }}-metrics
    scheme: {{ template "kube-prometheus-stack.kubelet.scheme" . }}
    {{- if .Values.kubelet.serviceMonitor.interval }}
    interval: {{ .Values.kubelet.serviceMonitor.interval }}
    {{- end }}
    {{- if .Values.kubelet.serviceMonitor.proxyUrl }}
    proxyUrl: {{ .Values.kubelet.serviceMonitor.proxyUrl }}
    {{- end }}
    {{- if .Values.kubelet.serviceMonitor.scrapeTimeout }}
    scrapeTimeout: {{ .Values.kubelet.serviceMonitor.scrapeTimeout }}
    {{- end }}
    {{- include "kube-prometheus-stack.kubelet.authConfig" . | indent 4 }}
    honorLabels: {{ .Values.kubelet.serviceMonitor.honorLabels }}
    honorTimestamps: {{ .Values.kubelet.serviceMonitor.honorTimestamps }}
{{- if .Values.kubelet.serviceMonitor.metricRelabelings }}
    metricRelabelings:
{{ tpl (toYaml .Values.kubelet.serviceMonitor.metricRelabelings | indent 4) . }}
{{- end }}
{{- if .Values.kubelet.serviceMonitor.relabelings }}
    relabelings:
{{ tpl (toYaml .Values.kubelet.serviceMonitor.relabelings | indent 4) . }}
{{- end }}
{{- if .Values.kubelet.serviceMonitor.cAdvisor }}
  - port: {{ template "kube-prometheus-stack.kubelet.scheme" . }}-metrics
    scheme: {{ template "kube-prometheus-stack.kubelet.scheme" . }}
    path: /metrics/cadvisor
    {{- if .Values.kubelet.serviceMonitor.interval }}
    interval: {{ .Values.kubelet.serviceMonitor.interval }}
    {{- else }}
    interval: {{ .Values.kubelet.serviceMonitor.cAdvisorInterval }}
    {{- end }}
    {{- if .Values.kubelet.serviceMonitor.proxyUrl }}
    proxyUrl: {{ .Values.kubelet.serviceMonitor.proxyUrl }}
    {{- end }}
    {{- if .Values.kubelet.serviceMonitor.scrapeTimeout }}
    scrapeTimeout: {{ .Values.kubelet.serviceMonitor.scrapeTimeout }}
    {{- end }}
    honorLabels: {{ .Values.kubelet.serviceMonitor.honorLabels }}
    {{- if .Values.kubelet.serviceMonitor.trackTimestampsStaleness }}
    honorTimestamps: true
    {{- else }}
    honorTimestamps: {{ .Values.kubelet.serviceMonitor.honorTimestamps }}
    {{- end }}
    trackTimestampsStaleness: {{ .Values.kubelet.serviceMonitor.trackTimestampsStaleness }}
    {{- include "kube-prometheus-stack.kubelet.authConfig" . | indent 4 }}
{{- if .Values.kubelet.serviceMonitor.cAdvisorMetricRelabelings }}
    metricRelabelings:
{{ tpl (toYaml .Values.kubelet.serviceMonitor.cAdvisorMetricRelabelings | indent 4) . }}
{{- end }}
{{- if .Values.kubelet.serviceMonitor.cAdvisorRelabelings }}
    relabelings:
{{ tpl (toYaml .Values.kubelet.serviceMonitor.cAdvisorRelabelings | indent 4) . }}
{{- end }}
{{- end }}
{{- if .Values.kubelet.serviceMonitor.probes }}
  - port: {{ template "kube-prometheus-stack.kubelet.scheme" . }}-metrics
    scheme: {{ template "kube-prometheus-stack.kubelet.scheme" . }}
    path: /metrics/probes
    {{- if .Values.kubelet.serviceMonitor.interval }}
    interval: {{ .Values.kubelet.serviceMonitor.interval }}
    {{- end }}
    {{- if .Values.kubelet.serviceMonitor.proxyUrl }}
    proxyUrl: {{ .Values.kubelet.serviceMonitor.proxyUrl }}
    {{- end }}
    {{- if .Values.kubelet.serviceMonitor.scrapeTimeout }}
    scrapeTimeout: {{ .Values.kubelet.serviceMonitor.scrapeTimeout }}
    {{- end }}
    honorLabels: {{ .Values.kubelet.serviceMonitor.honorLabels }}
    honorTimestamps: {{ .Values.kubelet.serviceMonitor.honorTimestamps }}
    {{- include "kube-prometheus-stack.kubelet.authConfig" . | indent 4 }}
{{- if .Values.kubelet.serviceMonitor.probesMetricRelabelings }}
    metricRelabelings:
{{ tpl (toYaml .Values.kubelet.serviceMonitor.probesMetricRelabelings | indent 4) . }}
{{- end }}
{{- if .Values.kubelet.serviceMonitor.probesRelabelings }}
    relabelings:
{{ tpl (toYaml .Values.kubelet.serviceMonitor.probesRelabelings | indent 4) . }}
{{- end }}
{{- end }}
{{- if .Values.kubelet.serviceMonitor.resource }}
  - port: {{ template "kube-prometheus-stack.kubelet.scheme" . }}-metrics
    scheme: {{ template "kube-prometheus-stack.kubelet.scheme" . }}
    path: {{ .Values.kubelet.serviceMonitor.resourcePath }}
    {{- if .Values.kubelet.serviceMonitor.interval }}
    interval: {{ .Values.kubelet.serviceMonitor.interval }}
    {{- else }}
    interval: {{ .Values.kubelet.serviceMonitor.resourceInterval }}
    {{- end }}
    {{- if .Values.kubelet.serviceMonitor.proxyUrl }}
    proxyUrl: {{ .Values.kubelet.serviceMonitor.proxyUrl }}
    {{- end }}
    {{- if .Values.kubelet.serviceMonitor.scrapeTimeout }}
    scrapeTimeout: {{ .Values.kubelet.serviceMonitor.scrapeTimeout }}
    {{- end }}
    honorLabels: {{ .Values.kubelet.serviceMonitor.honorLabels }}
    {{- if .Values.kubelet.serviceMonitor.trackTimestampsStaleness }}
    honorTimestamps: true
    {{- else }}
    honorTimestamps: {{ .Values.kubelet.serviceMonitor.honorTimestamps }}
    {{- end }}
    trackTimestampsStaleness: {{ .Values.kubelet.serviceMonitor.trackTimestampsStaleness }}
    {{- include "kube-prometheus-stack.kubelet.authConfig" .  | indent 4 }}
{{- if .Values.kubelet.serviceMonitor.resourceMetricRelabelings }}
    metricRelabelings:
{{ tpl (toYaml .Values.kubelet.serviceMonitor.resourceMetricRelabelings | indent 4) . }}
{{- end }}
{{- if .Values.kubelet.serviceMonitor.resourceRelabelings }}
    relabelings:
{{ tpl (toYaml .Values.kubelet.serviceMonitor.resourceRelabelings | indent 4) . }}
{{- end }}
{{- end }}
{{- end }}
