{{- if and .Values.kubeControllerManager.enabled .Values.kubeControllerManager.serviceMonitor.enabled .Values.kubernetesServiceMonitors.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "kube-prometheus-stack.fullname" . }}-kube-controller-manager
  {{- if .Values.prometheus.prometheusSpec.ignoreNamespaceSelectors }}
  namespace: kube-system
  {{- else }}
  namespace: {{ template "kube-prometheus-stack.namespace" . }}
  {{- end }}
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-kube-controller-manager
  {{- with .Values.kubeControllerManager.serviceMonitor.additionalLabels }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
spec:
  jobLabel: {{ .Values.kubeControllerManager.serviceMonitor.jobLabel }}
  {{- with .Values.kubeControllerManager.serviceMonitor.targetLabels }}
  targetLabels:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- include "servicemonitor.scrapeLimits" .Values.kubeControllerManager.serviceMonitor | nindent 2 }}
  selector:
    {{- if .Values.kubeControllerManager.serviceMonitor.selector }}
    {{ tpl (toYaml .Values.kubeControllerManager.serviceMonitor.selector | nindent 4) . }}
    {{- else }}
    matchLabels:
      app: {{ template "kube-prometheus-stack.name" . }}-kube-controller-manager
      release: {{ $.Release.Name | quote }}
    {{- end }}
  namespaceSelector:
    matchNames:
      - "kube-system"
  endpoints:
  - port: {{ .Values.kubeControllerManager.serviceMonitor.port }}
    {{- if .Values.kubeControllerManager.serviceMonitor.interval }}
    interval: {{ .Values.kubeControllerManager.serviceMonitor.interval }}
    {{- end }}
    bearerTokenFile: /var/run/secrets/kubernetes.io/serviceaccount/token
    {{- if .Values.kubeControllerManager.serviceMonitor.proxyUrl }}
    proxyUrl: {{ .Values.kubeControllerManager.serviceMonitor.proxyUrl}}
    {{- end }}
    {{- if eq (include "kube-prometheus-stack.kubeControllerManager.insecureScrape" (list . false true .Values.kubeControllerManager.serviceMonitor.https )) "true" }}
    scheme: https
    tlsConfig:
      caFile: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
      {{- if eq (include "kube-prometheus-stack.kubeControllerManager.insecureScrape" (list . nil true .Values.kubeControllerManager.serviceMonitor.insecureSkipVerify)) "true" }}
      insecureSkipVerify: true
      {{- end }}
      {{- if .Values.kubeControllerManager.serviceMonitor.serverName }}
      serverName: {{ .Values.kubeControllerManager.serviceMonitor.serverName }}
      {{- end }}
    {{- end }}
{{- if .Values.kubeControllerManager.serviceMonitor.metricRelabelings }}
    metricRelabelings:
{{ tpl (toYaml .Values.kubeControllerManager.serviceMonitor.metricRelabelings | indent 4) . }}
{{- end }}
{{- if .Values.kubeControllerManager.serviceMonitor.relabelings }}
    relabelings:
{{ tpl (toYaml .Values.kubeControllerManager.serviceMonitor.relabelings | indent 4) . }}
{{- end }}
{{- end }}
