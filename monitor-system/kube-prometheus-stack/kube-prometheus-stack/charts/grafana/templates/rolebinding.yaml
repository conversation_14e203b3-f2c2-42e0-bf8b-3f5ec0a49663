{{- if .Values.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "grafana.fullname" . }}
  namespace: {{ include "grafana.namespace" . }}
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
  {{- with .Values.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  {{- if .Values.rbac.useExistingRole }}
  name: {{ .Values.rbac.useExistingRole }}
  {{- else }}
  name: {{ include "grafana.fullname" . }}
  {{- end }}
subjects:
- kind: ServiceAccount
  name: {{ include "grafana.serviceAccountName" . }}
  namespace: {{ include "grafana.namespace" . }}
{{- end }}
