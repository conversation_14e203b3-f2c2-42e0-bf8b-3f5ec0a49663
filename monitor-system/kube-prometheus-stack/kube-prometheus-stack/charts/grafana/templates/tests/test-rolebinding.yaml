{{- if and (.Capabilities.APIVersions.Has "policy/v1beta1/PodSecurityPolicy") .Values.testFramework.enabled .Values.rbac.pspEnabled }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "grafana.fullname" . }}-test
  namespace: {{ include "grafana.namespace" . }}
  annotations:
    "helm.sh/hook": {{ .Values.testFramework.hookType | default "test" }}
    "helm.sh/hook-delete-policy": "before-hook-creation,hook-succeeded"
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "grafana.fullname" . }}-test
subjects:
  - kind: ServiceAccount
    name: {{ include "grafana.serviceAccountNameTest" . }}
    namespace: {{ include "grafana.namespace" . }}
{{- end }}
