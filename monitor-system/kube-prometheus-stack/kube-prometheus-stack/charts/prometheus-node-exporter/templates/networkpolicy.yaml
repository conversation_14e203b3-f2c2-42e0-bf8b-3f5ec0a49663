{{- if .Values.networkPolicy.enabled }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "prometheus-node-exporter.fullname" . }}
  namespace: {{ include "prometheus-node-exporter.namespace" . }}
  labels:
    {{- include "prometheus-node-exporter.labels" $ | nindent 4 }}
  {{- with .Values.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  ingress:
    {{- if .Values.networkPolicy.ingress }}
      {{- toYaml .Values.networkPolicy.ingress | nindent 4 }}
    {{- else }}
    - ports:
      - port: {{ .Values.service.port }}
    {{- end }}
  policyTypes:
    - Egress
    - Ingress
  podSelector:
    matchLabels:
      {{- include "prometheus-node-exporter.selectorLabels" . | nindent 6 }}
{{- end }}
