# ==============================
# MySQL Exporter Service Monitor
# ==============================
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: mysql-exporter-monitor
  labels:
    app: mysql-exporter           # 与 Service/Deployment 保持标签一致
    release: kube-prometheus-stack           # 用于 Prometheus Operator 自动发现
spec:
  jobLabel: mysqld
  endpoints:
  - interval: 30s                 # 抓取间隔
    scrapeTimeout: 25s            # 超时时间（建议小于 interval）
    port: metrics                     # exporter 的端口
    path: /metrics                # exporter 的 metrics 端点
    scheme: http                  # 通信协议
    honorLabels: true             # 保留原始指标标签
    relabelings:                  # 指标重标签配置
    - sourceLabels: [__meta_kubernetes_namespace]
      targetLabel: source_namespace
  namespaceSelector:
    matchNames:
    - dbv
  selector:
    matchLabels:
      app: mysql-exporter         # 必须与 Service 的 metadata.labels 完全匹配
