# ========================
# MySQL Exporter ConfigMap
# ========================
apiVersion: v1
kind: ConfigMap
metadata:
  name: mysql-exporter-config  # 更明确的资源名称
data:
  .my.cnf: |-
    [client]
    user = mysql_exporter
    password = torr0
    
    [client.servers]
    user = mysql_exporter
    password = torr0  # 删除多余空格

---
# =========================
# MySQL Exporter Deployment
# =========================
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql-exporter
  labels:  # 添加全局标签
    app: mysql-exporter
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql-exporter  # 统一使用 app 标签（单数形式）
  template:
    metadata:
      labels:
        app: mysql-exporter  # 与 selector 保持一致
    spec:
      volumes:
        - name: config-volume
          configMap:
            name: mysql-exporter-config  # 同步修改 ConfigMap 引用
            items:
              - key: .my.cnf
                path: .my.cnf
      containers:
        - name: mysql-exporter  # 容器名称保持与 Deployment 一致
          image: prom/mysqld-exporter:v0.15.1
          command:
            - mysqld_exporter
            - --config.my-cnf=/etc/mysql-exporter/.my.cnf  # 规范配置文件路径
            - --mysqld.address=mysql.dbv.svc.cluster.local:3306
          securityContext:
            runAsNonRoot: true  # 不使用 root 用户
            runAsUser: 65534    # 使用 nobody 用户
          resources:
            limits:
              cpu: 200m
              memory: 256Mi
            requests:
              cpu: 100m
              memory: 128Mi
          livenessProbe:
            httpGet:
              path: /metrics
              port: 9104
            initialDelaySeconds: 30
            timeoutSeconds: 5
          readinessProbe:
            httpGet:
              path: /metrics
              port: 9104
            initialDelaySeconds: 10
            timeoutSeconds: 5
          ports:
            - containerPort: 9104
              protocol: TCP  # 显式声明协议
          volumeMounts:
            - name: config-volume
              mountPath: /etc/mysql-exporter  # 标准配置文件目录
              readOnly: true  # 添加只读挂载

---
# ======================
# MySQL Exporter Service
# ======================
apiVersion: v1
kind: Service
metadata:
  name: mysql-exporter  # 服务名与 Deployment 保持一致
  labels:
    app: mysql-exporter  # 标签统一
spec:
  selector:
    app: mysql-exporter  # 对齐 Deployment 标签
  ports:
    - name: metrics  # 更明确的端口名称
      protocol: TCP
      port: 9104
      targetPort: 9104
  type: ClusterIP  # 显式声明服务类型
