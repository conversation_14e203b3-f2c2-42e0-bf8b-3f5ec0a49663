apiVersion: monitoring.coreos.com/v1
kind: Alertmanager
metadata:
  name: mysql-alertmanager
  namespace: monitor-system
  labels:
    app: mysql-exporter
spec:
  replicas: 1
  configSecret: mysql-am-config
---
apiVersion: v1
kind: Secret
metadata:
  name: mysql-am-config
  namespace: monitor-system
stringData:
  alertmanager.yaml: |-
    global:
      resolve_timeout: 5m
      smtp_require_tls: false

    route:
      group_by: [alertname, job, severity]
      group_wait: 30s
      group_interval: 5m
      repeat_interval: 4h
      receiver: default-receiver
      routes:
      - match:
          severity: critical
        receiver: critical-receiver
        group_wait: 30s
        group_interval: 3m
        repeat_interval: 30m
      - match:
          severity: warning
        receiver: warning-receiver
        group_wait: 30s
        group_interval: 5m
        repeat_interval: 2h
      - match:
          severity: info
        receiver: info-receiver
        group_wait: 1m
        group_interval: 10m
        repeat_interval: 6h

    receivers:
    - name: default-receiver
      webhook_configs:
      - send_resolved: true
        url: https://open.feishu.cn/open-apis/bot/v2/hook/5c1ad818-6c8b-43fd-bfb5-e95527edb155
        http_config:
          follow_redirects: true
        # 关键修正：使用标准模板语法
        body: '{
          "msg_type": "text",
          "content": {
            "text": "ℹ️ [系统通知]\n🏷️ 告警名称: {{ .CommonLabels.alertname }}\n📝 告警详情: {{ .CommonAnnotations.description }}\n🔍 告警级别: {{ .CommonLabels.severity }}\n💻 实例: {{ .CommonLabels.instance }}\n⏰ 开始时间: {{ (.StartsAt.Add 28800e9).Format \"2006-01-02 15:04:05\" }}{{ if eq .Status \"resolved\" }}\n✅ 恢复时间: {{ (.EndsAt.Add 28800e9).Format \"2006-01-02 15:04:05\" }}{{ end }}\n🔗 详情链接: {{ .GeneratorURL }}"
          }
        }'

    - name: critical-receiver
      webhook_configs:
      - send_resolved: true
        url: https://open.feishu.cn/open-apis/bot/v2/hook/5c1ad818-6c8b-43fd-bfb5-e95527edb155
        http_config:
          follow_redirects: true
        body: '{
          "msg_type": "text",
          "content": {
            "text": "🔴 [严重告警]\n🏷️ 告警名称: {{ .CommonLabels.alertname }}\n📝 告警详情: {{ .CommonAnnotations.description }}\n⚠️ 告警值: {{ .CommonAnnotations.value }}\n💻 实例: {{ .CommonLabels.instance }}\n📊 指标: {{ .CommonLabels.job }}\n⏰ 开始时间: {{ (.StartsAt.Add 28800e9).Format \"2006-01-02 15:04:05\" }}{{ if eq .Status \"resolved\" }}\n✅ 恢复时间: {{ (.EndsAt.Add 28800e9).Format \"2006-01-02 15:04:05\" }}{{ end }}\n🔗 详情链接: {{ .GeneratorURL }}\n⚡ 请立即处理！"
          }
        }'

    - name: warning-receiver
      webhook_configs:
      - send_resolved: true
        url: https://open.feishu.cn/open-apis/bot/v2/hook/5c1ad818-6c8b-43fd-bfb5-e95527edb155
        http_config:
          follow_redirects: true
        body: '{
          "msg_type": "text",
          "content": {
            "text": "⚠️ [警告]\n🏷️ 告警名称: {{ .CommonLabels.alertname }}\n📝 告警详情: {{ .CommonAnnotations.description }}\n⚠️ 告警值: {{ .CommonAnnotations.value }}\n💻 实例: {{ .CommonLabels.instance }}\n📊 指标: {{ .CommonLabels.job }}\n⏰ 开始时间: {{ (.StartsAt.Add 28800e9).Format \"2006-01-02 15:04:05\" }}{{ if eq .Status \"resolved\" }}\n✅ 恢复时间: {{ (.EndsAt.Add 28800e9).Format \"2006-01-02 15:04:05\" }}{{ end }}\n🔗 详情链接: {{ .GeneratorURL }}"
          }
        }'

    - name: info-receiver
      webhook_configs:
      - send_resolved: true
        url: https://open.feishu.cn/open-apis/bot/v2/hook/5c1ad818-6c8b-43fd-bfb5-e95527edb155
        http_config:
          follow_redirects: true
        body: '{
          "msg_type": "text",
          "content": {
            "text": "💡 [信息通知]\n🏷️ 告警名称: {{ .CommonLabels.alertname }}\n📝 告警详情: {{ .CommonAnnotations.description }}\n💻 实例: {{ .CommonLabels.instance }}\n⏰ 开始时间: {{ (.StartsAt.Add 28800e9).Format \"2006-01-02 15:04:05\" }}{{ if eq .Status \"resolved\" }}\n✅ 恢复时间: {{ (.EndsAt.Add 28800e9).Format \"2006-01-02 15:04:05\" }}{{ end }}"
          }
        }'

    inhibit_rules:
    - source_match:
        severity: critical
      target_match:
        severity: warning
      equal: [alertname, instance, job]
    - source_match:
        severity: warning
      target_match:
        severity: info
      equal: [alertname, instance, job]

