apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  labels:
    app: sentry-web
  name: sentry-web
spec:
  ingressClassName: nginx
  rules:
  - host: sentry.k8s.hak3.today
    http:
      paths:
      - backend:
          service:
            name: sentry-web
            port:
              number: 9000
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - sentry.k8s.hak3.today
    secretName: hak3-crt-secret
