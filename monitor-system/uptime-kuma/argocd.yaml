apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: uptime-kuma
spec:
  project: default
  source:
    repoURL: '**************:k7zy/kubernetes-operator.git'
    path: monitor-system/uptime-kuma
    targetRevision: HEAD
    helm:
      valueFiles:
        - values.yaml
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: monitor-system
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
