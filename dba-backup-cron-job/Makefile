.PHONY: help build dev

# Docker image name and tag
IMAGE:=registry.hak3.today/infra/dba-backup
TAG?=ubuntu-18.04
# Shell that make should use
SHELL:=bash

help:
# http://marmelab.com/blog/2016/02/29/auto-documented-makefile.html
	@grep -E '^[a-zA-Z0-9_%/-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

build: DARGS?=
build: ## Make the latest build of the image
	docker build $(DARGS) --rm --force-rm -t $(IMAGE):$(TAG) . --no-cache

push:
	docker push $(IMAGE):$(TAG)

dev: ARGS?=bash
dev: PORT?=8090
dev: ## Make a container from a tagged image image
	docker run -it --rm -p $(PORT):$(PORT) $(IMAGE):$(TAG) $(ARGS)

deploy:
	helm upgrade --install atlassian-confluence ../atlassian-confluence  && helm upgrade --install mysql ../mysql

clean:
	docker rmi -f $(IMAGE):$(TAG)

uninstall:
	helm uninstall atlassian-confluence && helm uninstall mysql

