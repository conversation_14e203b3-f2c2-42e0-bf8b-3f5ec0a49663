apiVersion: batch/v1
kind: CronJob
metadata:
  name: gitlab-backup-cron-job
spec:
  #Cron Time is set according to server time, ensure server time zone and set accordingly.
  schedule: "0 1 * * *"
  failedJobsHistoryLimit: 1
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
  suspend: false
  jobTemplate:
    spec:
      backoffLimit: 1
      template:
        spec:
          containers:
            - name: gitlab-backup-job
              image: registry.hak3.today/infra/gitlab-backup-cron-job:v1.23.6
              env:
                - name: S3_BUCKET
                  value: "s3://dba-backup-11334/dev-pro-cluster/"
                - name: PG_PASS
                  value: "dummy-password"
              imagePullPolicy: Always
              command: 
              - /bin/sh
              - -c 
              - |-
                #!/bin/bash
                # For More Information, https://docs.gitlab.com/ce/raketasks/backup_restore.html#back-up-gitlab
                # For GitLab 12.1 and earlier, use `gitlab-rake gitlab:backup:create`
                # For later, use `gitlab-backup create`
                echo "======Start Backup GitLab======"
                POD_NAME=$(kubectl -n gitlab get pods -l app=toolbox -o jsonpath='{.items[*].metadata.name}')
                kubectl -n gitlab exec ${POD_NAME} -- backup-utility --maximum-backups 2
                echo "======End Backup GitLab======"
                echo "======Clean Expired Backup======"

                # find files which modified before 15 days ago and delete its
                kubectl -n gitlab exec ${POD_NAME} -- find /srv/gitlab/tmp/backups -name *tar -mtime +2 -exec ls {} \;
                kubectl -n gitlab exec ${POD_NAME} -- find /srv/gitlab/tmp/backups -name *tar -mtime +2 -exec rm -f {} \;

                # Backup Secrets
                kubectl get secrets gitlab-rails-secret -o jsonpath="{.data['secrets\.yml']}" | base64 --decode > gitlab-secrets.json
                kubectl cp gitlab-secrets.json ${POD_NAME}:/tmp/gitlab-secrets.json -c toolbox
                kubectl -n gitlab exec ${POD_NAME} -- s3cmd put /tmp/gitlab-secrets.json s3://gitlab-backups/
          restartPolicy: OnFailure
