kind: Deployment
apiVersion: apps/v1
metadata:
  name: mq-namesrv
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mq-namesrv
      name: mq-namesrv
  template:
    metadata:
     labels:
      app: mq-namesrv
      name: mq-namesrv
    spec:
      imagePullSecrets:
        - name: default-registry-secret
      containers:
      - name: mq-namesrv
        image: registry.cn-hangzhou.aliyuncs.com/sigmahouse/rocketmq-broker:4.9.2-centos-operator-0.3.0
        command: ["sh","mqnamesrv"]
        imagePullPolicy: IfNotPresent
        ports:
          - containerPort: 9876
        volumeMounts:
          - mountPath: /home/<USER>/logs
            name: namesrvlogs
          - mountPath: /home/<USER>/store
            name: namesrvstore
      volumes:
      - name: namesrvlogs
        emptyDir: {}
      - name: namesrvstore
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    service.beta.kubernetes.io/alibaba-cloud-loadbalancer-force-override-listeners: "true"
    service.beta.kubernetes.io/alibaba-cloud-loadbalancer-id: lb-bp1v921kevdv0f7pbfx7e
  name: mq-namesrv-svc
  labels:
    app: mq-namesrv-svc
spec:
  type: LoadBalancer
  ports:
    - port: 39876
      targetPort: 9876
      protocol: TCP
      name: http
  selector:
    app: mq-namesrv

---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: mq-broker-master
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mq-broker-master
      name: mq-broker-master
  template:
    metadata:
     labels:
      app: mq-broker-master
      name: mq-broker-master
    spec:
      imagePullSecrets:
        - name: default-registry-secret
      containers:
      - name: mq-broker-master
        image: registry.cn-hangzhou.aliyuncs.com/sigmahouse/rocketmq-broker:4.9.2-centos-operator-0.3.0
        command: ["sh","mqbroker", "-n","mq-namesrv-svc:39876"]
        imagePullPolicy: IfNotPresent
        ports:
          - containerPort: 10909
          - containerPort: 10911
        volumeMounts:
          - mountPath: /home/<USER>/logs
            name: brokerlogs
          - mountPath: /home/<USER>/store
            name: brokerstore
          - mountPath: /root/rocketmq/broker/conf/broker.conf
            subPath: broker.conf
            name: mq-broker-config
        env:
         - name: JAVA_OPT_EXT
           value: "-Xms2048M -Xmx2048M"
      volumes:
      - name: brokerlogs
        emptyDir: {}
      - name: brokerstore
        emptyDir: {}
#        persistentVolumeClaim:
#          claimName: broker-store
      - name: mq-broker-config
        configMap:
          name: mq-broker-config
          items:
            - key: "broker.conf"
              path: "broker.conf"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: mq-broker-config
data:
  broker.conf: |
    brokerClusterName = DefaultCluster
    brokerName = broker-a
    brokerId = 0
    deleteWhen = 04
    fileReservedTime = 48
    brokerRole = ASYNC_MASTER
    flushDiskType = ASYNC_FLUSH
    autoCreateSubscriptionGroup=true
    autoCreateTopicEnable=true

#---
#apiVersion: v1
#kind: PersistentVolumeClaim
#metadata:
#  name: broker-store
#spec:
#  storageClassName: alibabacloud-cnfs-nas
#  resources:
#    requests:
#      storage: 32G
#  volumeMode: Filesystem
#  accessModes:
#    - ReadWriteOnce
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mq-console
  labels:
    app: mq-console
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mq-console
  template:
    metadata:
      labels:
        app: mq-console
    spec:
      imagePullSecrets:
        - name: default-registry-secret
      containers:
        - name: mq-console
          image: "registry.cn-hangzhou.aliyuncs.com/sigmahouse/rcoketmq-console:v1.6.6"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          env:
          - name: serverPort
            value: "8080"
          - name: srvAddr
            value: "mq-namesrv-svc:39876"
          livenessProbe:
            tcpSocket:
              port: 8080
          readinessProbe:
            tcpSocket:
              port: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: mq-console
  labels:
    app: mq-console
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app: mq-console

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: mq-console
  labels:
    app: mq-console
spec:
  rules:
    - host: "mq-console-dev.k8s-aliyun.sigmahouse.vip"
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: mq-console
                port:
                  number: 8080
