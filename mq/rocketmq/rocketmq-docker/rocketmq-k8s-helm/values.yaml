# Default values for rocketmq-proxy.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

nameserver:
  replicaCount: 1
  image:
    pullPolicy: IfNotPresent
    repository: "apache/rocketmq"
    tag: "latest"

  imagePullSecrets: [ ]
  nameOverride: "nameserver"
  fullnameOverride: ""

  port: 9876
  heapSize: "1792M"

  serviceAccount:
    # Specifies whether a service account should be created
    create: false
    # Annotations to add to the service account
    annotations: { }
    # The name of the service account to use.
    # If not set and create is true, a name is generated using the fullname template
    name: ""

  podAnnotations: { }
  nodeSelector: { }

  podSecurityContext:
    { }
  # fsGroup: 2000

  service:
    type: ClusterIP
    servicePort: 9876
    enableDraining: true
    drainTimeout: 30

  ingress:
    enabled: false
    className: ""
    annotations:
      { }
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
    hosts:
      - host: chart-example.local
        paths:
          - path: /
            pathType: ImplementationSpecific
    tls: [ ]
    #  - secretName: chart-example-tls
    #    hosts:
    #      - chart-example.local

  persistence:
    enabled: false
    size: 8Gi

  resources:
    limits:
      cpu: 1
      memory: 2Gi
    requests:
      cpu: 250m
      memory: 2Gi

  autoscaling:
    enabled: false
    minReplicas: 1
    maxReplicas: 100
    targetCPUUtilizationPercentage: 80
    # targetMemoryUtilizationPercentage: 80

  configmap: |
    defaultThreadPoolNums=4
    deleteTopicWithBrokerRegistration=true


proxy:
  replicaCount: 1
  image:
    pullPolicy: IfNotPresent
    repository: "apache/rocketmq"
    tag: "latest"

  imagePullSecrets: [ ]
  nameOverride: "proxy"
  fullnameOverride: ""

  heapSize: "1920M"
  maxDirectMemorySize: "384M"
  rocketMQClusterName: "DefaultCluster"

  config: { }

  serviceAccount:
    # Specifies whether a service account should be created
    create: false
    # Annotations to add to the service account
    annotations: { }
    # The name of the service account to use.
    # If not set and create is true, a name is generated using the fullname template
    name: ""

  podAnnotations:

  service:
    remotingInternetPort: 8080
    grpcPort: 8081
    adminPort: 8088
    internet:
      enabled: false
      acl:
        enabled: false
        id: ""
      configs:
        - id: ""

  ingress:
    enabled: false
    annotations: { }
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
    hosts:
      - host: chart-example.local
        paths: [ ]
    tls: [ ]
    #  - secretName: chart-example-tls
    #    hosts:
    #      - chart-example.local

  resources:
    limits:
      cpu: 1
      memory: 8Gi
    requests:
      cpu: 1
      memory: 8Gi

  autoscaling:
    enabled: false
    minReplicas: 1
    maxReplicas: 100
    targetCPUUtilizationPercentage: 80

  nodeSelector: { }

  tolerations: [ ]

  affinity: { }


broker:
  replicaCount: 1
  image:
    pullPolicy: IfNotPresent
    repository: "apache/rocketmq"
    tag: "latest"

  partition: 0
  persistence:
    enabled: false
    size: 8Gi

  nameOverride: "broker"
  fullnameOverride: ""
  namesrvAddr: ""

  conf:
    clusterNameOverride: ""
    brokerNamePrefixOverride: ""
    tlsMode: disabled
    heapSize: "3G"
    enableStartupProbe: false
    startupProbeNamesrvAddr: ""
    nameServerHeadlessAddr: ""

  config: ""

  store:
    data:
      size: 10Gi
    log:
      size: 10Gi

  service:
    port: 10911

  jvmMemory: " -Xms4g -Xmx4g -Xmn2g -XX:MaxDirectMemorySize=8g "
  resources:
    limits:
      cpu: 2
      memory: 4Gi
    requests:
      cpu: 2
      memory: 4Gi

  nodeSelector: { }

  tolerations: [ ]