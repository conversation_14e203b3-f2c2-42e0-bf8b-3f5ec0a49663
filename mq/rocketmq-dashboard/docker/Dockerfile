FROM maven:3.5.0-jdk-8-alpine AS builder

RUN apk --update add git && \
    git clone https://github.com/apache/rocketmq-dashboard.git --depth 1 && \
    cd rocketmq-dashboard && \
    mvn clean package -Dmaven.test.skip=true && \
     mv target/rocketmq-dashboard-*.jar /rocketmq-dashboard.jar

FROM openjdk:8-jre-alpine

ENV JAVA_OPTS=""

RUN apk --update add tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    apk del tzdata && \
    rm -rf /var/cache/apk/*

COPY --from=builder /rocketmq-dashboard.jar /rocketmq-dashboard.jar

EXPOSE 8080

ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar /rocketmq-dashboard.jar"]
