.PHONY: help build dev

# Docker image name and tag
IMAGE:=registry.hak3.today/infra/rcoketmq-dashboard
TAG?=latest
# Shell that make should use
SHELL:=bash

help:
# http://marmelab.com/blog/2016/02/29/auto-documented-makefile.html
	@grep -E '^[a-zA-Z0-9_%/-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

build: DARGS?=
build: ## Make the latest build of the image
	cd docker && docker build $(DARGS) --rm --force-rm -t $(IMAGE):$(TAG) --no-cache .

push:
	docker push $(IMAGE):$(TAG)

dev: ARGS?=sh
dev: ## Make a container from a tagged image image
	docker run -it --rm  $(IMAGE):$(TAG) $(ARGS)

release: build push

test:
	helm upgrade --install rocketmq-dashboard ./rocketmq-dashboard --dry-run

install:
	helm upgrade --install rocketmq-dashboard ./rocketmq-dashboard


uninstall:
	helm uninstall rocketmq-dashboard --dry-run
