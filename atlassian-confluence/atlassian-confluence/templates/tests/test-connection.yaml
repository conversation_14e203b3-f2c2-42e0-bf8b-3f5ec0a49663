apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "atlassian-confluence.fullname" . }}-test-connection"
  labels:
    {{- include "atlassian-confluence.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "atlassian-confluence.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
