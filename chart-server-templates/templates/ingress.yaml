{{- if .Values.ingress.enabled -}}
{{- $fullName := include "chart-server-template.name" . -}}
{{- $servicePort :=  .Values.service.port -}}
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: {{ include "chart-server-template.name" .  }}
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: {{ include "chart-server-template.name" . }}
    helm.sh/chart: {{ include "chart-server-template.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  rules:
    - host: {{ .Values.ingress.hosts }}
      http:
        paths:
          - path: /
            backend:
              serviceName: {{ $fullName }}
              servicePort: {{ $servicePort }}
{{- end }}
