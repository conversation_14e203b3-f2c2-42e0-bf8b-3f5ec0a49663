apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "chart-server-template.name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: {{ include "chart-server-template.name" . }}
    helm.sh/chart: {{ include "chart-server-template.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    app: {{ include "chart-server-template.name" . }}
spec:
  replicas: {{ .Values.replicaCount }}
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "chart-server-template.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "chart-server-template.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
        tier: backend
    spec:
      imagePullSecrets:
        - name: regsecret
      {{- if .Values.skywalking.enable }}
      initContainers:
        - name: skywalking-agent-sidecar
          image: "{{ .Values.skywalking.image }}"
          imagePullPolicy: IfNotPresent
          command: ['sh']
          args: ['-c','mkdir -p /skywalking/agent &&
          cp -r /usr/skywalking/agent/* /skywalking/agent &&
          sed -i "s/Your_ApplicationName/${SW_AGENT_NAME}/g" /skywalking/agent/config/agent.config &&
          sed -i "s/127.0.0.1:11800/${SW_AGENT_COLLECTOR_BACKEND_SERVICES}/g" /skywalking/agent/config/agent.config &&
          sed -i "s/default-namespace/${SW_AGENT_NAMESPACE}/g" /skywalking/agent/config/agent.config ']
          volumeMounts:
            - mountPath: /skywalking/agent
              name: skywalking-agent
          env:
          - name: SW_AGENT_NAMESPACE
            value: {{ .Values.skywalking.SW_AGENT_NAMESPACE }}
          - name : SW_AGENT_NAME
            value: {{ .Values.skywalking.SW_AGENT_NAME}}
          - name: SW_AGENT_COLLECTOR_BACKEND_SERVICES
            value: {{ .Values.skywalking.SW_AGENT_COLLECTOR_BACKEND_SERVICES}}
      {{- end }}
      containers:
      - name: {{ include "chart-server-template.name" . }}
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag | int64 }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        resources:
          {{- toYaml .Values.resources | nindent 12 }}
        ports:
        - containerPort: {{ .Values.service.port }}
        - containerPort: {{ .Values.service.debugPort }}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - name: chart-server-template-storage
          mountPath: /opt/applog
        {{- if .Values.skywalking.enable }}
        - name: skywalking-agent
          mountPath: /skywalking/agent
        {{- end }}
        env:
        - name: DEBUGPORT
          value: "{{ .Values.service.debugPort }}"
        - name: SERVERPORT
          value: "{{ .Values.service.port }}"
        {{- if .Values.searchRole.enabled }}
        - name: ROLE
          value: {{ .Values.searchRole.role }}
        {{- end }}
        {{- if .Values.sentry.enable }}
        - name: SENTRY_DSN
          value: {{ .Values.sentry.dsn }}
        - name: SENTRY_ENVIRONMENT
          value: {{ .Values.sentry.environment }}
        - name: SENTRY_TAGS
          value: {{ .Values.sentry.tags }}
        {{- end }}
        {{- if .Values.skywalking.enable }}
        - name: SW_JAVA_AGENT
          value: {{ .Values.skywalking.SW_JAVA_AGENT }}
        {{- end }}
      volumes:
      - name: chart-server-template-storage
        persistentVolumeClaim:
          claimName: {{ include "chart-server-template.name" . }}
      {{- if .Values.skywalking.enable }}
      - name: skywalking-agent
        emptyDir: {}
      {{- end }}
      restartPolicy: Always
      securityContext:
        fsGroup: 1000
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
