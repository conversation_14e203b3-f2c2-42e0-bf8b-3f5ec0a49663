# Default values for meijian-chart-template.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository:
  tag: DATETIME
  pullPolicy: IfNotPresent

nameOverride: "chart-server-template"

service:
  type: ClusterIP
  port: 8080
  debugPort: 9090

rpc:
  enabled: false

searchRole:
  enabled: false
  role: '-Drole=producer'

skywalking:
  enable: false
  image: registry.meijian.tech/library/skywalking-agent-sidecar:latest
  SW_AGENT_NAMESPACE: ''
  SW_AGENT_NAME: ''
  SW_AGENT_COLLECTOR_BACKEND_SERVICES: ''
  SW_JAVA_AGENT: '-javaagent:/skywalking/agent/skywalking-agent.jar'

sentry:
  enable: false
  dsn: ''
  environment: ''
  tags: ''

ingress:
  enabled: false
  hosts:
    - host: "AppName.prodtest.k8s.sigmahouse.vip"
      paths: /

resources:
  limits:
    cpu: "1"
    memory: 2Gi
  requests:
    cpu: 30m
    memory: 200Mi

nodeSelector: {}

persistence:
  enable: true
  storageClass: "nfs-client"
  accessMode: ReadWriteOnce
  size: 100Gi
  annotations: {}

tolerations: []

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
            - key: kubernetes.io/hostname
              operator: NotIn
              values:
                - ************
    preferredDuringSchedulingIgnoredDuringExecution:
      - preference:
          matchExpressions:
            - key: kubernetes.io/os
              operator: In
              values:
                - linux
        weight: 1