FROM --platform=${BUILDPLATFORM:-amd64}  node:16.20.1-bullseye-slim as builder

# YApi 版本
ARG YAPI_VERSION=1.12.0

# 工作目录
WORKDIR /yapi/vendors

# 拉取 Yapi 源码
RUN  apt update \
    && apt install git python2 python3 build-essential -y \
    && mkdir -p /yapi/vendors \
    && git clone --branch "v${YAPI_VERSION}" --single-branch  --depth 1 https://github.com/YMFE/yapi.git /yapi/vendors

# 安装依赖
RUN yarn global add ykit \
    && yarn global add yapi-cli \
    && cp config_example.json ../config.json \
    && yarn install --production --registry https://registry.npm.taobao.org \
    && yarn add node-sass@7.0.3 \
    && cd ../ \
    && yapi plugin --name yapi-plugin-add-user

FROM  node:16.20.1-bullseye-slim

ARG DOCKER_COMPOSE_WAIT_VERSION=2.9.0
WORKDIR /yapi

COPY --from=builder /yapi .
ADD https://github.com/ufoscout/docker-compose-wait/releases/download/$DOCKER_COMPOSE_WAIT_VERSION/wait /wait
RUN chmod +x /wait

EXPOSE 3000

CMD /wait; node vendors/server/app.js
