#!/bin/bash

mail_address="xxxxx.com"

cat > add_user.ldif << EOF
dn: cn=$1@$mail_address,ou=People,dc=ldap,dc=xxxx,dc=net
objectClass: inetOrgPerson
objectClass: top
mail: $1@$mail_address
sn: $1
cn: $1@$mail_address
userPassword: $1@$mail_address
EOF

if [ $3 == "cs" ];then
cat >add_group.ldif<<EOF
dn: cn=confluence-users,ou=confluence,dc=ldap,dc=xxxx,dc=net
changetype: modify
add: uniqueMember
uniqueMember: cn=$1@$mail_address,ou=People,dc=ldap,dc=xxxx,dc=net#add: 添加的属性 ，uniqueMember: cn... 添加的属性值 视个人情况更改

dn: cn=customer service team,ou=confluence,dc=ldap,dc=xxxx,dc=net
changetype: modify
add: uniqueMember
uniqueMember: cn=$1@$mail_address,ou=People,dc=ldap,dc=xxxx,dc=net

dn: cn=jira-software-users,ou=jira,dc=ldap,dc=xxxx,dc=net
changetype: modify
add: uniqueMember
uniqueMember: cn=$1@$mail_address,ou=People,dc=ldap,dc=xxxx,dc=net
EOF
elif [ $3 == "dev" ];then
cat >add_group.ldif<<EOF
dn: cn=confluence-users,ou=confluence,dc=ldap,dc=xxxx,dc=net
changetype: modify
add: uniqueMember
uniqueMember: cn=$1@$mail_address,ou=People,dc=ldap,dc=xxxx,dc=net

dn: cn=developer,ou=confluence,dc=ldap,dc=xxxx,dc=net
changetype: modify
add: uniqueMember
uniqueMember: cn=$1@$mail_address,ou=People,dc=ldap,dc=xxxx,dc=net

dn: cn=harbor-dev-developer,ou=harbor,dc=ldap,dc=xxxx,dc=net
changetype: modify
add: member
member: cn=$1@$mail_address,ou=People,dc=ldap,dc=xxxx,dc=net

dn: cn=jenkins-dev-build,ou=jenkins,dc=ldap,dc=xxxx,dc=net
changetype: modify
add: uniqueMember
uniqueMember: cn=$1@$mail_address,ou=People,dc=ldap,dc=xxxx,dc=net

dn: cn=kibana-software-users,ou=kibana,dc=ldap,dc=xxxx,dc=net
changetype: modify
add: uniqueMember
uniqueMember: cn=$1@$mail_address,ou=People,dc=ldap,dc=xxxx,dc=net

dn: cn=rabbitmq-dev-management,ou=rabbitmq,dc=ldap,dc=xxxx,dc=net
changetype: modify
add: uniqueMember
uniqueMember: cn=$1@$mail_address,ou=People,dc=ldap,dc=xxxx,dc=net

dn: cn=jira-software-users,ou=jira,dc=ldap,dc=xxxx,dc=net
changetype: modify
add: uniqueMember
uniqueMember: cn=$1@$mail_address,ou=People,dc=ldap,dc=xxxx,dc=net
EOF
fi
#echo "######请输入ldap管理员密码#####"
echo "#####创建用户#####"
/usr/bin/ldapadd -D cn=Manager,dc=ldap,dc=xxxx,dc=net -y .ldappasswd -x -f add_user.ldif
echo "#####配置权限组#####"
/usr/bin/ldapadd -D cn=Manager,dc=ldap,dc=xxxx,dc=net -y .ldappasswd  -x -f add_group.ldif
if [ $? -eq 0 ];then
   echo "添加用户成功"
else
   echo "添加用户失败"
fi
