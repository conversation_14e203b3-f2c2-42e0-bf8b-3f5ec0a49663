apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "self-service-password.fullname" . }}-test-connection"
  labels:
    {{- include "self-service-password.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "self-service-password.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
