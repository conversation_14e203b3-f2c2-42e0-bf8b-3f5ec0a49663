apiVersion: v1
kind: ConfigMap
metadata:
  name: self-service-password-config

data:
  config.inc.local.php: |
    <?php
    #==============================================================================
    # Configuration
    #==============================================================================

    # Debug mode
    # true: log and display any errors or warnings (use this in configuration/testing)
    # false: log only errors and do not display them (use this in production)
    $debug = false;

    ## LDAP
    $ldap_url = "ldap://ldap-openldap.auth.svc.cluster.local:389";
    $ldap_starttls = false;
    $ldap_binddn = "cn=admin,dc=hak3,dc=today";
    $ldap_bindpw = 'T9zGIovJYJtQMnB';
    // for GSSAPI authentication, comment out ldap_bind* and uncomment ldap_krb5ccname lines
    //$ldap_krb5ccname = "/path/to/krb5cc";
    $ldap_base = "ou=People,dc=hak3,dc=today";
    $ldap_login_attribute = "cn";
    $ldap_fullname_attribute = "cn";
    $ldap_filter = "(&(objectClass=person)($ldap_login_attribute={login}))";
    $ldap_use_exop_passwd = false;
    $ldap_use_ppolicy_control = false;

    # Shadow options - require shadowAccount objectClass
    $shadow_options=[];
    # Update shadowLastChange
    $shadow_options['update_shadowLastChange'] = false;
    $shadow_options['update_shadowExpire'] = false;

    # Default to -1, never expire
    $shadow_options['shadow_expire_days'] = -1;

    # Hash mechanism for password:
    # SSHA, SSHA256, SSHA384, SSHA512
    # SHA, SHA256, SHA384, SHA512
    # SMD5
    # MD5
    # CRYPT
    # clear (the default)
    # auto (will check the hash of current password)
    # This option is not used with ad_mode = true
    $hash = "MD5";
    $hash_options=[];

    # Prefix to use for salt with CRYPT
    $hash_options['crypt_salt_prefix'] = "$6$";
    $hash_options['crypt_salt_length'] = "6";

    # USE rate-limiting by IP and/or by user
    $use_ratelimit = false;
    # dir for json db's (system default tmpdir)
    #$ratelimit_dbdir = '/tmp';
    # block attempts for same login ?
    $max_attempts_per_user = 2;
    # block attempts for same IP ?
    $max_attempts_per_ip = 2;
    # how many time to refuse subsequent requests ?
    $max_attempts_block_seconds = "60";
    # Header to use for client IP (HTTP_X_FORWARDED_FOR ?)
    $client_ip_header = 'REMOTE_ADDR';

    # Local password policy
    # This is applied before directory password policy
    # Minimal length
    $pwd_min_length = 6;
    # Maximal length
    $pwd_max_length = 32;
    # Minimal lower characters
    $pwd_min_lower = 0;
    # Minimal upper characters
    $pwd_min_upper = 0;
    # Minimal digit characters
    $pwd_min_digit = 0;
    # Minimal special characters
    $pwd_min_special = 0;
    # Definition of special characters
    $pwd_special_chars = "^a-zA-Z0-9";
    # Forbidden characters
    #$pwd_forbidden_chars = "@%";
    # Don't reuse the same password as currently
    $pwd_no_reuse = true;
    # Check that password is different than login
    $pwd_diff_login = true;
    # Check new passwords differs from old one - minimum characters count
    $pwd_diff_last_min_chars = 0;
    # Forbidden words which must not appear in the password
    $pwd_forbidden_words = array();
    # Forbidden ldap fields
    # Respective values of the user's entry must not appear in the password
    # example: $pwd_forbidden_ldap_fields = array('cn', 'givenName', 'sn', 'mail');
    $pwd_forbidden_ldap_fields = array();
    # Complexity: number of different class of character required
    $pwd_complexity = 0;
    # use pwnedpasswords api v2 to securely check if the password has been on a leak
    $use_pwnedpasswords = false;
    # Show policy constraints message:
    # always
    # never
    # onerror
    $pwd_show_policy = "never";
    # Position of password policy constraints message:
    # above - the form
    # below - the form
    $pwd_show_policy_pos = "above";

    # disallow use of the only special character as defined in `$pwd_special_chars` at the beginning and end
    $pwd_no_special_at_ends = false;

    # Who changes the password?
    # Also applicable for question/answer save
    # user: the user itself
    # manager: the above binddn
    $who_change_password = "user";

    # Show extended error message returned by LDAP directory when password is refused
    $show_extended_error = false;

    ## Standard change
    # Use standard change form?
    $use_change = true;

    ## SSH Key Change
    # Allow changing of sshPublicKey?
    $change_sshkey = false;

    # What attribute should be changed by the changesshkey action?
    $change_sshkey_attribute = "sshPublicKey";

    # Ensure the SSH Key submitted uses a type we trust
    $ssh_valid_key_types = array('ssh-rsa', 'ssh-dss', 'ecdsa-sha2-nistp256', 'ecdsa-sha2-nistp384', 'ecdsa-sha2-nistp521', 'ssh-ed25519');

    # Who changes the sshPublicKey attribute?
    # Also applicable for question/answer save
    # user: the user itself
    # manager: the above binddn
    $who_change_sshkey = "user";

    # Notify users anytime their sshPublicKey is changed
    ## Requires mail configuration below
    $notify_on_sshkey_change = false;

    ## Questions/answers
    # Use questions/answers?
    $use_questions = false;
    # Allow to register more than one answer?
    $multiple_answers = false;
    # Store many answers in a single string attribute
    # (only used if $multiple_answers = true)
    $multiple_answers_one_str = false;

    # Answer attribute should be hidden to users!
    $answer_objectClass = "extensibleObject";
    $answer_attribute = "info";

    # Crypt answers inside the directory
    $crypt_answers = true;

    # Extra questions (built-in questions are in lang/$lang.inc.php)
    # Should the built-in questions be included?
    $questions_use_default = true;
    #$messages['questions']['ice'] = "What is your favorite ice cream flavor?";

    # How many questions must be answered.
    #  If = 1: legacy behavior
    #  If > 1:
    #    this many questions will be included in the page forms
    #    this many questions must be set at a time
    #    user must answer this many correctly to reset a password
    #    $multiple_answers must be true
    #    at least this many possible questions must be available (there are only 2 questions built-in)
    $questions_count = 1;

    # Should the user be able to select registered question(s) by entering only the login?
    $question_populate_enable = false;

    ## Token
    # Use tokens?
    # true (default)
    # false
    $use_tokens = true;
    # Crypt tokens?
    # true (default)
    # false
    $crypt_tokens = true;
    # Token lifetime in seconds
    $token_lifetime = "3600";

    ## Mail
    # LDAP mail attribute
    $mail_attributes = array( "mail", "gosaMailAlternateAddress", "proxyAddresses" );
    # Get mail address directly from LDAP (only first mail entry)
    # and hide mail input field
    # default = false
    $mail_address_use_ldap = false;
    # Who the email should come from
    $mail_from = "<EMAIL>";
    $mail_from_name = "自助密码服务";
    $mail_signature = "";
    # Notify users anytime their password is changed
    $notify_on_change = true;
    # PHPMailer configuration (see https://github.com/PHPMailer/PHPMailer)
    $mail_sendmailpath = '/usr/sbin/sendmail';
    $mail_protocol = 'smtp';
    $mail_smtp_debug = 0;
    $mail_debug_format = 'error_log';
    $mail_smtp_host = 'smtp.office365.com';
    $mail_smtp_auth = true;
    $mail_smtp_user = '<EMAIL>';
    $mail_smtp_pass = 'N6vKK4eQkwcwykyH7';
    $mail_smtp_port = 587;
    $mail_smtp_timeout = 30;
    $mail_smtp_keepalive = false;
    $mail_smtp_secure = 'ssl';
    $mail_smtp_autotls = false;
    $mail_smtp_options = array();
    $mail_contenttype = 'text/plain';
    $mail_wordwrap = 0;
    $mail_charset = 'utf-8';
    $mail_priority = 3;

    ## SMS
    # Use sms
    $use_sms = false;
    # SMS method (mail, api)
    $sms_method = "mail";
    $sms_api_lib = "lib/smsapi.inc.php";
    # GSM number attribute
    $sms_attribute = "mobile";
    # Partially hide number
    $sms_partially_hide_number = true;
    # Send SMS mail to address
    $smsmailto = "{sms_attribute}@service.provider.com";
    # Subject when sending email to SMTP to SMS provider
    $smsmail_subject = "Provider code";
    # Message
    $sms_message = "{smsresetmessage} {smstoken}";
    # Remove non digit characters from GSM number
    $sms_sanitize_number = false;
    # Truncate GSM number
    $sms_truncate_number = false;
    $sms_truncate_number_length = 10;
    # SMS token length
    $sms_token_length = 6;
    # Max attempts allowed for SMS token
    $max_attempts = 3;

    # Encryption, decryption keyphrase, required if $use_tokens = true and $crypt_tokens = true, or $use_sms, or $crypt_answer
    # Please change it to anything long, random and complicated, you do not have to remember it
    # Changing it will also invalidate all previous tokens and SMS codes
    $keyphrase = "B5xvVsvvRyrGv7i4";

    # Reset URL (if behind a reverse proxy)
    $reset_url = $_SERVER['HTTP_X_FORWARDED_PROTO'] . "://" . $_SERVER['HTTP_X_FORWARDED_HOST'] . $_SERVER['SCRIPT_NAME'];

    # Display help messages
    $show_help = true;

    # Default language
    $lang = "en";

    # List of authorized languages. If empty, all language are allowed.
    # If not empty and the user's browser language setting is not in that list, language from $lang will be used.
    $allowed_lang = array();

    # Display menu on top
    $show_menu = true;

    # Logo
    $logo = "images/ltb-logo.png";

    # Background image
    $background_image = "images/unsplash-space.jpeg";

    $custom_css = "";
    $display_footer = true;

    # Where to log password resets - Make sure apache has write permission
    # By default, they are logged in Apache log
    $reset_request_log = "/tmp/self-service-password";

    # Invalid characters in login
    # Set at least "*()&|" to prevent LDAP injection
    # If empty, only alphanumeric characters are accepted
    $login_forbidden_chars = "*()&|";