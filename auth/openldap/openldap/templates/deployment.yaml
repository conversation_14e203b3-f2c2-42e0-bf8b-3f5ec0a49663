apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "openldap.fullname" . }}
  labels:
    {{- include "openldap.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "openldap.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "openldap.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "openldap.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: LDAP_LOG_LEVEL
              value: "{{ .Values.extraEnvVars.LDAP_LOG_LEVEL }}"
            - name: LDAP_ORGANISATION
              value: "{{ .Values.extraEnvVars.LDAP_ORGANISATION }}"
            - name: LDAP_DOMAIN
              value: "{{ .Values.extraEnvVars.LDAP_DOMAIN }}"
            - name: LDAP_ADMIN_PASSWORD
              value: "{{ .Values.extraEnvVars.LDAP_ADMIN_PASSWORD }}"
            - name: LDAP_CONFIG_PASSWORD
              value: "{{ .Values.extraEnvVars.LDAP_CONFIG_PASSWORD }}"
            - name: LDAP_READONLY_USER
              value: "{{ .Values.extraEnvVars.LDAP_READONLY_USER }}"
            - name: LDAP_READONLY_USER_USERNAME
              value: "{{ .Values.extraEnvVars.LDAP_READONLY_USER_USERNAME }}"
            - name: LDAP_READONLY_USER_PASSWORD
              value: "{{ .Values.extraEnvVars.LDAP_READONLY_USER_PASSWORD}}"
            - name: LDAP_RFC2307BIS_SCHEMA
              value: "{{ .Values.extraEnvVars.LDAP_RFC2307BIS_SCHEMA }}"
            - name: LDAP_BACKEND
              value: "{{ .Values.extraEnvVars.LDAP_BACKEND }}"
            - name: LDAP_TLS
              value: "{{ .Values.extraEnvVars.LDAP_TLS }}"
            - name: LDAP_TLS_CRT_FILENAME
              value: "{{ .Values.extraEnvVars.LDAP_TLS_CRT_FILENAME }}"
            - name: LDAP_TLS_KEY_FILENAME
              value: "{{ .Values.extraEnvVars.LDAP_TLS_KEY_FILENAME }}"
            - name: LDAP_TLS_DH_PARAM_FILENAME
              value: "{{ .Values.extraEnvVars.LDAP_TLS_DH_PARAM_FILENAME }}"
            - name: LDAP_TLS_CA_CRT_FILENAME
              value: "{{ .Values.extraEnvVars.LDAP_TLS_CA_CRT_FILENAME }}"
            - name: LDAP_TLS_ENFORCE
              value: "{{ .Values.extraEnvVars.LDAP_TLS_ENFORCE }}"
            - name: LDAP_TLS_CIPHER_SUITE
              value: {{ .Values.extraEnvVars.LDAP_TLS_CIPHER_SUITE | squote }}
            - name: LDAP_TLS_VERIFY_CLIENT
              value: "{{ .Values.extraEnvVars.LDAP_TLS_VERIFY_CLIENT }}"
            - name: LDAP_REPLICATION
              value: "{{ .Values.extraEnvVars.LDAP_REPLICATION }}"
            - name: LDAP_REPLICATION_CONFIG_SYNCPROV
              value: {{ .Values.extraEnvVars.LDAP_REPLICATION_CONFIG_SYNCPROV | squote }}
            - name: LDAP_REPLICATION_DB_SYNCPROV
              value: {{ .Values.extraEnvVars.LDAP_REPLICATION_DB_SYNCPROV | squote}}
            - name: LDAP_REPLICATION_HOSTS
              value: {{ .Values.extraEnvVars.LDAP_REPLICATION_HOSTS | quote }}
            - name: KEEP_EXISTING_CONFIG
              value: "{{ .Values.extraEnvVars.KEEP_EXISTING_CONFIG }}"
            - name: LDAP_REMOVE_CONFIG_AFTER_SETUP
              value: "{{ .Values.extraEnvVars.LDAP_REMOVE_CONFIG_AFTER_SETUP }}"
            - name: LDAP_SSL_HELPER_PREFIX
              value: "{{ .Values.extraEnvVars.LDAP_SSL_HELPER_PREFIX }}"
            - name: TZ
              value: "{{ .Values.extraEnvVars.TZ }}"
          volumeMounts:
            - name: data
              mountPath: /var/lib/ldap
              subPath: data
            - name: data
              mountPath: /etc/ldap/slapd.d
              subPath: config
            - name: data
              mountPath: /container/service/slapd/assets/certs
              subPath: certs
          ports:
            - name: ldap-port
              containerPort: 389
              protocol: TCP
            - name: ssl-ldap-port
              containerPort: 636
              protocol: TCP
          livenessProbe:
            tcpSocket:
              port: ldap-port
            initialDelaySeconds: 20
            timeoutSeconds: 5
            periodSeconds: 120
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            tcpSocket:
              port: ldap-port
            initialDelaySeconds: 20
            timeoutSeconds: 5
            periodSeconds: 120
            successThreshold: 1
            failureThreshold: 3
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      volumes:
        - name: data
          {{- if .Values.persistence.enabled }}
          persistentVolumeClaim:
            claimName: {{ .Values.persistence.existingClaim | default (include "openldap.fullname" .) }}
          {{- else }}
          emptyDir: {}
          {{- end -}}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
