apiVersion: v1
kind: Service
metadata:
  name: {{ include "openldap.fullname" . }}
  labels:
    {{- include "openldap.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: 389
      protocol: TCP
      name: ldap-port
    - port: {{ .Values.service.sslLdapPort }}
      targetPort: ssl-ldap-port
      protocol: TCP
      name: ssl-ldap-port
  selector:
    {{- include "openldap.selectorLabels" . | nindent 4 }}
