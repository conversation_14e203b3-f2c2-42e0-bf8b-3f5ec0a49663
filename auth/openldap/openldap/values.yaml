# Default values for openldap.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: osixia/openldap
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "1.5.0"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

extraEnvVars:
  LDAP_LOG_LEVEL: "256"
  LDAP_ORGANISATION: "hakInc"
  LDAP_DOMAIN: "hak3.today"
  LDAP_ADMIN_PASSWORD: "T9zGIovJYJtQMnB"
  LDAP_CONFIG_PASSWORD: "QchmOcDqxx"
  LDAP_READONLY_USER: "false"
  LDAP_READONLY_USER_USERNAME: "readonly"
  LDAP_READONLY_USER_PASSWORD: "readonly"
  LDAP_RFC2307BIS_SCHEMA: "false"
  LDAP_BACKEND: "mdb"
  LDAP_TLS: "true"
  LDAP_TLS_CRT_FILENAME: "ldap.crt"
  LDAP_TLS_KEY_FILENAME: "ldap.key"
  LDAP_TLS_DH_PARAM_FILENAME: "dhparam.pem"
  LDAP_TLS_CA_CRT_FILENAME: "ca.crt"
  LDAP_TLS_ENFORCE: "false"
  LDAP_TLS_CIPHER_SUITE: 'SECURE256:+SECURE128:-VERS-TLS-ALL:+VERS-TLS1.2:-RSA:-DHE-DSS:-CAMELLIA-128-CBC:-CAMELLIA-256-CBC'
  LDAP_TLS_VERIFY_CLIENT: "demand"
  LDAP_REPLICATION: "false"
  LDAP_REPLICATION_CONFIG_SYNCPROV: 'binddn=\"cn=admin,cn=config\" bindmethod=simple credentials=$LDAP_CONFIG_PASSWORD searchbase=\"cn=config\" type=refreshAndPersist retry=\"60 +\" timeout=1 starttls=critical'
  LDAP_REPLICATION_DB_SYNCPROV: 'binddn=\"cn=admin,$LDAP_BASE_DN\" bindmethod=simple credentials=$LDAP_ADMIN_PASSWORD searchbase=\"$LDAP_BASE_DN\" type=refreshAndPersist interval=00:00:00:10 retry=\"60 +\" timeout=1 starttls=critical'
  LDAP_REPLICATION_HOSTS: "#PYTHON2BASH:['ldap://ldap-one-service', 'ldap://ldap-two-service']"
  KEEP_EXISTING_CONFIG: "false"
  LDAP_REMOVE_CONFIG_AFTER_SETUP: "true"
  LDAP_SSL_HELPER_PREFIX: "ldap"
  TZ: "Asia/Shanghai"

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

## Persist data to a persistent volume
persistence:
  enabled: true
  ## database data Persistent Volume Storage Class
  ## If defined, storageClassName: <storageClass>
  ## If set to "-", storageClassName: "", which disables dynamic provisioning
  ## If undefined (the default) or set to null, no storageClassName spec is
  ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
  ##   GKE, AWS & OpenStack)
  ##
  storageClass: "local-path"
  accessMode: ReadWriteOnce
  size: 8Gi
  annotations: {}

service:
  type: LoadBalancer
  port: 389
  sslLdapPort: 636

ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 500m
    memory: 1024Mi
  requests:
    cpu: 500m
    memory: 512Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

self-service-password:
  enabled: true

phpldapadmin:
  enabled: true
