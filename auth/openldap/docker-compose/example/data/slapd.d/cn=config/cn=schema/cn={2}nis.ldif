# AUTO-GENERATED FILE - DO NOT EDIT!! Use ldapmodify.
# CRC32 5458c9be
dn: cn={2}nis
objectClass: olcSchemaConfig
cn: {2}nis
olcAttributeTypes: {0}( *******.1.1.1.2 NAME 'gecos' DESC 'The GECOS field; 
 the common name' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5Substrings
 Match SYNTAX *******.4.1.146***********.26 SINGLE-VALUE )
olcAttributeTypes: {1}( *******.1.1.1.3 NAME 'homeDirectory' DESC 'The absol
 ute path to the home directory' EQUALITY caseExactIA5Match SYNTAX *******.4
 .1.146***********.26 SINGLE-VALUE )
olcAttributeTypes: {2}( *******.1.1.1.4 NAME 'loginShell' DESC 'The path to 
 the login shell' EQUALITY caseExactIA5Match SYNTAX *******.4.1.1466.115.121
 .1.26 SINGLE-VALUE )
olcAttributeTypes: {3}( *******.1.1.1.5 NAME 'shadowLastChange' EQUALITY int
 egerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {4}( *******.1.1.1.6 NAME 'shadowMin' EQUALITY integerMat
 ch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {5}( *******.1.1.1.7 NAME 'shadowMax' EQUALITY integerMat
 ch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {6}( *******.1.1.1.8 NAME 'shadowWarning' EQUALITY intege
 rMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {7}( *******.1.1.1.9 NAME 'shadowInactive' EQUALITY integ
 erMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {8}( *******.1.1.1.10 NAME 'shadowExpire' EQUALITY intege
 rMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {9}( *******.******** NAME 'shadowFlag' EQUALITY integerM
 atch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {10}( *******.******** NAME 'memberUid' EQUALITY caseExac
 tIA5Match SUBSTR caseExactIA5SubstringsMatch SYNTAX *******.4.1.1466.115.12
 1.1.26 )
olcAttributeTypes: {11}( *******.******** NAME 'memberNisNetgroup' EQUALITY 
 caseExactIA5Match SUBSTR caseExactIA5SubstringsMatch SYNTAX *******.4.1.146
 ***********.26 )
olcAttributeTypes: {12}( *******.******** NAME 'nisNetgroupTriple' DESC 'Net
 group triple' SYNTAX *******.******* )
olcAttributeTypes: {13}( *******.******** NAME 'ipServicePort' EQUALITY inte
 gerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {14}( *******.******** NAME 'ipServiceProtocol' SUP name 
 )
olcAttributeTypes: {15}( *******.******** NAME 'ipProtocolNumber' EQUALITY i
 ntegerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {16}( *******.******** NAME 'oncRpcNumber' EQUALITY integ
 erMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {17}( *******.******** NAME 'ipHostNumber' DESC 'IP addre
 ss' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.146***********.26{128} )
olcAttributeTypes: {18}( *******.******** NAME 'ipNetworkNumber' DESC 'IP ne
 twork' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.146***********.26{128
 } SINGLE-VALUE )
olcAttributeTypes: {19}( *******.******** NAME 'ipNetmaskNumber' DESC 'IP ne
 tmask' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.146***********.26{128
 } SINGLE-VALUE )
olcAttributeTypes: {20}( *******.******** NAME 'macAddress' DESC 'MAC addres
 s' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.146***********.26{128} )
olcAttributeTypes: {21}( *******.******** NAME 'bootParameter' DESC 'rpc.boo
 tparamd parameter' SYNTAX *******.******* )
olcAttributeTypes: {22}( *******.******** NAME 'bootFile' DESC 'Boot image n
 ame' EQUALITY caseExactIA5Match SYNTAX *******.4.1.146***********.26 )
olcAttributeTypes: {23}( *******.******** NAME 'nisMapName' SUP name )
olcAttributeTypes: {24}( *******.******** NAME 'nisMapEntry' EQUALITY caseEx
 actIA5Match SUBSTR caseExactIA5SubstringsMatch SYNTAX *******.4.1.1466.115.
 121.1.26{1024} SINGLE-VALUE )
olcObjectClasses: {0}( *******.******* NAME 'posixAccount' DESC 'Abstraction
  of an account with POSIX attributes' SUP top AUXILIARY MUST ( cn $ uid $ u
 idNumber $ gidNumber $ homeDirectory ) MAY ( userPassword $ loginShell $ ge
 cos $ description ) )
olcObjectClasses: {1}( *******.******* NAME 'shadowAccount' DESC 'Additional
  attributes for shadow passwords' SUP top AUXILIARY MUST uid MAY ( userPass
 word $ shadowLastChange $ shadowMin $ shadowMax $ shadowWarning $ shadowIna
 ctive $ shadowExpire $ shadowFlag $ description ) )
olcObjectClasses: {2}( *******.******* NAME 'posixGroup' DESC 'Abstraction o
 f a group of accounts' SUP top STRUCTURAL MUST ( cn $ gidNumber ) MAY ( use
 rPassword $ memberUid $ description ) )
olcObjectClasses: {3}( *******.******* NAME 'ipService' DESC 'Abstraction an
  Internet Protocol service' SUP top STRUCTURAL MUST ( cn $ ipServicePort $ 
 ipServiceProtocol ) MAY description )
olcObjectClasses: {4}( *******.******* NAME 'ipProtocol' DESC 'Abstraction o
 f an IP protocol' SUP top STRUCTURAL MUST ( cn $ ipProtocolNumber $ descrip
 tion ) MAY description )
olcObjectClasses: {5}( *******.******* NAME 'oncRpc' DESC 'Abstraction of an
  ONC/RPC binding' SUP top STRUCTURAL MUST ( cn $ oncRpcNumber $ description
  ) MAY description )
olcObjectClasses: {6}( *******.******* NAME 'ipHost' DESC 'Abstraction of a 
 host, an IP device' SUP top AUXILIARY MUST ( cn $ ipHostNumber ) MAY ( l $ 
 description $ manager ) )
olcObjectClasses: {7}( *******.******* NAME 'ipNetwork' DESC 'Abstraction of
  an IP network' SUP top STRUCTURAL MUST ( cn $ ipNetworkNumber ) MAY ( ipNe
 tmaskNumber $ l $ description $ manager ) )
olcObjectClasses: {8}( *******.******* NAME 'nisNetgroup' DESC 'Abstraction 
 of a netgroup' SUP top STRUCTURAL MUST cn MAY ( nisNetgroupTriple $ memberN
 isNetgroup $ description ) )
olcObjectClasses: {9}( *******.******* NAME 'nisMap' DESC 'A generic abstrac
 tion of a NIS map' SUP top STRUCTURAL MUST nisMapName MAY description )
olcObjectClasses: {10}( *******.*******0 NAME 'nisObject' DESC 'An entry in 
 a NIS map' SUP top STRUCTURAL MUST ( cn $ nisMapEntry $ nisMapName ) MAY de
 scription )
olcObjectClasses: {11}( *******.*******1 NAME 'ieee802Device' DESC 'A device
  with a MAC address' SUP top AUXILIARY MAY macAddress )
olcObjectClasses: {12}( *******.******** NAME 'bootableDevice' DESC 'A devic
 e with boot parameters' SUP top AUXILIARY MAY ( bootFile $ bootParameter ) 
 )
structuralObjectClass: olcSchemaConfig
entryUUID: 9c711824-21b2-103c-9a22-c79c7a96c924
creatorsName: cn=admin,cn=config
createTimestamp: 20220214072229Z
entryCSN: 20220214072229.019207Z#000000#000#000000
modifiersName: cn=admin,cn=config
modifyTimestamp: 20220214072229Z
