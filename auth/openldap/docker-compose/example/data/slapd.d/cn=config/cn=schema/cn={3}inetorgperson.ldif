# AUTO-GENERATED FILE - DO NOT EDIT!! Use ldapmodify.
# CRC32 2b3fea76
dn: cn={3}inetorgperson
objectClass: olcSchemaConfig
cn: {3}inetorgperson
olcAttributeTypes: {0}( 2.16.840.1.113730.3.1.1 NAME 'carLicense' DESC 'RFC2
 798: vehicle license or registration plate' EQUALITY caseIgnoreMatch SUBSTR
  caseIgnoreSubstringsMatch SYNTAX 1.3.6.1.4.1.1466.115.121.1.15 )
olcAttributeTypes: {1}( 2.16.840.1.113730.3.1.2 NAME 'departmentNumber' DESC
  'RFC2798: identifies a department within an organization' EQUALITY caseIgn
 oreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX 1.3.6.1.4.1.1466.115.121.1
 .15 )
olcAttributeTypes: {2}( 2.16.840.1.113730.3.1.241 NAME 'displayName' DESC 'R
 FC2798: preferred name to be used when displaying entries' EQUALITY caseIgn
 oreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX 1.3.6.1.4.1.1466.115.121.1
 .15 SINGLE-VALUE )
olcAttributeTypes: {3}( 2.16.840.1.113730.3.1.3 NAME 'employeeNumber' DESC '
 RFC2798: numerically identifies an employee within an organization' EQUALIT
 Y caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX 1.3.6.1.4.1.1466.
 115.121.1.15 SINGLE-VALUE )
olcAttributeTypes: {4}( 2.16.840.1.113730.3.1.4 NAME 'employeeType' DESC 'RF
 C2798: type of employment for a person' EQUALITY caseIgnoreMatch SUBSTR cas
 eIgnoreSubstringsMatch SYNTAX 1.3.6.1.4.1.1466.115.121.1.15 )
olcAttributeTypes: {5}( 0.9.2342.19200300.100.1.60 NAME 'jpegPhoto' DESC 'RF
 C2798: a JPEG image' SYNTAX 1.3.6.1.4.1.1466.115.121.1.28 )
olcAttributeTypes: {6}( 2.16.840.1.113730.3.1.39 NAME 'preferredLanguage' DE
 SC 'RFC2798: preferred written or spoken language for a person' EQUALITY ca
 seIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX 1.3.6.1.4.1.1466.115.
 121.1.15 SINGLE-VALUE )
olcAttributeTypes: {7}( 2.16.840.1.113730.3.1.40 NAME 'userSMIMECertificate'
  DESC 'RFC2798: PKCS#7 SignedData used to support S/MIME' SYNTAX 1.3.6.1.4.
 1.1466.115.121.1.5 )
olcAttributeTypes: {8}( 2.16.840.1.113730.3.1.216 NAME 'userPKCS12' DESC 'RF
 C2798: personal identity information, a PKCS #12 PFX' SYNTAX 1.3.6.1.4.1.14
 66.115.121.1.5 )
olcObjectClasses: {0}( 2.16.840.1.113730.3.2.2 NAME 'inetOrgPerson' DESC 'RF
 C2798: Internet Organizational Person' SUP organizationalPerson STRUCTURAL 
 MAY ( audio $ businessCategory $ carLicense $ departmentNumber $ displayNam
 e $ employeeNumber $ employeeType $ givenName $ homePhone $ homePostalAddre
 ss $ initials $ jpegPhoto $ labeledURI $ mail $ manager $ mobile $ o $ page
 r $ photo $ roomNumber $ secretary $ uid $ userCertificate $ x500uniqueIden
 tifier $ preferredLanguage $ userSMIMECertificate $ userPKCS12 ) )
structuralObjectClass: olcSchemaConfig
entryUUID: 9c71254e-21b2-103c-9a23-c79c7a96c924
creatorsName: cn=admin,cn=config
createTimestamp: 20220214072229Z
entryCSN: 20220214072229.019545Z#000000#000#000000
modifiersName: cn=admin,cn=config
modifyTimestamp: 20220214072229Z
