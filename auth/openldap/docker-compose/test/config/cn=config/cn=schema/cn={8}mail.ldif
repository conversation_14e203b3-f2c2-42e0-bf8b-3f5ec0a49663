# AUTO-GENERATED FILE - DO NOT EDIT!! Use ldapmodify.
# CRC32 2acb9a1d
dn: cn={8}mail
objectClass: olcSchemaConfig
cn: {8}mail
olcAttributeTypes: {0}( *******.4.1.21103.******** NAME 'maildrop' DESC 'Mail 
 addresses where mails are forwarded -- ie forwards' EQUALITY caseIgnoreMatch 
 SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************{512} )
olcAttributeTypes: {1}( *******.4.1.21103.******** NAME 'mailalias' DESC 'Mail
  addresses accepted by this account -- ie aliases' EQUALITY caseIgnoreMatch S
 UBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************{512} )
olcAttributeTypes: {2}( *******.4.1.21103.******** NAME 'mailenable' DESC 'Mai
 l Account / Virtual alias validity' EQUALITY caseIgnoreMatch SUBSTR caseIgnor
 eSubstringsMatch SYNTAX *******.4.1.1466.************{8} )
olcAttributeTypes: {3}( *******.4.1.21103.******** NAME 'mailbox' DESC 'Mailbo
 x path where mails are delivered' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreS
 ubstringsMatch SYNTAX *******.4.1.1466.************{512} )
olcAttributeTypes: {4}( *******.4.1.21103.******** NAME 'virtualdomain' DESC '
 A mail domain name' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch
  SYNTAX *******.4.1.1466.************{512} )
olcAttributeTypes: {5}( *******.4.1.21103.******** NAME 'virtualdomaindescript
 ion' DESC 'Virtual domain description' EQUALITY caseIgnoreMatch SUBSTR caseIg
 noreSubstringsMatch SYNTAX *******.4.1.1466.************{512} )
olcAttributeTypes: {6}( *******.4.1.21103.******** NAME 'mailuserquota' DESC '
 Mailbox quota for a user in kilo-bytes' EQUALITY integerMatch SYNTAX *******.
 4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {7}( *******.4.1.21103.******** NAME 'mailhost' DESC 'The m
 ail server IP address or FQDN for a user' EQUALITY caseIgnoreIA5Match SUBSTR 
 caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************{256} SINGLE
 -VALUE )
olcAttributeTypes: {8}( *******.4.1.21103.******** NAME 'mailaliasmember' DESC
  'Member of a virtual alias' SUP distinguishedName )
olcAttributeTypes: {9}( *******.4.1.21103.********0 NAME 'mailproxy' DESC 'Mai
 l proxy' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNT
 AX *******.4.1.1466.************{256} SINGLE-VALUE )
olcAttributeTypes: {10}( *******.4.1.21103.********1 NAME 'mailhidden' DESC 'M
 ail Account hidden in address book' EQUALITY caseIgnoreMatch SUBSTR caseIgnor
 eSubstringsMatch SYNTAX *******.4.1.1466.************{8} )
olcObjectClasses: {0}( *******.4.1.21103.******** NAME 'mailAccount' DESC 'Mai
 l Account' SUP top AUXILIARY MUST mail MAY ( mailalias $ maildrop $ mailenabl
 e $ mailbox $ mailuserquota $ mailhost $ mailproxy $ mailhidden ) )
olcObjectClasses: {1}( *******.4.1.21103.******** NAME 'mailDomain' DESC 'Doma
 in mail entry' SUP top STRUCTURAL MUST virtualdomain MAY ( virtualdomaindescr
 iption $ mailuserquota ) )
olcObjectClasses: {2}( *******.4.1.21103.******** NAME 'mailGroup' DESC 'Mail 
 Group' SUP top AUXILIARY MUST mail MAY mailhidden )
olcObjectClasses: {3}( *******.4.1.21103.******** NAME 'mailAlias' DESC 'Mail 
 Alias' SUP top STRUCTURAL MUST mailalias MAY ( mail $ mailaliasmember $ maile
 nable ) )
structuralObjectClass: olcSchemaConfig
entryUUID: 1e464bcc-8c54-1034-9813-2f3e74c83a84
creatorsName: gidNumber=0+uidNumber=0,cn=peercred,cn=external,cn=auth
createTimestamp: **************Z
entryCSN: **************.701898Z#000000#000#000000
modifiersName: gidNumber=0+uidNumber=0,cn=peercred,cn=external,cn=auth
modifyTimestamp: **************Z
