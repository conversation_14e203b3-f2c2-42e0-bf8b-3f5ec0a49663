# AUTO-GENERATED FILE - DO NOT EDIT!! Use ldapmodify.
# CRC32 5b59ad61
dn: cn={0}core
objectClass: olcSchemaConfig
cn: {0}core
olcAttributeTypes: {0}( 2.5.4.2 NAME 'knowledgeInformation' DESC 'RFC2256: kno
 wledge information' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.115.121.
 1.15{32768} )
olcAttributeTypes: {1}( 2.5.4.4 NAME ( 'sn' 'surname' ) DESC 'RFC2256: last (f
 amily) name(s) for which the entity is known by' SUP name )
olcAttributeTypes: {2}( 2.5.4.5 NAME 'serialNumber' DESC 'RFC2256: serial numb
 er of the entity' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch S
 YNTAX *******.4.1.1466.************{64} )
olcAttributeTypes: {3}( ******* NAME ( 'c' 'countryName' ) DESC 'RFC2256: ISO-
 3166 country 2-letter code' SUP name SINGLE-VALUE )
olcAttributeTypes: {4}( ******* NAME ( 'l' 'localityName' ) DESC 'RFC2256: loc
 ality which this object resides in' SUP name )
olcAttributeTypes: {5}( ******* NAME ( 'st' 'stateOrProvinceName' ) DESC 'RFC2
 256: state or province which this object resides in' SUP name )
olcAttributeTypes: {6}( ******* NAME ( 'street' 'streetAddress' ) DESC 'RFC225
 6: street address of this object' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreS
 ubstringsMatch SYNTAX *******.4.1.1466.************{128} )
olcAttributeTypes: {7}( ******** NAME ( 'o' 'organizationName' ) DESC 'RFC2256
 : organization this object belongs to' SUP name )
olcAttributeTypes: {8}( ******** NAME ( 'ou' 'organizationalUnitName' ) DESC '
 RFC2256: organizational unit this object belongs to' SUP name )
olcAttributeTypes: {9}( ******** NAME 'title' DESC 'RFC2256: title associated 
 with the entity' SUP name )
olcAttributeTypes: {10}( ******** NAME 'searchGuide' DESC 'RFC2256: search gui
 de, deprecated by enhancedSearchGuide' SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {11}( ******** NAME 'businessCategory' DESC 'RFC2256: busin
 ess category' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTA
 X *******.4.1.1466.************{128} )
olcAttributeTypes: {12}( ******** NAME 'postalAddress' DESC 'RFC2256: postal a
 ddress' EQUALITY caseIgnoreListMatch SUBSTR caseIgnoreListSubstringsMatch SYN
 TAX *******.4.1.1466.************ )
olcAttributeTypes: {13}( ******** NAME 'postalCode' DESC 'RFC2256: postal code
 ' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.
 1.1466.************{40} )
olcAttributeTypes: {14}( ******** NAME 'postOfficeBox' DESC 'RFC2256: Post Off
 ice Box' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX 1.3
 .*******.1466.************{40} )
olcAttributeTypes: {15}( ******** NAME 'physicalDeliveryOfficeName' DESC 'RFC2
 256: Physical Delivery Office Name' EQUALITY caseIgnoreMatch SUBSTR caseIgnor
 eSubstringsMatch SYNTAX *******.4.1.1466.************{128} )
olcAttributeTypes: {16}( ******** NAME 'telephoneNumber' DESC 'RFC2256: Teleph
 one Number' EQUALITY telephoneNumberMatch SUBSTR telephoneNumberSubstringsMat
 ch SYNTAX *******.4.1.1466.************{32} )
olcAttributeTypes: {17}( ******** NAME 'telexNumber' DESC 'RFC2256: Telex Numb
 er' SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {18}( ******** NAME 'teletexTerminalIdentifier' DESC 'RFC22
 56: Teletex Terminal Identifier' SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {19}( ******** NAME ( 'facsimileTelephoneNumber' 'fax' ) DE
 SC 'RFC2256: Facsimile (Fax) Telephone Number' SYNTAX *******.4.1.1466.115.12
 1.1.22 )
olcAttributeTypes: {20}( ******** NAME 'x121Address' DESC 'RFC2256: X.121 Addr
 ess' EQUALITY numericStringMatch SUBSTR numericStringSubstringsMatch SYNTAX 1
 .*******.1.1466.************{15} )
olcAttributeTypes: {21}( ******** NAME 'internationaliSDNNumber' DESC 'RFC2256
 : international ISDN number' EQUALITY numericStringMatch SUBSTR numericString
 SubstringsMatch SYNTAX *******.4.1.1466.************{16} )
olcAttributeTypes: {22}( ******** NAME 'registeredAddress' DESC 'RFC2256: regi
 stered postal address' SUP postalAddress SYNTAX *******.4.1.1466.************
  )
olcAttributeTypes: {23}( ******** NAME 'destinationIndicator' DESC 'RFC2256: d
 estination indicator' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMat
 ch SYNTAX *******.4.1.1466.************{128} )
olcAttributeTypes: {24}( ******** NAME 'preferredDeliveryMethod' DESC 'RFC2256
 : preferred delivery method' SYNTAX *******.4.1.1466.************ SINGLE-VALU
 E )
olcAttributeTypes: {25}( ******** NAME 'presentationAddress' DESC 'RFC2256: pr
 esentation address' EQUALITY presentationAddressMatch SYNTAX *******.4.1.1466
 .************ SINGLE-VALUE )
olcAttributeTypes: {26}( ******** NAME 'supportedApplicationContext' DESC 'RFC
 2256: supported application context' EQUALITY objectIdentifierMatch SYNTAX 1.
 *******.1.1466.************ )
olcAttributeTypes: {27}( ******** NAME 'member' DESC 'RFC2256: member of a gro
 up' SUP distinguishedName )
olcAttributeTypes: {28}( ******** NAME 'owner' DESC 'RFC2256: owner (of the ob
 ject)' SUP distinguishedName )
olcAttributeTypes: {29}( ******** NAME 'roleOccupant' DESC 'RFC2256: occupant 
 of role' SUP distinguishedName )
olcAttributeTypes: {30}( ******** NAME 'userCertificate' DESC 'RFC2256: X.509 
 user certificate, use ;binary' EQUALITY certificateExactMatch SYNTAX *******.
 4.1.1466.*********** )
olcAttributeTypes: {31}( ******** NAME 'cACertificate' DESC 'RFC2256: X.509 CA
  certificate, use ;binary' EQUALITY certificateExactMatch SYNTAX *******.4.1.
 1466.*********** )
olcAttributeTypes: {32}( ******** NAME 'authorityRevocationList' DESC 'RFC2256
 : X.509 authority revocation list, use ;binary' SYNTAX *******.4.1.1466.115.1
 21.1.9 )
olcAttributeTypes: {33}( 2.5.4.39 NAME 'certificateRevocationList' DESC 'RFC22
 56: X.509 certificate revocation list, use ;binary' SYNTAX *******.4.1.1466.1
 15.121.1.9 )
olcAttributeTypes: {34}( 2.5.4.40 NAME 'crossCertificatePair' DESC 'RFC2256: X
 .509 cross certificate pair, use ;binary' SYNTAX *******.4.1.1466.115.121.1.1
 0 )
olcAttributeTypes: {35}( 2.5.4.42 NAME ( 'givenName' 'gn' ) DESC 'RFC2256: fir
 st name(s) for which the entity is known by' SUP name )
olcAttributeTypes: {36}( 2.5.4.43 NAME 'initials' DESC 'RFC2256: initials of s
 ome or all of names, but not the surname(s).' SUP name )
olcAttributeTypes: {37}( 2.5.4.44 NAME 'generationQualifier' DESC 'RFC2256: na
 me qualifier indicating a generation' SUP name )
olcAttributeTypes: {38}( 2.5.4.45 NAME 'x500UniqueIdentifier' DESC 'RFC2256: X
 .500 unique identifier' EQUALITY bitStringMatch SYNTAX *******.4.1.1466.115.1
 21.1.6 )
olcAttributeTypes: {39}( 2.5.4.46 NAME 'dnQualifier' DESC 'RFC2256: DN qualifi
 er' EQUALITY caseIgnoreMatch ORDERING caseIgnoreOrderingMatch SUBSTR caseIgno
 reSubstringsMatch SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {40}( 2.5.4.47 NAME 'enhancedSearchGuide' DESC 'RFC2256: en
 hanced search guide' SYNTAX *******.4.1.1466.115.121.1.21 )
olcAttributeTypes: {41}( 2.5.4.48 NAME 'protocolInformation' DESC 'RFC2256: pr
 otocol information' EQUALITY protocolInformationMatch SYNTAX *******.4.1.1466
 .115.121.1.42 )
olcAttributeTypes: {42}( 2.5.4.50 NAME 'uniqueMember' DESC 'RFC2256: unique me
 mber of a group' EQUALITY uniqueMemberMatch SYNTAX *******.4.1.1466.115.121.1
 .34 )
olcAttributeTypes: {43}( 2.5.4.51 NAME 'houseIdentifier' DESC 'RFC2256: house 
 identifier' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX 
 *******.4.1.1466.************{32768} )
olcAttributeTypes: {44}( 2.5.4.52 NAME 'supportedAlgorithms' DESC 'RFC2256: su
 pported algorithms' SYNTAX *******.4.1.1466.115.121.1.49 )
olcAttributeTypes: {45}( 2.5.4.53 NAME 'deltaRevocationList' DESC 'RFC2256: de
 lta revocation list; use ;binary' SYNTAX *******.4.1.1466.115.121.1.9 )
olcAttributeTypes: {46}( 2.5.4.54 NAME 'dmdName' DESC 'RFC2256: name of DMD' S
 UP name )
olcAttributeTypes: {47}( *******5 NAME 'pseudonym' DESC 'X.520(4th): pseudonym
  for the object' SUP name )
olcAttributeTypes: {48}( 0.9.2342.19200300.100.1.3 NAME ( 'mail' 'rfc822Mailbo
 x' ) DESC 'RFC1274: RFC822 Mailbox'   EQUALITY caseIgnoreIA5Match   SUBSTR ca
 seIgnoreIA5SubstringsMatch   SYNTAX *******.4.1.1466.************{256} )
olcAttributeTypes: {49}( 0.9.2342.19200300.100.1.25 NAME ( 'dc' 'domainCompone
 nt' ) DESC 'RFC1274/2247: domain component' EQUALITY caseIgnoreIA5Match SUBST
 R caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VA
 LUE )
olcAttributeTypes: {50}( 0.9.2342.19200300.100.1.37 NAME 'associatedDomain' DE
 SC 'RFC1274: domain associated with object' EQUALITY caseIgnoreIA5Match SUBST
 R caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {51}( 1.2.840.113549.1.9.1 NAME ( 'email' 'emailAddress' 'p
 kcs9email' ) DESC 'RFC3280: legacy attribute for email addresses in DNs' EQUA
 LITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.
 1.1466.************{128} )
olcObjectClasses: {0}( ******* NAME 'country' DESC 'RFC2256: a country' SUP to
 p STRUCTURAL MUST c MAY ( searchGuide $ description ) )
olcObjectClasses: {1}( ******* NAME 'locality' DESC 'RFC2256: a locality' SUP 
 top STRUCTURAL MAY ( street $ seeAlso $ searchGuide $ st $ l $ description ) 
 )
olcObjectClasses: {2}( ******* NAME 'organization' DESC 'RFC2256: an organizat
 ion' SUP top STRUCTURAL MUST o MAY ( userPassword $ searchGuide $ seeAlso $ b
 usinessCategory $ x121Address $ registeredAddress $ destinationIndicator $ pr
 eferredDeliveryMethod $ telexNumber $ teletexTerminalIdentifier $ telephoneNu
 mber $ internationaliSDNNumber $  facsimileTelephoneNumber $ street $ postOff
 iceBox $ postalCode $ postalAddress $ physicalDeliveryOfficeName $ st $ l $ d
 escription ) )
olcObjectClasses: {3}( ******* NAME 'organizationalUnit' DESC 'RFC2256: an org
 anizational unit' SUP top STRUCTURAL MUST ou MAY ( userPassword $ searchGuide
  $ seeAlso $ businessCategory $ x121Address $ registeredAddress $ destination
 Indicator $ preferredDeliveryMethod $ telexNumber $ teletexTerminalIdentifier
  $ telephoneNumber $ internationaliSDNNumber $ facsimileTelephoneNumber $ str
 eet $ postOfficeBox $ postalCode $ postalAddress $ physicalDeliveryOfficeName
  $ st $ l $ description ) )
olcObjectClasses: {4}( ******* NAME 'person' DESC 'RFC2256: a person' SUP top 
 STRUCTURAL MUST ( sn $ cn ) MAY ( userPassword $ telephoneNumber $ seeAlso $ 
 description ) )
olcObjectClasses: {5}( ******* NAME 'organizationalPerson' DESC 'RFC2256: an o
 rganizational person' SUP person STRUCTURAL MAY ( title $ x121Address $ regis
 teredAddress $ destinationIndicator $ preferredDeliveryMethod $ telexNumber $
  teletexTerminalIdentifier $ telephoneNumber $ internationaliSDNNumber $  fac
 simileTelephoneNumber $ street $ postOfficeBox $ postalCode $ postalAddress $
  physicalDeliveryOfficeName $ ou $ st $ l ) )
olcObjectClasses: {6}( ******* NAME 'organizationalRole' DESC 'RFC2256: an org
 anizational role' SUP top STRUCTURAL MUST cn MAY ( x121Address $ registeredAd
 dress $ destinationIndicator $ preferredDeliveryMethod $ telexNumber $ telete
 xTerminalIdentifier $ telephoneNumber $ internationaliSDNNumber $ facsimileTe
 lephoneNumber $ seeAlso $ roleOccupant $ preferredDeliveryMethod $ street $ p
 ostOfficeBox $ postalCode $ postalAddress $ physicalDeliveryOfficeName $ ou $
  st $ l $ description ) )
olcObjectClasses: {7}( ******* NAME 'groupOfNames' DESC 'RFC2256: a group of n
 ames (DNs)' SUP top STRUCTURAL MUST ( member $ cn ) MAY ( businessCategory $ 
 seeAlso $ owner $ ou $ o $ description ) )
olcObjectClasses: {8}( ******** NAME 'residentialPerson' DESC 'RFC2256: an res
 idential person' SUP person STRUCTURAL MUST l MAY ( businessCategory $ x121Ad
 dress $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ 
 telexNumber $ teletexTerminalIdentifier $ telephoneNumber $ internationaliSDN
 Number $ facsimileTelephoneNumber $ preferredDeliveryMethod $ street $ postOf
 ficeBox $ postalCode $ postalAddress $ physicalDeliveryOfficeName $ st $ l ) 
 )
olcObjectClasses: {9}( ******** NAME 'applicationProcess' DESC 'RFC2256: an ap
 plication process' SUP top STRUCTURAL MUST cn MAY ( seeAlso $ ou $ l $ descri
 ption ) )
olcObjectClasses: {10}( ******** NAME 'applicationEntity' DESC 'RFC2256: an ap
 plication entity' SUP top STRUCTURAL MUST ( presentationAddress $ cn ) MAY ( 
 supportedApplicationContext $ seeAlso $ ou $ o $ l $ description ) )
olcObjectClasses: {11}( ******** NAME 'dSA' DESC 'RFC2256: a directory system 
 agent (a server)' SUP applicationEntity STRUCTURAL MAY knowledgeInformation )
olcObjectClasses: {12}( ******** NAME 'device' DESC 'RFC2256: a device' SUP to
 p STRUCTURAL MUST cn MAY ( serialNumber $ seeAlso $ owner $ ou $ o $ l $ desc
 ription ) )
olcObjectClasses: {13}( ******** NAME 'strongAuthenticationUser' DESC 'RFC2256
 : a strong authentication user' SUP top AUXILIARY MUST userCertificate )
olcObjectClasses: {14}( ******** NAME 'certificationAuthority' DESC 'RFC2256: 
 a certificate authority' SUP top AUXILIARY MUST ( authorityRevocationList $ c
 ertificateRevocationList $ cACertificate ) MAY crossCertificatePair )
olcObjectClasses: {15}( ******** NAME 'groupOfUniqueNames' DESC 'RFC2256: a gr
 oup of unique names (DN and Unique Identifier)' SUP top STRUCTURAL MUST ( uni
 queMember $ cn ) MAY ( businessCategory $ seeAlso $ owner $ ou $ o $ descript
 ion ) )
olcObjectClasses: {16}( ******** NAME 'userSecurityInformation' DESC 'RFC2256:
  a user security information' SUP top AUXILIARY MAY ( supportedAlgorithms ) )
olcObjectClasses: {17}( ********.2 NAME 'certificationAuthority-V2' SUP certif
 icationAuthority AUXILIARY MAY ( deltaRevocationList ) )
olcObjectClasses: {18}( ******** NAME 'cRLDistributionPoint' SUP top STRUCTURA
 L MUST ( cn ) MAY ( certificateRevocationList $ authorityRevocationList $ del
 taRevocationList ) )
olcObjectClasses: {19}( ******** NAME 'dmd' SUP top STRUCTURAL MUST ( dmdName 
 ) MAY ( userPassword $ searchGuide $ seeAlso $ businessCategory $ x121Address
  $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ telex
 Number $ teletexTerminalIdentifier $ telephoneNumber $ internationaliSDNNumbe
 r $ facsimileTelephoneNumber $ street $ postOfficeBox $ postalCode $ postalAd
 dress $ physicalDeliveryOfficeName $ st $ l $ description ) )
olcObjectClasses: {20}( ******** NAME 'pkiUser' DESC 'RFC2587: a PKI user' SUP
  top AUXILIARY MAY userCertificate )
olcObjectClasses: {21}( ******** NAME 'pkiCA' DESC 'RFC2587: PKI certificate a
 uthority' SUP top AUXILIARY MAY ( authorityRevocationList $ certificateRevoca
 tionList $ cACertificate $ crossCertificatePair ) )
olcObjectClasses: {22}( ******** NAME 'deltaCRL' DESC 'RFC2587: PKI user' SUP 
 top AUXILIARY MAY deltaRevocationList )
olcObjectClasses: {23}( *******.*********.15 NAME 'labeledURIObject' DESC 'RFC
 2079: object that contains the URI attribute type' MAY ( labeledURI ) SUP top
  AUXILIARY )
olcObjectClasses: {24}( 0.9.2342.19200300.100.4.19 NAME 'simpleSecurityObject'
  DESC 'RFC1274: simple security object' SUP top AUXILIARY MUST userPassword )
olcObjectClasses: {25}( *******.4.1.1466.344 NAME 'dcObject' DESC 'RFC2247: do
 main component object' SUP top AUXILIARY MUST dc )
olcObjectClasses: {26}( *******.1.3.1 NAME 'uidObject' DESC 'RFC2377: uid obje
 ct' SUP top AUXILIARY MUST uid )
structuralObjectClass: olcSchemaConfig
entryUUID: 1dcfd1cc-8c54-1034-90f9-f5ce8ee3f703
creatorsName: cn=config
createTimestamp: 20150511180556Z
entryCSN: 20150511180556.925451Z#000000#000#000000
modifiersName: cn=config
modifyTimestamp: 20150511180556Z
