# AUTO-GENERATED FILE - DO NOT EDIT!! Use ldapmodify.
# CRC32 12b63e24
dn: cn={6}zarafa
objectClass: olcSchemaConfig
cn: {6}zarafa
olcAttributeTypes: {0}( *******.4.1.26278.1.1.1.1 NAME 'zarafaQuotaOverride' D
 ESC 'ZARAFA: Override child quota' EQUALITY integerMatch SYNTAX *******.4.1.1
 466.************ SINGLE-VALUE )
olcAttributeTypes: {1}( *******.4.1.26278.1.1.1.2 NAME 'zarafaQuotaWarn' DESC 
 'ZARAFA: Warning quota size in MB' EQUALITY integerMatch SYNTAX *******.4.1.1
 466.************ SINGLE-VALUE )
olcAttributeTypes: {2}( *******.4.1.26278.1.1.1.3 NAME 'zarafaQuotaSoft' DESC 
 'ZARAFA: Soft quota size in MB' EQUALITY integerMatch SYNTAX *******.4.1.1466
 .************ SINGLE-VALUE )
olcAttributeTypes: {3}( *******.4.1.26278.1.1.1.4 NAME 'zarafaQuotaHard' DESC 
 'ZARAFA: Hard quota size in MB' EQUALITY integerMatch SYNTAX *******.4.1.1466
 .************ SINGLE-VALUE )
olcAttributeTypes: {4}( *******.4.1.26278.1.1.1.5 NAME 'zarafaUserDefaultQuota
 Override' DESC 'ZARAFA: Override User default quota for children' EQUALITY in
 tegerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {5}( *******.4.1.26278.1.1.1.6 NAME 'zarafaUserDefaultQuota
 Warn' DESC 'ZARAFA: User default warning quota size in MB' EQUALITY integerMa
 tch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {6}( *******.4.1.26278.1.1.1.7 NAME 'zarafaUserDefaultQuota
 Soft' DESC 'ZARAFA: User default soft quota size in MB' EQUALITY integerMatch
  SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {7}( *******.4.1.26278.1.1.1.8 NAME 'zarafaUserDefaultQuota
 Hard' DESC 'ZARAFA: User default hard quota size in MB' EQUALITY integerMatch
  SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {8}( *******.4.1.26278.1.1.2.1 NAME 'zarafaAdmin' DESC 'ZAR
 AFA: Administrator of zarafa' EQUALITY integerMatch SYNTAX *******.4.1.1466.1
 15.121.1.27 SINGLE-VALUE )
olcAttributeTypes: {9}( *******.4.1.26278.1.1.2.2 NAME 'zarafaSharedStoreOnly'
  DESC 'ZARAFA: is store a shared store' EQUALITY integerMatch SYNTAX *******.
 4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {10}( *******.4.1.26278.1.1.2.3 NAME 'zarafaAccount' DESC '
 ZARAFA: entry is a part of zarafa' EQUALITY integerMatch SYNTAX *******.4.1.1
 466.************ SINGLE-VALUE )
olcAttributeTypes: {11}( *******.4.1.26278.1.1.2.4 NAME 'zarafaSendAsPrivilege
 ' DESC 'ZARAFA: Users may directly send email as this user' EQUALITY caseIgno
 reMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************
  )
olcAttributeTypes: {12}( *******.4.1.26278.1.1.2.5 NAME 'zarafaMrAccept' DESC 
 'ZARAFA: user should auto-accept meeting requests' EQUALITY integerMatch SYNT
 AX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {13}( *******.4.1.26278.1.1.2.6 NAME 'zarafaMrDeclineConfli
 ct' DESC 'ZARAFA: user should automatically decline conflicting meeting reque
 sts' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE 
 )
olcAttributeTypes: {14}( *******.4.1.26278.1.1.2.7 NAME 'zarafaMrDeclineRecurr
 ing' DESC 'ZARAFA: user should automatically decline recurring meeting reques
 ts' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {15}( *******.4.1.26278.******* NAME 'zarafaId' DESC 'ZARAF
 A: Generic unique ID' EQUALITY octetStringMatch SYNTAX *******.4.1.1466.115.1
 21.1.40 SINGLE-VALUE )
olcAttributeTypes: {16}( *******.4.1.26278.******* NAME 'zarafaResourceType' D
 ESC 'ZARAFA: for shared stores, resource is type Room or Equipment' EQUALITY 
 caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.115.
 121.1.15 SINGLE-VALUE )
olcAttributeTypes: {17}( *******.4.1.26278.******** NAME 'zarafaResourceCapaci
 ty' DESC 'ZARAFA: number of rooms or equipment available' EQUALITY integerMat
 ch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {18}( *******.4.1.26278.******** NAME 'zarafaHidden' DESC '
 ZARAFA: This object should be hidden from address book' EQUALITY integerMatch
  SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {19}( *******.4.1.26278.******* NAME 'zarafaAliases' DESC '
 ZARAFA: All other email addresses for this user' EQUALITY caseIgnoreMatch SUB
 STR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {20}( *******.4.1.26278.******* NAME 'zarafaUserServer' DES
 C 'ZARAFA: Home server for the user' EQUALITY caseIgnoreMatch SUBSTR caseIgno
 reSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {21}( *******.4.1.26278.******* NAME 'zarafaSecurityGroup' 
 DESC 'ZARAFA: group has security possibilities' EQUALITY integerMatch SYNTAX 
 *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {22}( *******.4.1.26278.******* NAME 'zarafaViewPrivilege' 
 DESC 'ZARAFA: Companies with view privileges over selected company' EQUALITY 
 caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.115.
 121.1.15 )
olcAttributeTypes: {23}( *******.4.1.26278.******* NAME 'zarafaAdminPrivilege'
  DESC 'ZARAFA: Users from different companies which are administrator over se
 lected company' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYN
 TAX *******.4.1.1466.************ )
olcAttributeTypes: {24}( *******.4.1.26278.******* NAME 'zarafaSystemAdmin' DE
 SC 'ZARAFA: The user who is the system administrator for this company' EQUALI
 TY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.1
 *********** SINGLE-VALUE )
olcAttributeTypes: {25}( *******.4.1.26278.******* NAME 'zarafaQuotaUserWarnin
 gRecipients' DESC 'ZARAFA: Users who will recieve a notification email when a
  user exceeds his quota' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstrings
 Match SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {26}( *******.4.1.26278.******* NAME 'zarafaQuotaCompanyWar
 ningRecipients' DESC 'ZARAFA: Users who will recieve a notification email whe
 n a company exceeds its quota' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubs
 tringsMatch SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {27}( *******.4.1.26278.******* NAME 'zarafaCompanyServer' 
 DESC 'ZARAFA: Home server for the user' EQUALITY caseIgnoreMatch SUBSTR caseI
 gnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {28}( *******.4.1.26278.******* NAME 'zarafaHttpPort' DESC 
 'ZARAFA: Port for the http connection' EQUALITY integerMatch SYNTAX *******.4
 .1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {29}( *******.4.1.26278.******* NAME 'zarafaSslPort' DESC '
 ZARAFA: Port for the ssl connection' EQUALITY integerMatch SYNTAX *******.4.1
 .1466.************ SINGLE-VALUE )
olcAttributeTypes: {30}( *******.4.1.26278.******* NAME 'zarafaFilePath' DESC 
 'ZARAFA: The unix socket or named pipe to the server' EQUALITY caseIgnoreMatc
 h SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGL
 E-VALUE )
olcAttributeTypes: {31}( *******.4.1.26278.******* NAME 'zarafaContainsPublic'
  DESC 'ZARAFA: This server contains the public store' EQUALITY integerMatch S
 YNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {32}( *******.4.1.26278.******* NAME 'zarafaFilter' DESC 'Z
 ARAFA: LDAP Filter to apply' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstr
 ingsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {33}( *******.4.1.26278.******* NAME 'zarafaBase' DESC 'ZAR
 AFA: LDAP Search base to apply' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSub
 stringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcObjectClasses: {0}( *******.4.1.26278.******* NAME 'zarafa-user' DESC 'Zara
 fa: an user of Zarafa' SUP top AUXILIARY MUST cn MAY ( zarafaQuotaOverride $ 
 zarafaQuotaWarn $ zarafaQuotaSoft $ zarafaSendAsPrivilege $ zarafaQuotaHard $
  zarafaAdmin $ zarafaSharedStoreOnly $ zarafaResourceType $ zarafaResourceCap
 acity $ zarafaAccount $ zarafaHidden $ zarafaAliases $ zarafaUserServer ) )
olcObjectClasses: {1}( *******.4.1.26278.1.6.0.0 NAME 'zarafa-contact' DESC 'Z
 arafa: a contact of Zarafa' SUP top AUXILIARY MUST ( cn $ uidNumber ) MAY ( z
 arafaSendAsPrivilege $ zarafaHidden $ zarafaAliases ) )
olcObjectClasses: {2}( *******.4.1.26278.1.2.0.0 NAME 'zarafa-group' DESC 'Zar
 afa: a group of Zarafa' SUP top AUXILIARY MUST cn MAY ( zarafaAccount $ zaraf
 aHidden $ mail $ zarafaAliases $ zarafaSecurityGroup ) )
olcObjectClasses: {3}( *******.4.1.26278.******* NAME 'zarafa-company' DESC 'Z
 ARAFA: a company of Zarafa' SUP top AUXILIARY MUST cn MAY ( zarafaAccount $ z
 arafaHidden $ zarafaViewPrivilege $ zarafaAdminPrivilege $ zarafaSystemAdmin 
 $ zarafaQuotaOverride $ zarafaQuotaWarn $ zarafaUserDefaultQuotaOverride $ za
 rafaUserDefaultQuotaWarn $ zarafaUserDefaultQuotaSoft $ zarafaUserDefaultQuot
 aHard $ zarafaQuotaUserWarningRecipients $ zarafaQuotaCompanyWarningRecipient
 s $ zarafaCompanyServer ) )
olcObjectClasses: {4}( *******.4.1.26278.******* NAME 'zarafa-server' DESC 'ZA
 RAFA: a Zarafa server' SUP top AUXILIARY MUST cn MAY ( zarafaAccount $ zarafa
 Hidden $ zarafaHttpPort $ zarafaSslPort $ zarafaFilePath $ zarafaContainsPubl
 ic ) )
olcObjectClasses: {5}( *******.4.1.26278.******* NAME 'zarafa-addresslist' DES
 C 'ZARAFA: a Zarafa Addresslist' SUP top STRUCTURAL MUST cn MAY ( zarafaAccou
 nt $ zarafaHidden $ zarafaFilter $ zarafaBase ) )
olcObjectClasses: {6}( *******.4.1.26278.******* NAME 'zarafa-dynamicgroup' DE
 SC 'ZARAFA: a Zarafa dynamic group' SUP top STRUCTURAL MUST cn MAY ( zarafaAc
 count $ zarafaHidden $ mail $ zarafaAliases $ zarafaFilter $ zarafaBase ) )
structuralObjectClass: olcSchemaConfig
entryUUID: 1e435106-8c54-1034-9811-2f3e74c83a84
creatorsName: gidNumber=0+uidNumber=0,cn=peercred,cn=external,cn=auth
createTimestamp: **************Z
entryCSN: **************.682369Z#000000#000#000000
modifiersName: gidNumber=0+uidNumber=0,cn=peercred,cn=external,cn=auth
modifyTimestamp: **************Z
