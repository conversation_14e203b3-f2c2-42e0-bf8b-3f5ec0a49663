# AUTO-GENERATED FILE - DO NOT EDIT!! Use ldapmodify.
# CRC32 2dd801cc
dn: cn={7}samba
objectClass: olcSchemaConfig
cn: {7}samba
olcAttributeTypes: {0}( *******.4.1.7165.2.1.24 NAME 'sambaLMPassword' DESC 'L
 anManager Password' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.115.1
 21.1.26{32} SINGLE-VALUE )
olcAttributeTypes: {1}( *******.4.1.7165.2.1.25 NAME 'sambaNTPassword' DESC 'M
 D4 hash of the unicode password' EQUALITY caseIgnoreIA5Match SYNTAX *******.4
 .1.1466.************{32} SINGLE-VALUE )
olcAttributeTypes: {2}( *******.4.1.7165.2.1.26 NAME 'sambaAcctFlags' DESC 'Ac
 count Flags' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************
 {16} SINGLE-VALUE )
olcAttributeTypes: {3}( *******.4.1.7165.2.1.27 NAME 'sambaPwdLastSet' DESC 'T
 imestamp of the last password update' EQUALITY integerMatch SYNTAX *******.4.
 1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {4}( *******.4.1.7165.2.1.28 NAME 'sambaPwdCanChange' DESC 
 'Timestamp of when the user is allowed to update the password' EQUALITY integ
 erMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {5}( *******.4.1.7165.2.1.29 NAME 'sambaPwdMustChange' DESC
  'Timestamp of when the password will expire' EQUALITY integerMatch SYNTAX 1.
 3.*******.1466.************ SINGLE-VALUE )
olcAttributeTypes: {6}( *******.4.1.7165.2.1.30 NAME 'sambaLogonTime' DESC 'Ti
 mestamp of last logon' EQUALITY integerMatch SYNTAX *******.4.1.1466.115.121.
 1.27 SINGLE-VALUE )
olcAttributeTypes: {7}( *******.4.1.7165.2.1.31 NAME 'sambaLogoffTime' DESC 'T
 imestamp of last logoff' EQUALITY integerMatch SYNTAX *******.4.1.1466.115.12
 1.1.27 SINGLE-VALUE )
olcAttributeTypes: {8}( *******.4.1.7165.2.1.32 NAME 'sambaKickoffTime' DESC '
 Timestamp of when the user will be logged off automatically' EQUALITY integer
 Match SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {9}( *******.4.1.7165.2.1.48 NAME 'sambaBadPasswordCount' D
 ESC 'Bad password attempt count' EQUALITY integerMatch SYNTAX *******.4.1.146
 6.************ SINGLE-VALUE )
olcAttributeTypes: {10}( *******.4.1.7165.2.1.49 NAME 'sambaBadPasswordTime' D
 ESC 'Time of the last bad password attempt' EQUALITY integerMatch SYNTAX 1.3.
 *******.1466.************ SINGLE-VALUE )
olcAttributeTypes: {11}( *******.4.1.7165.2.1.55 NAME 'sambaLogonHours' DESC '
 Logon Hours' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************
 {42} SINGLE-VALUE )
olcAttributeTypes: {12}( *******.4.1.7165.2.1.33 NAME 'sambaHomeDrive' DESC 'D
 river letter of home directory mapping' EQUALITY caseIgnoreIA5Match SYNTAX 1.
 3.*******.1466.************{4} SINGLE-VALUE )
olcAttributeTypes: {13}( *******.4.1.7165.2.1.34 NAME 'sambaLogonScript' DESC 
 'Logon script path' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.115.121.
 1.15{255} SINGLE-VALUE )
olcAttributeTypes: {14}( *******.4.1.7165.2.1.35 NAME 'sambaProfilePath' DESC 
 'Roaming profile path' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.115.1
 21.1.15{255} SINGLE-VALUE )
olcAttributeTypes: {15}( *******.4.1.7165.2.1.36 NAME 'sambaUserWorkstations' 
 DESC 'List of user workstations the user is allowed to logon to' EQUALITY cas
 eIgnoreMatch SYNTAX *******.4.1.1466.************{255} SINGLE-VALUE )
olcAttributeTypes: {16}( *******.4.1.7165.2.1.37 NAME 'sambaHomePath' DESC 'Ho
 me directory UNC path' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.115.1
 21.1.15{128} )
olcAttributeTypes: {17}( *******.4.1.7165.2.1.38 NAME 'sambaDomainName' DESC '
 Windows NT domain to which the user belongs' EQUALITY caseIgnoreMatch SYNTAX 
 *******.4.1.1466.************{128} )
olcAttributeTypes: {18}( *******.4.1.7165.2.1.47 NAME 'sambaMungedDial' DESC '
 Base64 encoded user parameter string' EQUALITY caseExactMatch SYNTAX *******.
 4.1.1466.************{1050} )
olcAttributeTypes: {19}( *******.4.1.7165.2.1.54 NAME 'sambaPasswordHistory' D
 ESC 'Concatenated MD5 hashes of the salted NT passwords used on this account'
  EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************{32} )
olcAttributeTypes: {20}( *******.4.1.7165.2.1.20 NAME 'sambaSID' DESC 'Securit
 y ID' EQUALITY caseIgnoreIA5Match SUBSTR caseExactIA5SubstringsMatch SYNTAX 1
 .3.*******.1466.************{64} SINGLE-VALUE )
olcAttributeTypes: {21}( *******.4.1.7165.2.1.23 NAME 'sambaPrimaryGroupSID' D
 ESC 'Primary Group Security ID' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.
 1.1466.************{64} SINGLE-VALUE )
olcAttributeTypes: {22}( *******.4.1.7165.2.1.51 NAME 'sambaSIDList' DESC 'Sec
 urity ID List' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.115.121.1.
 26{64} )
olcAttributeTypes: {23}( *******.4.1.7165.2.1.19 NAME 'sambaGroupType' DESC 'N
 T Group Type' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SING
 LE-VALUE )
olcAttributeTypes: {24}( *******.4.1.7165.2.1.21 NAME 'sambaNextUserRid' DESC 
 'Next NT rid to give our for users' EQUALITY integerMatch SYNTAX *******.4.1.
 1466.************ SINGLE-VALUE )
olcAttributeTypes: {25}( *******.4.1.7165.2.1.22 NAME 'sambaNextGroupRid' DESC
  'Next NT rid to give out for groups' EQUALITY integerMatch SYNTAX *******.4.
 1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {26}( *******.4.1.7165.2.1.39 NAME 'sambaNextRid' DESC 'Nex
 t NT rid to give out for anything' EQUALITY integerMatch SYNTAX *******.4.1.1
 466.************ SINGLE-VALUE )
olcAttributeTypes: {27}( *******.4.1.7165.2.1.40 NAME 'sambaAlgorithmicRidBase
 ' DESC 'Base at which the samba RID generation algorithm should operate' EQUA
 LITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {28}( *******.4.1.7165.2.1.41 NAME 'sambaShareName' DESC 'S
 hare Name' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.************ SING
 LE-VALUE )
olcAttributeTypes: {29}( *******.4.1.7165.2.1.42 NAME 'sambaOptionName' DESC '
 Option Name' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX
  *******.4.1.1466.************{256} )
olcAttributeTypes: {30}( *******.4.1.7165.2.1.43 NAME 'sambaBoolOption' DESC '
 A boolean option' EQUALITY booleanMatch SYNTAX *******.4.1.1466.115.121.1.7 S
 INGLE-VALUE )
olcAttributeTypes: {31}( *******.4.1.7165.2.1.44 NAME 'sambaIntegerOption' DES
 C 'An integer option' EQUALITY integerMatch SYNTAX *******.4.1.1466.115.121.1
 .27 SINGLE-VALUE )
olcAttributeTypes: {32}( *******.4.1.7165.2.1.45 NAME 'sambaStringOption' DESC
  'A string option' EQUALITY caseExactIA5Match SYNTAX *******.4.1.1466.115.121
 .1.26 SINGLE-VALUE )
olcAttributeTypes: {33}( *******.4.1.7165.2.1.46 NAME 'sambaStringListOption' 
 DESC 'A string list option' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.
 ************ )
olcAttributeTypes: {34}( *******.4.1.7165.2.1.53 NAME 'sambaTrustFlags' DESC '
 Trust Password Flags' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.115
 .121.1.26 )
olcAttributeTypes: {35}( *******.4.1.7165.2.1.58 NAME 'sambaMinPwdLength' DESC
  'Minimal password length (default: 5)' EQUALITY integerMatch SYNTAX *******.
 4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {36}( *******.4.1.7165.2.1.59 NAME 'sambaPwdHistoryLength' 
 DESC 'Length of Password History Entries (default: 0 => off)' EQUALITY intege
 rMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {37}( *******.4.1.7165.2.1.60 NAME 'sambaLogonToChgPwd' DES
 C 'Force Users to logon for password change (default: 0 => off, 2 => on)' EQU
 ALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {38}( *******.4.1.7165.2.1.61 NAME 'sambaMaxPwdAge' DESC 'M
 aximum password age, in seconds (default: -1 => never expire passwords)' EQUA
 LITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {39}( *******.4.1.7165.2.1.62 NAME 'sambaMinPwdAge' DESC 'M
 inimum password age, in seconds (default: 0 => allow immediate password chang
 e)' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {40}( *******.4.1.7165.2.1.63 NAME 'sambaLockoutDuration' D
 ESC 'Lockout duration in minutes (default: 30, -1 => forever)' EQUALITY integ
 erMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {41}( *******.4.1.7165.2.1.64 NAME 'sambaLockoutObservation
 Window' DESC 'Reset time after lockout in minutes (default: 30)' EQUALITY int
 egerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {42}( *******.4.1.7165.2.1.65 NAME 'sambaLockoutThreshold' 
 DESC 'Lockout users after bad logon attempts (default: 0 => off)' EQUALITY in
 tegerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {43}( *******.4.1.7165.2.1.66 NAME 'sambaForceLogoff' DESC 
 'Disconnect Users outside logon hours (default: -1 => off, 0 => on)' EQUALITY
  integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {44}( *******.4.1.7165.2.1.67 NAME 'sambaRefuseMachinePwdCh
 ange' DESC 'Allow Machine Password changes (default: 0 => off)' EQUALITY inte
 gerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcObjectClasses: {0}( *******.4.1.7165.2.2.6 NAME 'sambaSamAccount' DESC 'Sam
 ba 3.0 Auxilary SAM Account' SUP top AUXILIARY MUST ( uid $ sambaSID ) MAY ( 
 cn $ sambaLMPassword $ sambaNTPassword $ sambaPwdLastSet $ sambaLogonTime $ s
 ambaLogoffTime $ sambaKickoffTime $ sambaPwdCanChange $ sambaPwdMustChange $ 
 sambaAcctFlags $ displayName $ sambaHomePath $ sambaHomeDrive $ sambaLogonScr
 ipt $ sambaProfilePath $ description $ sambaUserWorkstations $ sambaPrimaryGr
 oupSID $ sambaDomainName $ sambaMungedDial $ sambaBadPasswordCount $ sambaBad
 PasswordTime $ sambaPasswordHistory $ sambaLogonHours ) )
olcObjectClasses: {1}( *******.4.1.7165.2.2.4 NAME 'sambaGroupMapping' DESC 'S
 amba Group Mapping' SUP top AUXILIARY MUST ( gidNumber $ sambaSID $ sambaGrou
 pType ) MAY ( displayName $ description $ sambaSIDList ) )
olcObjectClasses: {2}( *******.4.1.7165.2.2.14 NAME 'sambaTrustPassword' DESC 
 'Samba Trust Password' SUP top STRUCTURAL MUST ( sambaDomainName $ sambaNTPas
 sword $ sambaTrustFlags ) MAY ( sambaSID $ sambaPwdLastSet ) )
olcObjectClasses: {3}( *******.4.1.7165.2.2.5 NAME 'sambaDomain' DESC 'Samba D
 omain Information' SUP top STRUCTURAL MUST ( sambaDomainName $ sambaSID ) MAY
  ( sambaNextRid $ sambaNextGroupRid $ sambaNextUserRid $ sambaAlgorithmicRidB
 ase $ sambaMinPwdLength $ sambaPwdHistoryLength $ sambaLogonToChgPwd $ sambaM
 axPwdAge $ sambaMinPwdAge $ sambaLockoutDuration $ sambaLockoutObservationWin
 dow $ sambaLockoutThreshold $ sambaForceLogoff $ sambaRefuseMachinePwdChange 
 ) )
olcObjectClasses: {4}( *******.4.1.7165.2.2.7 NAME 'sambaUnixIdPool' DESC 'Poo
 l for allocating UNIX uids/gids' SUP top AUXILIARY MUST ( uidNumber $ gidNumb
 er ) )
olcObjectClasses: {5}( *******.4.1.7165.2.2.8 NAME 'sambaIdmapEntry' DESC 'Map
 ping from a SID to an ID' SUP top AUXILIARY MUST sambaSID MAY ( uidNumber $ g
 idNumber ) )
olcObjectClasses: {6}( *******.4.1.7165.2.2.9 NAME 'sambaSidEntry' DESC 'Struc
 tural Class for a SID' SUP top STRUCTURAL MUST sambaSID )
olcObjectClasses: {7}( *******.4.1.7165.2.2.10 NAME 'sambaConfig' DESC 'Samba 
 Configuration Section' SUP top AUXILIARY MAY description )
olcObjectClasses: {8}( *******.4.1.7165.2.2.11 NAME 'sambaShare' DESC 'Samba S
 hare Section' SUP top STRUCTURAL MUST sambaShareName MAY description )
olcObjectClasses: {9}( *******.4.1.7165.2.2.12 NAME 'sambaConfigOption' DESC '
 Samba Configuration Option' SUP top STRUCTURAL MUST sambaOptionName MAY ( sam
 baBoolOption $ sambaIntegerOption $ sambaStringOption $ sambaStringListoption
  $ description ) )
structuralObjectClass: olcSchemaConfig
entryUUID: 1e45010e-8c54-1034-9812-2f3e74c83a84
creatorsName: gidNumber=0+uidNumber=0,cn=peercred,cn=external,cn=auth
createTimestamp: 20150511180557Z
entryCSN: 20150511180557.693431Z#000000#000#000000
modifiersName: gidNumber=0+uidNumber=0,cn=peercred,cn=external,cn=auth
modifyTimestamp: 20150511180557Z
