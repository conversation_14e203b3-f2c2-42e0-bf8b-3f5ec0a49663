# AUTO-GENERATED FILE - DO NOT EDIT!! Use ldapmodify.
# CRC32 75183a81
dn: cn={11}quota
objectClass: olcSchemaConfig
cn: {11}quota
olcAttributeTypes: {0}( 1.3.6.1.4.1.19937.1.1.1 NAME 'quota' DESC 'Quotas (Fil
 eSystem:BlocksSoft,BlocksHard,InodesSoft,InodesHard)' EQUALITY caseIgnoreIA5M
 atch SYNTAX 1.3.6.1.4.1.1466.115.121.1.26{255} )
olcAttributeTypes: {1}( 1.3.6.1.4.1.19937.1.1.2 NAME 'networkquota' DESC 'Netw
 ork Quotas (network,protocol,bytes)' EQUALITY caseIgnoreIA5Match SYNTAX 1.3.6
 .1.4.1.1466.115.121.1.26{255} )
olcObjectClasses: {0}( 1.3.6.1.4.1.19937.1.2.1 NAME 'systemQuotas' DESC 'Syste
 m Quotas' SUP posixAccount AUXILIARY MUST uid MAY ( quota $ networkquota ) )
olcObjectClasses: {1}( 1.3.6.1.4.1.19937.1.2.2 NAME 'defaultQuotas' DESC 'Quot
 a defaults to apply to members of a group' SUP top AUXILIARY MUST cn MAY ( qu
 ota $ networkquota ) )
structuralObjectClass: olcSchemaConfig
entryUUID: 1e4a04d8-8c54-1034-9816-2f3e74c83a84
creatorsName: gidNumber=0+uidNumber=0,cn=peercred,cn=external,cn=auth
createTimestamp: **************Z
entryCSN: **************.726296Z#000000#000#000000
modifiersName: gidNumber=0+uidNumber=0,cn=peercred,cn=external,cn=auth
modifyTimestamp: **************Z
