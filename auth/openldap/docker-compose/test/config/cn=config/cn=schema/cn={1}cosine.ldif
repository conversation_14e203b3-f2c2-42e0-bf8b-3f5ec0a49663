# AUTO-GENERATED FILE - DO NOT EDIT!! Use ldapmodify.
# CRC32 d740867f
dn: cn={1}cosine
objectClass: olcSchemaConfig
cn: {1}cosine
olcAttributeTypes: {0}( 0.9.2342.********.100.1.2 NAME 'textEncodedORAddress' 
 EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.
 1466.************{256} )
olcAttributeTypes: {1}( 0.9.2342.********.100.1.4 NAME 'info' DESC 'RFC1274: g
 eneral information' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch
  SYNTAX *******.4.1.1466.************{2048} )
olcAttributeTypes: {2}( 0.9.2342.********.100.1.5 NAME ( 'drink' 'favouriteDri
 nk' ) DESC 'RFC1274: favorite drink' EQUALITY caseIgnoreMatch SUBSTR caseIgno
 reSubstringsMatch SYNTAX *******.4.1.1466.************{256} )
olcAttributeTypes: {3}( 0.9.2342.********.100.1.6 NAME 'roomNumber' DESC 'RFC1
 274: room number' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch S
 YNTAX *******.4.1.1466.************{256} )
olcAttributeTypes: {4}( 0.9.2342.********.100.1.7 NAME 'photo' DESC 'RFC1274: 
 photo (G3 fax)' SYNTAX *******.4.1.1466.************{25000} )
olcAttributeTypes: {5}( 0.9.2342.********.100.1.8 NAME 'userClass' DESC 'RFC12
 74: category of user' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMat
 ch SYNTAX *******.4.1.1466.************{256} )
olcAttributeTypes: {6}( 0.9.2342.********.100.1.9 NAME 'host' DESC 'RFC1274: h
 ost computer' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTA
 X *******.4.1.1466.************{256} )
olcAttributeTypes: {7}( 0.9.2342.********.100.1.10 NAME 'manager' DESC 'RFC127
 4: DN of manager' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.115
 .121.1.12 )
olcAttributeTypes: {8}( 0.9.2342.********.100.1.11 NAME 'documentIdentifier' D
 ESC 'RFC1274: unique identifier of document' EQUALITY caseIgnoreMatch SUBSTR 
 caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************{256} )
olcAttributeTypes: {9}( 0.9.2342.********.100.1.12 NAME 'documentTitle' DESC '
 RFC1274: title of document' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstri
 ngsMatch SYNTAX *******.4.1.1466.************{256} )
olcAttributeTypes: {10}( 0.9.2342.********.100.1.13 NAME 'documentVersion' DES
 C 'RFC1274: version of document' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSu
 bstringsMatch SYNTAX *******.4.1.1466.************{256} )
olcAttributeTypes: {11}( 0.9.2342.********.100.1.14 NAME 'documentAuthor' DESC
  'RFC1274: DN of author of document' EQUALITY distinguishedNameMatch SYNTAX 1
 .3.6.1.4.1.1466.************ )
olcAttributeTypes: {12}( 0.9.2342.********.100.1.15 NAME 'documentLocation' DE
 SC 'RFC1274: location of document original' EQUALITY caseIgnoreMatch SUBSTR c
 aseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************{256} )
olcAttributeTypes: {13}( 0.9.2342.********.100.1.20 NAME ( 'homePhone' 'homeTe
 lephoneNumber' ) DESC 'RFC1274: home telephone number' EQUALITY telephoneNumb
 erMatch SUBSTR telephoneNumberSubstringsMatch SYNTAX *******.4.1.1466.115.121
 .1.50 )
olcAttributeTypes: {14}( 0.9.2342.********.100.1.21 NAME 'secretary' DESC 'RFC
 1274: DN of secretary' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.146
 6.************ )
olcAttributeTypes: {15}( 0.9.2342.********.100.1.22 NAME 'otherMailbox' SYNTAX
  *******.4.1.1466.115.121.1.39 )
olcAttributeTypes: {16}( 0.9.2342.********.100.1.26 NAME 'aRecord' EQUALITY ca
 seIgnoreIA5Match SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {17}( 0.9.2342.********.100.1.27 NAME 'mDRecord' EQUALITY c
 aseIgnoreIA5Match SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {18}( 0.9.2342.********.100.1.28 NAME 'mXRecord' EQUALITY c
 aseIgnoreIA5Match SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {19}( 0.9.2342.********.100.1.29 NAME 'nSRecord' EQUALITY c
 aseIgnoreIA5Match SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {20}( 0.9.2342.********.100.1.30 NAME 'sOARecord' EQUALITY 
 caseIgnoreIA5Match SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {21}( 0.9.2342.********.100.1.31 NAME 'cNAMERecord' EQUALIT
 Y caseIgnoreIA5Match SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {22}( 0.9.2342.********.100.1.38 NAME 'associatedName' DESC
  'RFC1274: DN of entry associated with domain' EQUALITY distinguishedNameMatc
 h SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {23}( 0.9.2342.********.100.1.39 NAME 'homePostalAddress' D
 ESC 'RFC1274: home postal address' EQUALITY caseIgnoreListMatch SUBSTR caseIg
 noreListSubstringsMatch SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {24}( 0.9.2342.********.100.1.40 NAME 'personalTitle' DESC 
 'RFC1274: personal title' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstring
 sMatch SYNTAX *******.4.1.1466.************{256} )
olcAttributeTypes: {25}( 0.9.2342.********.100.1.41 NAME ( 'mobile' 'mobileTel
 ephoneNumber' ) DESC 'RFC1274: mobile telephone number' EQUALITY telephoneNum
 berMatch SUBSTR telephoneNumberSubstringsMatch SYNTAX *******.4.1.1466.115.12
 1.1.50 )
olcAttributeTypes: {26}( 0.9.2342.********.100.1.42 NAME ( 'pager' 'pagerTelep
 honeNumber' ) DESC 'RFC1274: pager telephone number' EQUALITY telephoneNumber
 Match SUBSTR telephoneNumberSubstringsMatch SYNTAX *******.4.1.1466.115.121.1
 .50 )
olcAttributeTypes: {27}( 0.9.2342.********.100.1.43 NAME ( 'co' 'friendlyCount
 ryName' ) DESC 'RFC1274: friendly country name' EQUALITY caseIgnoreMatch SUBS
 TR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {28}( 0.9.2342.********.100.1.44 NAME 'uniqueIdentifier' DE
 SC 'RFC1274: unique identifer' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.14
 66.************{256} )
olcAttributeTypes: {29}( 0.9.2342.********.100.1.45 NAME 'organizationalStatus
 ' DESC 'RFC1274: organizational status' EQUALITY caseIgnoreMatch SUBSTR caseI
 gnoreSubstringsMatch SYNTAX *******.4.1.1466.************{256} )
olcAttributeTypes: {30}( 0.9.2342.********.100.1.46 NAME 'janetMailbox' DESC '
 RFC1274: Janet mailbox' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5Subst
 ringsMatch SYNTAX *******.4.1.1466.************{256} )
olcAttributeTypes: {31}( 0.9.2342.********.100.1.47 NAME 'mailPreferenceOption
 ' DESC 'RFC1274: mail preference option' SYNTAX *******.4.1.1466.115.121.1.27
  )
olcAttributeTypes: {32}( 0.9.2342.********.100.1.48 NAME 'buildingName' DESC '
 RFC1274: name of building' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstrin
 gsMatch SYNTAX *******.4.1.1466.************{256} )
olcAttributeTypes: {33}( 0.9.2342.********.100.1.49 NAME 'dSAQuality' DESC 'RF
 C1274: DSA Quality' SYNTAX *******.4.1.1466.115.121.1.19 SINGLE-VALUE )
olcAttributeTypes: {34}( 0.9.2342.********.100.1.50 NAME 'singleLevelQuality' 
 DESC 'RFC1274: Single Level Quality' SYNTAX *******.4.1.1466.115.121.1.13 SIN
 GLE-VALUE )
olcAttributeTypes: {35}( 0.9.2342.********.100.1.51 NAME 'subtreeMinimumQualit
 y' DESC 'RFC1274: Subtree Mininum Quality' SYNTAX *******.4.1.1466.115.121.1.
 13 SINGLE-VALUE )
olcAttributeTypes: {36}( 0.9.2342.********.100.1.52 NAME 'subtreeMaximumQualit
 y' DESC 'RFC1274: Subtree Maximun Quality' SYNTAX *******.4.1.1466.115.121.1.
 13 SINGLE-VALUE )
olcAttributeTypes: {37}( 0.9.2342.********.100.1.53 NAME 'personalSignature' D
 ESC 'RFC1274: Personal Signature (G3 fax)' SYNTAX *******.4.1.1466.115.121.1.
 23 )
olcAttributeTypes: {38}( 0.9.2342.********.100.1.54 NAME 'dITRedirect' DESC 'R
 FC1274: DIT Redirect' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466
 .************ )
olcAttributeTypes: {39}( 0.9.2342.********.100.1.55 NAME 'audio' DESC 'RFC1274
 : audio (u-law)' SYNTAX *******.4.1.1466.***********{25000} )
olcAttributeTypes: {40}( 0.9.2342.********.100.1.56 NAME 'documentPublisher' D
 ESC 'RFC1274: publisher of document' EQUALITY caseIgnoreMatch SUBSTR caseIgno
 reSubstringsMatch SYNTAX *******.4.1.1466.************ )
olcObjectClasses: {0}( 0.9.2342.********.100.4.4 NAME ( 'pilotPerson' 'newPilo
 tPerson' ) SUP person STRUCTURAL MAY ( userid $ textEncodedORAddress $ rfc822
 Mailbox $ favouriteDrink $ roomNumber $ userClass $ homeTelephoneNumber $ hom
 ePostalAddress $ secretary $ personalTitle $ preferredDeliveryMethod $ busine
 ssCategory $ janetMailbox $ otherMailbox $ mobileTelephoneNumber $ pagerTelep
 honeNumber $ organizationalStatus $ mailPreferenceOption $ personalSignature 
 ) )
olcObjectClasses: {1}( 0.9.2342.********.100.4.5 NAME 'account' SUP top STRUCT
 URAL MUST userid MAY ( description $ seeAlso $ localityName $ organizationNam
 e $ organizationalUnitName $ host ) )
olcObjectClasses: {2}( 0.9.2342.********.100.4.6 NAME 'document' SUP top STRUC
 TURAL MUST documentIdentifier MAY ( commonName $ description $ seeAlso $ loca
 lityName $ organizationName $ organizationalUnitName $ documentTitle $ docume
 ntVersion $ documentAuthor $ documentLocation $ documentPublisher ) )
olcObjectClasses: {3}( 0.9.2342.********.100.4.7 NAME 'room' SUP top STRUCTURA
 L MUST commonName MAY ( roomNumber $ description $ seeAlso $ telephoneNumber 
 ) )
olcObjectClasses: {4}( 0.9.2342.********.100.4.9 NAME 'documentSeries' SUP top
  STRUCTURAL MUST commonName MAY ( description $ seeAlso $ telephonenumber $ l
 ocalityName $ organizationName $ organizationalUnitName ) )
olcObjectClasses: {5}( 0.9.2342.********.100.4.13 NAME 'domain' SUP top STRUCT
 URAL MUST domainComponent MAY ( associatedName $ organizationName $ descripti
 on $ businessCategory $ seeAlso $ searchGuide $ userPassword $ localityName $
  stateOrProvinceName $ streetAddress $ physicalDeliveryOfficeName $ postalAdd
 ress $ postalCode $ postOfficeBox $ streetAddress $ facsimileTelephoneNumber 
 $ internationalISDNNumber $ telephoneNumber $ teletexTerminalIdentifier $ tel
 exNumber $ preferredDeliveryMethod $ destinationIndicator $ registeredAddress
  $ x121Address ) )
olcObjectClasses: {6}( 0.9.2342.********.100.4.14 NAME 'RFC822localPart' SUP d
 omain STRUCTURAL MAY ( commonName $ surname $ description $ seeAlso $ telepho
 neNumber $ physicalDeliveryOfficeName $ postalAddress $ postalCode $ postOffi
 ceBox $ streetAddress $ facsimileTelephoneNumber $ internationalISDNNumber $ 
 telephoneNumber $ teletexTerminalIdentifier $ telexNumber $ preferredDelivery
 Method $ destinationIndicator $ registeredAddress $ x121Address ) )
olcObjectClasses: {7}( 0.9.2342.********.100.4.15 NAME 'dNSDomain' SUP domain 
 STRUCTURAL MAY ( ARecord $ MDRecord $ MXRecord $ NSRecord $ SOARecord $ CNAME
 Record ) )
olcObjectClasses: {8}( 0.9.2342.********.100.4.17 NAME 'domainRelatedObject' D
 ESC 'RFC1274: an object related to an domain' SUP top AUXILIARY MUST associat
 edDomain )
olcObjectClasses: {9}( 0.9.2342.********.100.4.18 NAME 'friendlyCountry' SUP c
 ountry STRUCTURAL MUST friendlyCountryName )
olcObjectClasses: {10}( 0.9.2342.********.100.4.20 NAME 'pilotOrganization' SU
 P ( organization $ organizationalUnit ) STRUCTURAL MAY buildingName )
olcObjectClasses: {11}( 0.9.2342.********.100.4.21 NAME 'pilotDSA' SUP dsa STR
 UCTURAL MAY dSAQuality )
olcObjectClasses: {12}( 0.9.2342.********.100.4.22 NAME 'qualityLabelledData' 
 SUP top AUXILIARY MUST dsaQuality MAY ( subtreeMinimumQuality $ subtreeMaximu
 mQuality ) )
structuralObjectClass: olcSchemaConfig
entryUUID: 1dcfeff4-8c54-1034-90fa-f5ce8ee3f703
creatorsName: cn=config
createTimestamp: 20150511180556Z
entryCSN: 20150511180556.926224Z#000000#000#000000
modifiersName: cn=config
modifyTimestamp: 20150511180556Z
