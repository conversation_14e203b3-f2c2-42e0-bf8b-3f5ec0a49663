# AUTO-GENERATED FILE - DO NOT EDIT!! Use ldapmodify.
# CRC32 ab1eec7a
dn: cn={13}mmc
objectClass: olcSchemaConfig
cn: {13}mmc
olcAttributeTypes: {0}( 1.3.6.1.4.1.40098.1.1.12.1 NAME 'lmcACL' DESC 'LMC acl
  entry' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX 1.3.
 6.1.4.1.1466.115.121.1.15 SINGLE-VALUE )
olcAttributeTypes: {1}( 1.3.6.1.4.1.40098.1.1.12.2 NAME 'lmcPrefMode' DESC 'LM
 C user preferences' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch
  SYNTAX 1.3.6.1.4.1.1466.115.121.1.15 SINGLE-VALUE )
olcAttributeTypes: {2}( 1.3.6.1.4.1.40098.1.1.12.3 NAME 'lmcPrinterAllowed' DE
 SC 'LMC a printer where the user has the rights to print' EQUALITY caseIgnore
 IA5Match SUBSTR caseIgnoreSubstringsMatch SYNTAX 1.3.6.1.4.1.1466.115.121.1.2
 6 )
olcObjectClasses: {0}( 1.3.6.1.4.1.40098.1.2.1.19.1 NAME 'lmcUserObject' DESC 
 'Objectclass for LMC user settings ' AUXILIARY MAY ( lmcACL $ lmcPrefMode $ l
 mcPrinterAllowed ) )
structuralObjectClass: olcSchemaConfig
entryUUID: 1e4cd546-8c54-1034-9818-2f3e74c83a84
creatorsName: gidNumber=0+uidNumber=0,cn=peercred,cn=external,cn=auth
createTimestamp: 20150511180557Z
entryCSN: 20150511180557.744737Z#000000#000#000000
modifiersName: gidNumber=0+uidNumber=0,cn=peercred,cn=external,cn=auth
modifyTimestamp: 20150511180557Z
