# AUTO-GENERATED FILE - DO NOT EDIT!! Use ldapmodify.
# CRC32 28a1d01d
dn: cn={2}nis
objectClass: olcSchemaConfig
cn: {2}nis
olcAttributeTypes: {0}( *******.1.1.1.2 NAME 'gecos' DESC 'The GECOS field; th
 e common name' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatc
 h SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )
olcAttributeTypes: {1}( *******.1.1.1.3 NAME 'homeDirectory' DESC 'The absolut
 e path to the home directory' EQUALITY caseExactIA5Match SYNTAX *******.4.1.1
 466.11********** SINGLE-VALUE )
olcAttributeTypes: {2}( *******.1.1.1.4 NAME 'loginShell' DESC 'The path to th
 e login shell' EQUALITY caseExactIA5Match SYNTAX *******.4.1.1466.115.121.1.2
 6 SINGLE-VALUE )
olcAttributeTypes: {3}( *******.1.1.1.5 NAME 'shadowLastChange' EQUALITY integ
 erMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {4}( *******.1.1.1.6 NAME 'shadowMin' EQUALITY integerMatch
  SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {5}( *******.1.1.1.7 NAME 'shadowMax' EQUALITY integerMatch
  SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {6}( *******.1.1.1.8 NAME 'shadowWarning' EQUALITY integerM
 atch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {7}( *******.1.1.1.9 NAME 'shadowInactive' EQUALITY integer
 Match SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {8}( *******.1.1.1.10 NAME 'shadowExpire' EQUALITY integerM
 atch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {9}( *******.******** NAME 'shadowFlag' EQUALITY integerMat
 ch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {10}( *******.******** NAME 'memberUid' EQUALITY caseExactI
 A5Match SUBSTR caseExactIA5SubstringsMatch SYNTAX *******.4.1.1466.115.121.1.
 26 )
olcAttributeTypes: {11}( *******.******** NAME 'memberNisNetgroup' EQUALITY ca
 seExactIA5Match SUBSTR caseExactIA5SubstringsMatch SYNTAX *******.4.1.1466.11
 ********** )
olcAttributeTypes: {12}( *******.******** NAME 'nisNetgroupTriple' DESC 'Netgr
 oup triple' SYNTAX *******.******* )
olcAttributeTypes: {13}( *******.******** NAME 'ipServicePort' EQUALITY intege
 rMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {14}( *******.******** NAME 'ipServiceProtocol' SUP name )
olcAttributeTypes: {15}( *******.******** NAME 'ipProtocolNumber' EQUALITY int
 egerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {16}( *******.******** NAME 'oncRpcNumber' EQUALITY integer
 Match SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {17}( *******.******** NAME 'ipHostNumber' DESC 'IP address
 ' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.11**********{128} )
olcAttributeTypes: {18}( *******.******** NAME 'ipNetworkNumber' DESC 'IP netw
 ork' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.11**********{128} SI
 NGLE-VALUE )
olcAttributeTypes: {19}( *******.******** NAME 'ipNetmaskNumber' DESC 'IP netm
 ask' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.11**********{128} SI
 NGLE-VALUE )
olcAttributeTypes: {20}( *******.******** NAME 'macAddress' DESC 'MAC address'
  EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.11**********{128} )
olcAttributeTypes: {21}( *******.******** NAME 'bootParameter' DESC 'rpc.bootp
 aramd parameter' SYNTAX *******.******* )
olcAttributeTypes: {22}( *******.******** NAME 'bootFile' DESC 'Boot image nam
 e' EQUALITY caseExactIA5Match SYNTAX *******.4.1.1466.11********** )
olcAttributeTypes: {23}( *******.******** NAME 'nisMapName' SUP name )
olcAttributeTypes: {24}( *******.******** NAME 'nisMapEntry' EQUALITY caseExac
 tIA5Match SUBSTR caseExactIA5SubstringsMatch SYNTAX *******.4.1.1466.115.121.
 1.26{1024} SINGLE-VALUE )
olcObjectClasses: {0}( *******.******* NAME 'posixAccount' DESC 'Abstraction o
 f an account with POSIX attributes' SUP top AUXILIARY MUST ( cn $ uid $ uidNu
 mber $ gidNumber $ homeDirectory ) MAY ( userPassword $ loginShell $ gecos $ 
 description ) )
olcObjectClasses: {1}( *******.******* NAME 'shadowAccount' DESC 'Additional a
 ttributes for shadow passwords' SUP top AUXILIARY MUST uid MAY ( userPassword
  $ shadowLastChange $ shadowMin $ shadowMax $ shadowWarning $ shadowInactive 
 $ shadowExpire $ shadowFlag $ description ) )
olcObjectClasses: {2}( *******.******* NAME 'posixGroup' DESC 'Abstraction of 
 a group of accounts' SUP top STRUCTURAL MUST ( cn $ gidNumber ) MAY ( userPas
 sword $ memberUid $ description ) )
olcObjectClasses: {3}( *******.******* NAME 'ipService' DESC 'Abstraction an I
 nternet Protocol service' SUP top STRUCTURAL MUST ( cn $ ipServicePort $ ipSe
 rviceProtocol ) MAY description )
olcObjectClasses: {4}( *******.******* NAME 'ipProtocol' DESC 'Abstraction of 
 an IP protocol' SUP top STRUCTURAL MUST ( cn $ ipProtocolNumber $ description
  ) MAY description )
olcObjectClasses: {5}( *******.******* NAME 'oncRpc' DESC 'Abstraction of an O
 NC/RPC binding' SUP top STRUCTURAL MUST ( cn $ oncRpcNumber $ description ) M
 AY description )
olcObjectClasses: {6}( *******.******* NAME 'ipHost' DESC 'Abstraction of a ho
 st, an IP device' SUP top AUXILIARY MUST ( cn $ ipHostNumber ) MAY ( l $ desc
 ription $ manager ) )
olcObjectClasses: {7}( *******.******* NAME 'ipNetwork' DESC 'Abstraction of a
 n IP network' SUP top STRUCTURAL MUST ( cn $ ipNetworkNumber ) MAY ( ipNetmas
 kNumber $ l $ description $ manager ) )
olcObjectClasses: {8}( *******.******* NAME 'nisNetgroup' DESC 'Abstraction of
  a netgroup' SUP top STRUCTURAL MUST cn MAY ( nisNetgroupTriple $ memberNisNe
 tgroup $ description ) )
olcObjectClasses: {9}( *******.******* NAME 'nisMap' DESC 'A generic abstracti
 on of a NIS map' SUP top STRUCTURAL MUST nisMapName MAY description )
olcObjectClasses: {10}( *******.*******0 NAME 'nisObject' DESC 'An entry in a 
 NIS map' SUP top STRUCTURAL MUST ( cn $ nisMapEntry $ nisMapName ) MAY descri
 ption )
olcObjectClasses: {11}( *******.*******1 NAME 'ieee802Device' DESC 'A device w
 ith a MAC address' SUP top AUXILIARY MAY macAddress )
olcObjectClasses: {12}( *******.******** NAME 'bootableDevice' DESC 'A device 
 with boot parameters' SUP top AUXILIARY MAY ( bootFile $ bootParameter ) )
structuralObjectClass: olcSchemaConfig
entryUUID: 1dd0050c-8c54-1034-90fb-f5ce8ee3f703
creatorsName: cn=config
createTimestamp: 20150511180556Z
entryCSN: 20150511180556.926764Z#000000#000#000000
modifiersName: cn=config
modifyTimestamp: 20150511180556Z
