# AUTO-GENERATED FILE - DO NOT EDIT!! Use ldapmodify.
# CRC32 7e6e0285
dn: cn={5}dhcp
objectClass: olcSchemaConfig
cn: {5}dhcp
olcAttributeTypes: {0}( 2.16.840.1.113719.1.203.4.1 NAME 'dhcpPrimaryDN' DESC 
 'The DN of the dhcpServer which is the primary server for the configuration.'
  EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ SINGLE-
 VALUE )
olcAttributeTypes: {1}( 2.16.840.1.113719.1.203.4.2 NAME 'dhcpSecondaryDN' DES
 C 'The DN of dhcpServer(s) which provide backup service for the configuration
 .' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {2}( 2.16.840.1.113719.********* NAME 'dhcpStatements' DESC
  'Flexible storage for specific data depending on what object this exists in.
  Like conditional statements, server parameters, etc. This allows the standar
 d to evolve without needing to adjust the schema.' EQUALITY caseIgnoreIA5Matc
 h SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {3}( 2.16.840.1.113719.********* NAME 'dhcpRange' DESC 'The
  starting & ending IP Addresses in the range (inclusive), separated by a hyph
 en; if the range only contains one address, then just the address can be spec
 ified with no hyphen.  Each range is defined as a separate value.' EQUALITY c
 aseIgnoreIA5Match SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {4}( 2.16.840.1.113719.********* NAME 'dhcpPermitList' DESC
  'This attribute contains the permit lists associated with a pool. Each permi
 t list is defined as a separate value.' EQUALITY caseIgnoreIA5Match SYNTAX 1.
 *******.1.1466.************ )
olcAttributeTypes: {5}( 2.16.840.1.113719.********* NAME 'dhcpNetMask' DESC 'T
 he subnet mask length for the subnet.  The mask can be easily computed from t
 his length.' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGL
 E-VALUE )
olcAttributeTypes: {6}( 2.16.840.1.113719.********* NAME 'dhcpOption' DESC 'En
 coded option values to be sent to clients.  Each value represents a single op
 tion and contains (OptionTag, Length, OptionValue) encoded in the format used
  by DHCP.' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {7}( 2.16.840.1.113719.1.203.4.8 NAME 'dhcpClassData' DESC 
 'Encoded text string or list of bytes expressed in hexadecimal, separated by 
 colons.  Clients match subclasses based on matching the class data with the r
 esults of match or spawn with statements in the class name declarations.' EQU
 ALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {8}( 2.16.840.1.113719.********* NAME 'dhcpOptionsDN' DESC 
 'The distinguished name(s) of the dhcpOption objects containing the configura
 tion options provided by the server.' EQUALITY distinguishedNameMatch SYNTAX 
 *******.4.1.1466.************ )
olcAttributeTypes: {9}( 2.16.840.1.113719.********** NAME 'dhcpHostDN' DESC 't
 he distinguished name(s) of the dhcpHost objects.' EQUALITY distinguishedName
 Match SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {10}( 2.16.840.1.113719.********** NAME 'dhcpPoolDN' DESC '
 The distinguished name(s) of pools.' EQUALITY distinguishedNameMatch SYNTAX 1
 .*******.1.1466.************ )
olcAttributeTypes: {11}( 2.16.840.1.113719.********** NAME 'dhcpGroupDN' DESC 
 'The distinguished name(s)   of the groups.' EQUALITY distinguishedNameMatch 
 SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {12}( 2.16.840.1.113719.********** NAME 'dhcpSubnetDN' DESC
  'The distinguished name(s) of the subnets.' EQUALITY distinguishedNameMatch 
 SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {13}( 2.16.840.1.113719.********** NAME 'dhcpLeaseDN' DESC 
 'The distinguished name of a client address.' EQUALITY distinguishedNameMatch
  SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {14}( 2.16.840.1.113719.********** NAME 'dhcpLeasesDN' DESC
  'The distinguished name(s) client addresses.' EQUALITY distinguishedNameMatc
 h SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {15}( 2.16.840.1.113719.********** NAME 'dhcpClassesDN' DES
 C 'The distinguished name(s) of a class(es) in a subclass.' EQUALITY distingu
 ishedNameMatch SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {16}( 2.16.840.1.113719.********** NAME 'dhcpSubclassesDN' 
 DESC 'The distinguished name(s) of subclass(es).' EQUALITY distinguishedNameM
 atch SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {17}( 2.16.840.1.113719.********** NAME 'dhcpSharedNetworkD
 N' DESC 'The distinguished name(s) of sharedNetworks.' EQUALITY distinguished
 NameMatch SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {18}( 2.16.840.1.113719.********** NAME 'dhcpServiceDN' DES
 C 'The DN of dhcpService object(s)which contain the configuration information
 . Each dhcpServer object has this attribute identifying the DHCP configuratio
 n(s) that the server is associated with.' EQUALITY distinguishedNameMatch SYN
 TAX *******.4.1.1466.************ )
olcAttributeTypes: {19}( 2.16.840.1.113719.********** NAME 'dhcpVersion' DESC 
 'The version attribute of this object.' EQUALITY caseIgnoreIA5Match SYNTAX 1.
 *******.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {20}( 2.16.840.1.113719.********** NAME 'dhcpImplementation
 ' DESC 'Description of the DHCP Server implementation e.g. DHCP Servers vendo
 r.' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************ SINGLE-V
 ALUE )
olcAttributeTypes: {21}( 2.16.840.1.113719.********** NAME 'dhcpAddressState' 
 DESC 'This stores information about the current binding-status of an address.
   For dynamic addresses managed by DHCP, the values should be restricted to t
 he following: "FREE", "ACTIVE", "EXPIRED", "RELEASED", "RESET", "ABANDONED", 
 "BACKUP".  For other addresses, it SHOULD be one of the following: "UNKNOWN",
  "RESERVED" (an address that is managed by DHCP that is reserved for a specif
 ic client), "RESERVED-ACTIVE" (same as reserved, but address is currently in 
 use), "ASSIGNED" (assigned manually or by some other mechanism), "UNASSIGNED"
 , "NOTASSIGNABLE".' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.115.1
 21.1.26 SINGLE-VALUE )
olcAttributeTypes: {22}( 2.16.840.1.113719.********** NAME 'dhcpExpirationTime
 ' DESC 'This is the time the current lease for an address expires.' EQUALITY 
 generalizedTimeMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {23}( 2.16.840.1.113719.********** NAME 'dhcpStartTimeOfSta
 te' DESC 'This is the time of the last state change for a leased address.' EQ
 UALITY generalizedTimeMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE
  )
olcAttributeTypes: {24}( 2.16.840.1.113719.********** NAME 'dhcpLastTransactio
 nTime' DESC 'This is the last time a valid DHCP packet was received from the 
 client.' EQUALITY generalizedTimeMatch SYNTAX *******.4.1.1466.************ S
 INGLE-VALUE )
olcAttributeTypes: {25}( 2.16.840.1.113719.********** NAME 'dhcpBootpFlag' DES
 C 'This indicates whether the address was assigned via BOOTP.' EQUALITY boole
 anMatch SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )
olcAttributeTypes: {26}( 2.16.840.1.113719.********** NAME 'dhcpDomainName' DE
 SC 'This is the name of the domain sent to the client by the server.  It is e
 ssentially the same as the value for DHCP option 15 sent to the client, and r
 epresents only the domain - not the full FQDN.  To obtain the full FQDN assig
 ned to the client you must prepend the "dhcpAssignedHostName" to this value w
 ith a ".".' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************ 
 SINGLE-VALUE )
olcAttributeTypes: {27}( 2.16.840.1.113719.********** NAME 'dhcpDnsStatus' DES
 C 'This indicates the status of updating DNS resource records on behalf of th
 e client by the DHCP server for this address.  The value is a 16-bit bitmask.
 ' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {28}( 2.16.840.1.113719.********** NAME 'dhcpRequestedHostN
 ame' DESC 'This is the hostname that was requested by the client.' EQUALITY c
 aseIgnoreIA5Match SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {29}( 2.16.840.1.113719.*********0 NAME 'dhcpAssignedHostNa
 me' DESC 'This is the actual hostname that was assigned to a client. It may n
 ot be the name that was requested by the client.  The fully qualified domain 
 name can be determined by appending the value of "dhcpDomainName" (with a dot
  separator) to this name.' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.146
 6.************ SINGLE-VALUE )
olcAttributeTypes: {30}( 2.16.840.1.113719.*********1 NAME 'dhcpReservedForCli
 ent' DESC 'The distinguished name of a "dhcpClient" that an address is reserv
 ed for.  This may not be the same as the "dhcpAssignedToClient" attribute if 
 the address is being reassigned but the current lease has not yet expired.' E
 QUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ SINGLE-VA
 LUE )
olcAttributeTypes: {31}( 2.16.840.1.113719.*********2 NAME 'dhcpAssignedToClie
 nt' DESC 'This is the distinguished name of a "dhcpClient" that an address is
  currently assigned to.  This attribute is only present in the class when the
  address is leased.' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.
 ************ SINGLE-VALUE )
olcAttributeTypes: {32}( 2.16.840.1.113719.*********3 NAME 'dhcpRelayAgentInfo
 ' DESC 'If the client request was received via a relay agent, this contains i
 nformation about the relay agent that was available from the DHCP request.  T
 his is a hex-encoded option value.' EQUALITY octetStringMatch SYNTAX *******.
 4.1.146***********.40 SINGLE-VALUE )
olcAttributeTypes: {33}( 2.16.840.1.113719.*********4 NAME 'dhcpHWAddress' DES
 C 'The clients hardware address that requested this IP address.' EQUALITY cas
 eIgnoreIA5Match SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {34}( 2.16.840.1.113719.*********5 NAME 'dhcpHashBucketAssi
 gnment' DESC 'HashBucketAssignment bit map for the DHCP Server, as defined in
  DHC Load Balancing Algorithm [RFC 3074].' EQUALITY octetStringMatch SYNTAX 1
 .*******.1.146***********.40 SINGLE-VALUE )
olcAttributeTypes: {35}( 2.16.840.1.113719.*********6 NAME 'dhcpDelayedService
 Parameter' DESC 'Delay in seconds corresponding to Delayed Service Parameter 
 configuration, as defined in  DHC Load Balancing Algorithm [RFC 3074]. ' EQUA
 LITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {36}( 2.16.840.1.113719.*********7 NAME 'dhcpMaxClientLeadT
 ime' DESC 'Maximum Client Lead Time configuration in seconds, as defined in D
 HCP Failover Protocol [FAILOVR]' EQUALITY integerMatch SYNTAX *******.4.1.146
 6.************ SINGLE-VALUE )
olcAttributeTypes: {37}( 2.16.840.1.113719.*********8 NAME 'dhcpFailOverEndpoi
 ntState' DESC 'Server (Failover Endpoint) state, as defined in DHCP Failover 
 Protocol [FAILOVR]' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.115.1
 21.1.26 SINGLE-VALUE )
olcAttributeTypes: {38}( 2.16.840.1.113719.*********9 NAME 'dhcpErrorLog' DESC
  'Generic error log attribute that allows logging error conditions within a d
 hcpService or a dhcpSubnet, like no IP addresses available for lease.' EQUALI
 TY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcAttributeTypes: {39}( 2.16.840.1.113719.*********0 NAME 'dhcpLocatorDN' DES
 C 'The DN of dhcpLocator object which contain the DNs of all DHCP configurati
 on objects. There will be a single dhcpLocator object in the tree with links 
 to all the DHCP objects in the tree' EQUALITY distinguishedNameMatch SYNTAX 1
 .*******.1.1466.************ )
olcAttributeTypes: {40}( 2.16.840.1.113719.*********1 NAME 'dhcpKeyAlgorithm' 
 DESC 'Algorithm to generate TSIG Key' EQUALITY caseIgnoreIA5Match SYNTAX 1.3.
 *******.1466.************ SINGLE-VALUE )
olcAttributeTypes: {41}( 2.16.840.1.113719.*********2 NAME 'dhcpKeySecret' DES
 C 'Secret to generate TSIG Key' EQUALITY octetStringMatch SYNTAX *******.4.1.
 146***********.40 SINGLE-VALUE )
olcAttributeTypes: {42}( 2.16.840.1.113719.*********3 NAME 'dhcpDnsZoneServer'
  DESC 'Master server of the DNS Zone' EQUALITY caseIgnoreIA5Match SYNTAX 1.3.
 *******.1466.************ SINGLE-VALUE )
olcAttributeTypes: {43}( 2.16.840.1.113719.*********4 NAME 'dhcpKeyDN' DESC 'T
 he DNs of TSIG Key to use in secure dynamic updates. In case of locator objec
 t, this will be list of TSIG keys.  In case of DHCP Service, Shared Network, 
 Subnet and DNS Zone, it will be a single key.' EQUALITY distinguishedNameMatc
 h SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {44}( 2.16.840.1.113719.*********5 NAME 'dhcpZoneDN' DESC '
 The DNs of DNS Zone. In case of locator object, this will be list of DNS Zone
 s in the tree. In case of DHCP Service, Shared Network and Subnet, it will be
  a single DNS Zone.' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.
 ************ )
olcAttributeTypes: {45}( 2.16.840.1.113719.*********6 NAME 'dhcpFailOverRole' 
 DESC 'Role of the DHCP Server. Either primary or secondary' EQUALITY caseIgno
 reIA5Match SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {46}( 2.16.840.1.113719.*********7 NAME 'dhcpFailOverReceiv
 eAddress' DESC 'IP address or DNS  name  on  which the server should listen f
 or connections from its fail over peer' EQUALITY caseIgnoreIA5Match SYNTAX 1.
 *******.1.1466.************ )
olcAttributeTypes: {47}( 2.16.840.1.113719.*********8 NAME 'dhcpFailOverPeerAd
 dress' DESC 'IP address  or  DNS  name  to which  the  server  should  connec
 t  to  reach  its fail over peer' EQUALITY caseIgnoreIA5Match SYNTAX *******.
 4.1.1466.************ )
olcAttributeTypes: {48}( 2.16.840.1.113719.*********9 NAME 'dhcpFailOverPeerPo
 rt' DESC 'Port to which server should connect to reach its fail over peer' EQ
 UALITY integerMatch SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {49}( 2.16.840.1.113719.*********0 NAME 'dhcpFailOverReceiv
 ePort' DESC 'Port on which server should listen for connections from its fail
  over peer' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {50}( 2.16.840.1.113719.*********1 NAME 'dhcpFailOverRespon
 seDelay' DESC 'Maximum response time in seconds, before Server assumes that c
 onnection to fail over peer has failed' EQUALITY integerMatch SYNTAX *******.
 4.1.1466.************ )
olcAttributeTypes: {51}( 2.16.840.1.113719.*********2 NAME 'dhcpFailOverUnpack
 edUpdates' DESC 'Number of BNDUPD messages that server can send before it rec
 eives BNDACK from its fail over peer' EQUALITY integerMatch SYNTAX *******.4.
 1.1466.************ )
olcAttributeTypes: {52}( 2.16.840.1.113719.*********3 NAME 'dhcpFailOverSplit'
  DESC 'Split between the primary and secondary servers for fail over purpose'
  EQUALITY integerMatch SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {53}( 2.16.840.1.113719.*********4 NAME 'dhcpFailOverLoadBa
 lanceTime' DESC 'Cutoff time in seconds, after which load balance is disabled
 ' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {54}( 2.16.840.1.113719.*********5 NAME 'dhcpFailOverPeerDN
 ' DESC 'The DNs of Fail over peers. In case of locator object, this will be l
 ist of fail over peers in the tree. In case of Subnet and pool, it will be a 
 single Fail Over Peer' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.146
 6.************ )
olcAttributeTypes: {55}( 2.16.840.1.113719.*********6 NAME 'dhcpServerDN' DESC
  'List of all  DHCP Servers in the tree. Used by dhcpLocatorObject' EQUALITY 
 distinguishedNameMatch SYNTAX *******.4.1.1466.************ )
olcAttributeTypes: {56}( 2.16.840.1.113719.*********7 NAME 'dhcpComments' DESC
  'Generic attribute that allows coments  within any DHCP object' EQUALITY cas
 eIgnoreIA5Match SYNTAX *******.4.1.1466.************ SINGLE-VALUE )
olcObjectClasses: {0}( 2.16.840.1.113719.********* NAME 'dhcpService' DESC 'Se
 rvice object that represents the actual DHCP Service configuration. This is a
  container object.' SUP top STRUCTURAL MUST cn MAY ( dhcpPrimaryDN $ dhcpSeco
 ndaryDN $ dhcpServerDN $ dhcpSharedNetworkDN $ dhcpSubnetDN $ dhcpGroupDN $ d
 hcpHostDN $ dhcpClassesDN $ dhcpOptionsDN $ dhcpZoneDN $ dhcpKeyDN $ dhcpFail
 OverPeerDN $ dhcpStatements $ dhcpComments $ dhcpOption ) )
olcObjectClasses: {1}( 2.16.840.1.113719.********* NAME 'dhcpSharedNetwork' DE
 SC 'This stores configuration information for a shared network.' SUP top STRU
 CTURAL MUST cn MAY ( dhcpSubnetDN $ dhcpPoolDN $ dhcpOptionsDN $ dhcpZoneDN $
  dhcpStatements $ dhcpComments $ dhcpOption ) X-NDS_CONTAINMENT 'dhcpService'
  )
olcObjectClasses: {2}( 2.16.840.1.113719.********* NAME 'dhcpSubnet' DESC 'Thi
 s class defines a subnet. This is a container object.' SUP top STRUCTURAL MUS
 T ( cn $ dhcpNetMask ) MAY ( dhcpRange $ dhcpPoolDN $ dhcpGroupDN $ dhcpHostD
 N $ dhcpClassesDN $ dhcpLeasesDN $ dhcpOptionsDN $ dhcpZoneDN $ dhcpKeyDN $ d
 hcpFailOverPeerDN $ dhcpStatements $ dhcpComments $ dhcpOption ) X-NDS_CONTAI
 NMENT ( 'dhcpService' 'dhcpSharedNetwork' ) )
olcObjectClasses: {3}( 2.16.840.1.113719.********* NAME 'dhcpPool' DESC 'This 
 stores configuration information about a pool.' SUP top STRUCTURAL MUST ( cn 
 $ dhcpRange ) MAY ( dhcpClassesDN $ dhcpPermitList $ dhcpLeasesDN $ dhcpOptio
 nsDN $ dhcpZoneDN $ dhcpKeyDN $ dhcpStatements $ dhcpComments $ dhcpOption ) 
 X-NDS_CONTAINMENT ( 'dhcpSubnet' 'dhcpSharedNetwork' ) )
olcObjectClasses: {4}( 2.16.840.1.113719.********* NAME 'dhcpGroup' DESC 'Grou
 p object that lists host DNs and parameters. This is a container object.' SUP
  top STRUCTURAL MUST cn MAY ( dhcpHostDN $ dhcpOptionsDN $ dhcpStatements $ d
 hcpComments $ dhcpOption ) X-NDS_CONTAINMENT ( 'dhcpSubnet' 'dhcpService' ) )
olcObjectClasses: {5}( 2.16.840.1.113719.********* NAME 'dhcpHost' DESC 'This 
 represents information about a particular client' SUP top STRUCTURAL MUST cn 
 MAY ( dhcpLeaseDN $ dhcpHWAddress $ dhcpOptionsDN $ dhcpStatements $ dhcpComm
 ents $ dhcpOption ) X-NDS_CONTAINMENT ( 'dhcpService' 'dhcpSubnet' 'dhcpGroup
 ' ) )
olcObjectClasses: {6}( 2.16.840.1.113719.********* NAME 'dhcpClass' DESC 'Repr
 esents information about a collection of related clients.' SUP top STRUCTURAL
  MUST cn MAY ( dhcpSubClassesDN $ dhcpOptionsDN $ dhcpStatements $ dhcpCommen
 ts $ dhcpOption ) X-NDS_CONTAINMENT ( 'dhcpService' 'dhcpSubnet' ) )
olcObjectClasses: {7}( 2.16.840.1.113719.********* NAME 'dhcpSubClass' DESC 'R
 epresents information about a collection of related classes.' SUP top STRUCTU
 RAL MUST cn MAY ( dhcpClassData $ dhcpOptionsDN $ dhcpStatements $ dhcpCommen
 ts $ dhcpOption ) X-NDS_CONTAINMENT 'dhcpClass' )
olcObjectClasses: {8}( 2.16.840.1.113719.********* NAME 'dhcpOptions' DESC 'Re
 presents information about a collection of options defined.' SUP top AUXILIAR
 Y MUST cn MAY ( dhcpOption $ dhcpComments ) X-NDS_CONTAINMENT ( 'dhcpService'
  'dhcpSharedNetwork' 'dhcpSubnet' 'dhcpPool' 'dhcpGroup' 'dhcpHost' 'dhcpClas
 s' ) )
olcObjectClasses: {9}( 2.16.840.1.113719.*********0 NAME 'dhcpLeases' DESC 'Th
 is class represents an IP Address, which may or may not have been leased.' SU
 P top STRUCTURAL MUST ( cn $ dhcpAddressState ) MAY ( dhcpExpirationTime $ dh
 cpStartTimeOfState $ dhcpLastTransactionTime $ dhcpBootpFlag $ dhcpDomainName
  $ dhcpDnsStatus $ dhcpRequestedHostName $ dhcpAssignedHostName $ dhcpReserve
 dForClient $ dhcpAssignedToClient $ dhcpRelayAgentInfo $ dhcpHWAddress $ dhcp
 Option ) X-NDS_CONTAINMENT ( 'dhcpService' 'dhcpSubnet' 'dhcpPool' ) )
olcObjectClasses: {10}( 2.16.840.1.113719.*********1 NAME 'dhcpLog' DESC 'This
  is the object that holds past information about the IP address. The cn is th
 e time/date stamp when the address was assigned or released, the address stat
 e at the time, if the address was assigned or released.' SUP top STRUCTURAL M
 UST cn MAY ( dhcpAddressState $ dhcpExpirationTime $ dhcpStartTimeOfState $ d
 hcpLastTransactionTime $ dhcpBootpFlag $ dhcpDomainName $ dhcpDnsStatus $ dhc
 pRequestedHostName $ dhcpAssignedHostName $ dhcpReservedForClient $ dhcpAssig
 nedToClient $ dhcpRelayAgentInfo $ dhcpHWAddress $ dhcpErrorLog ) X-NDS_CONTA
 INMENT ( 'dhcpLeases' 'dhcpPool' 'dhcpSubnet' 'dhcpSharedNetwork' 'dhcpServic
 e' ) )
olcObjectClasses: {11}( 2.16.840.1.113719.*********2 NAME 'dhcpServer' DESC 'D
 HCP Server Object' SUP top STRUCTURAL MUST cn MAY ( dhcpServiceDN $ dhcpLocat
 orDN $ dhcpVersion $ dhcpImplementation $ dhcpHashBucketAssignment $ dhcpDela
 yedServiceParameter $ dhcpMaxClientLeadTime $ dhcpFailOverEndpointState $ dhc
 pStatements $ dhcpComments $ dhcpOption ) X-NDS_CONTAINMENT ( 'organization' 
 'organizationalunit' 'domain' ) )
olcObjectClasses: {12}( 2.16.840.1.113719.*********3 NAME 'dhcpTSigKey' DESC '
 TSIG key for secure dynamic updates' SUP top STRUCTURAL MUST ( cn $ dhcpKeyAl
 gorithm $ dhcpKeySecret ) MAY dhcpComments X-NDS_CONTAINMENT ( 'dhcpService' 
 'dhcpSharedNetwork' 'dhcpSubnet' ) )
olcObjectClasses: {13}( 2.16.840.1.113719.********** NAME 'dhcpDnsZone' DESC '
 DNS Zone for updating leases' SUP top STRUCTURAL MUST ( cn $ dhcpDnsZoneServe
 r ) MAY ( dhcpKeyDN $ dhcpComments ) X-NDS_CONTAINMENT ( 'dhcpService' 'dhcpS
 haredNetwork' 'dhcpSubnet' ) )
olcObjectClasses: {14}( 2.16.840.1.113719.********** NAME 'dhcpFailOverPeer' D
 ESC 'This class defines the Fail over peer' SUP top STRUCTURAL MUST ( cn $ dh
 cpFailOverRole $ dhcpFailOverReceiveAddress $ dhcpFailOverPeerAddress $ dhcpF
 ailoverReceivePort $ dhcpFailOverPeerPort ) MAY ( dhcpFailOverResponseDelay $
  dhcpFailOverUnpackedUpdates $ dhcpMaxClientLeadTime $ dhcpFailOverSplit $ dh
 cpHashBucketAssignment $ dhcpFailOverLoadBalanceTime $ dhcpComments $ dhcpOpt
 ion ) X-NDS_CONTAINMENT ( 'dhcpService' 'dhcpSharedNetwork' 'dhcpSubnet' ) )
olcObjectClasses: {15}( 2.16.840.1.113719.********** NAME 'dhcpLocator' DESC '
 Locator object for DHCP configuration in the tree. There will be a single dhc
 pLocator object in the tree with links to all the DHCP objects in the tree' S
 UP top STRUCTURAL MUST cn MAY ( dhcpServiceDN $ dhcpServerDN $ dhcpSharedNetw
 orkDN $ dhcpSubnetDN $ dhcpPoolDN $ dhcpGroupDN $ dhcpHostDN $ dhcpClassesDN 
 $ dhcpKeyDN $ dhcpZoneDN $ dhcpFailOverPeerDN $ dhcpOption $ dhcpComments ) X
 -NDS_CONTAINMENT ( 'organization' 'organizationalunit' 'domain' ) )
structuralObjectClass: olcSchemaConfig
entryUUID: 1e41c53e-8c54-1034-9810-2f3e74c83a84
creatorsName: gidNumber=0+uidNumber=0,cn=peercred,cn=external,cn=auth
createTimestamp: 20150511180557Z
entryCSN: 20150511180557.672238Z#000000#000#000000
modifiersName: gidNumber=0+uidNumber=0,cn=peercred,cn=external,cn=auth
modifyTimestamp: 20150511180557Z
