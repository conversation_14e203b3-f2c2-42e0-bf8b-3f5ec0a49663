{"ei":0,"event":"user.create","uid":"7403a4fc-02d8-4cc6-8e86-fa5cf4b66fc9","code":"T1002I","time":"2025-01-11T20:03:54.052Z","cluster_name":"localhost","user":"0b5cefaa-8bb8-45ee-b71b-00ebf5f31e6d.localhost","user_kind":1,"name":"r00t","expires":"0001-01-01T00:00:00Z","roles":["access","auditor","editor"],"connector":"local","addr.remote":"127.0.0.1:59094"}
{"ei":0,"event":"reset_password_token.create","uid":"2a08d396-9fbf-4ef2-b985-3cce80f9e387","code":"T6000I","time":"2025-01-11T20:03:54.135Z","cluster_name":"localhost","name":"r00t","expires":"2025-01-11T21:03:54.134946846Z","ttl":"1h0m0s","user":"0b5cefaa-8bb8-45ee-b71b-00ebf5f31e6d.localhost","user_kind":1}
{"ei":0,"event":"mfa.add","uid":"17af660d-002c-48af-9d8d-a93e223faf41","code":"T1006I","time":"2025-01-11T20:05:14.936Z","cluster_name":"localhost","user":"r00t","user_kind":1,"mfa_device_name":"otp-device","mfa_device_uuid":"265dde03-2a6d-4798-a245-dbfcee8b1a43","mfa_device_type":"TOTP","addr.remote":"127.0.0.1:58498"}
{"ei":0,"event":"cert.create","uid":"a06d9f4f-2f32-4968-8b94-cd1f1f5ede43","code":"TC000I","time":"2025-01-11T20:05:15.039Z","cluster_name":"localhost","cert_type":"user","identity":{"user":"r00t","roles":["access","auditor","editor"],"logins":["r00t","root","-teleport-internal-join"],"expires":"2025-01-12T08:05:15.036945521Z","route_to_cluster":"localhost","traits":{"aws_role_arns":null,"azure_identities":null,"db_names":null,"db_roles":null,"db_users":null,"gcp_service_accounts":null,"host_user_gid":[""],"host_user_uid":[""],"kubernetes_groups":null,"kubernetes_users":null,"logins":["r00t","root"],"windows_logins":null},"teleport_cluster":"localhost","client_ip":"*************","prev_identity_expires":"0001-01-01T00:00:00Z","private_key_policy":"none"},"user_agent":"grpc-go/1.62.1"}
{"ei":0,"event":"user.login","uid":"cb59d55c-ab28-43cb-b97f-571cc4687c20","code":"T1000W","time":"2025-01-11T20:07:23.823Z","cluster_name":"localhost","user":"r00t","success":false,"error":"invalid username, password or second factor","method":"local","user_agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","addr.remote":"*************:30826"}
{"ei":0,"event":"user.login","uid":"18aec829-b941-46d6-a4e2-311a326f7e4d","code":"T1000W","time":"2025-01-11T20:07:34.706Z","cluster_name":"localhost","user":"r00t","success":false,"error":"invalid username, password or second factor","method":"local","user_agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","addr.remote":"*************:54811"}
{"ei":0,"event":"user.login","uid":"fec32ada-2332-4c37-8746-eb3bc3a7615c","code":"T1000W","time":"2025-01-11T20:07:45.536Z","cluster_name":"localhost","user":"r00t","success":false,"error":"invalid username, password or second factor","method":"local","user_agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","addr.remote":"*************:60911"}
{"ei":0,"event":"user.login","uid":"ce909891-5c24-4270-ac26-47e42be29878","code":"T1000I","time":"2025-01-11T20:07:51.706Z","cluster_name":"localhost","user":"r00t","required_private_key_policy":"none","success":true,"method":"local","mfa_device":{"mfa_device_name":"otp-device","mfa_device_uuid":"265dde03-2a6d-4798-a245-dbfcee8b1a43","mfa_device_type":"TOTP"},"user_agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","addr.remote":"*************:22265"}
{"ei":0,"event":"cert.create","uid":"a6bed604-3812-4c23-953e-545dea50159a","code":"TC000I","time":"2025-01-11T20:07:51.717Z","cluster_name":"localhost","cert_type":"user","identity":{"user":"r00t","roles":["access","auditor","editor"],"logins":["r00t","root","-teleport-internal-join"],"expires":"2025-01-12T08:07:51.715641191Z","route_to_cluster":"localhost","traits":{"aws_role_arns":null,"azure_identities":null,"db_names":null,"db_roles":null,"db_users":null,"gcp_service_accounts":null,"host_user_gid":[""],"host_user_uid":[""],"kubernetes_groups":null,"kubernetes_users":null,"logins":["r00t","root"],"windows_logins":null},"teleport_cluster":"localhost","client_ip":"*************","prev_identity_expires":"0001-01-01T00:00:00Z","private_key_policy":"none"}}
