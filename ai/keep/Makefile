.PHONY: help build dev

# Docker image name and tag
IMAGE:=registry.hak3.today/infra/fastgpt
TAG?=latest
# Shell that make should use
SHELL:=bash

# Namespace to deploy to
NAMESPACE:=keep

help:
# http://marmelab.com/blog/2016/02/29/auto-documented-makefile.html
	@grep -E '^[a-zA-Z0-9_%/-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

build: DARGS?=
release: ## Make the latest build of the image
	cd docker && docker build $(DARGS) --platform linux/amd64 --rm --no-cache -t  $(IMAGE):$(TAG) . --push

dev: ARGS?=
dev: PORT?=8090
dev: ## Make a container from a tagged image image
	docker run -it --rm -p $(PORT):$(PORT) $(IMAGE):$(TAG) $(ARGS)

clean:
	docker rmi -f $(IMAGE):$(TAG)

test:
	helm upgrade --install keep keephq/keep --namespace $(NAMESPACE) --create-namespace --dry-run

install:
	helm upgrade --install keep keephq/keep --namespace $(NAMESPACE) --create-namespace -f values.yaml

uninstall:
	helm uninstall keep --namespace $(NAMESPACE)
