# The GroupName here is used to identify your company or business unit that
# created this webhook.
# For example, this may be "acme.mycompany.com".
# This name will need to be referenced in each Issuer's `webhook` stanza to
# inform cert-manager of where to send ChallengePayload resources in order to
# solve the DNS01 challenge.
# This group name should be **unique**, hence using your own company's domain
# here is recommended.
groupName: hak3.today

certManager:
  namespace: cert-manager
  serviceAccountName: cert-manager

image:
  repository: registry.hak3.today/infra/cert-manager-webhook-dnspod
  tag: latest
  pullPolicy: Always

clusterIssuer:
  enabled: true
  name: dnspod
  ttl: 600
  staging: false
  secretId: "AKIDrZ8XkUWXWKyTm5FA1mP6JQM9oMrMjvx0"
  secretKey: "3uXBn5A1GHF4gDl7Odw86sVwFVcgcS5X"
  email: <EMAIL>

  # https://cert-manager.io/docs/configuration/acme/#adding-multiple-solver-types
  #selector:
  #  dnsZones:
  #    - 'example.com'

selector:
  dnsZones:
    - '*.hak3.today'

nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP
  port: 443

resources:
  {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #  cpu: 100m
  #  memory: 128Mi
  # requests:
  #  cpu: 100m
  #  memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}

namespace: "cert-manager"

cert-manager:
  enabled: false
  installCRDs: true
  prometheus:
    enabled: false
  namespace: cert-manager
