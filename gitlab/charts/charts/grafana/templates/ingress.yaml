{{- if .Values.ingress.enabled -}}
{{- $fullName := include "grafana.fullname" . -}}
{{- $servicePort := .Values.service.port -}}
{{- $ingressPath := .Values.ingress.path -}}
{{- $ingressPathType := .Values.ingress.pathType -}}
{{- $extraPaths := .Values.ingress.extraPaths -}}
{{- $newAPI := .Capabilities.APIVersions.Has "networking.k8s.io/v1/Ingress" -}}
{{- if $newAPI -}}
apiVersion: networking.k8s.io/v1
{{- else if .Capabilities.APIVersions.Has "networking.k8s.io/v1beta1/Ingress" }}
apiVersion: networking.k8s.io/v1beta1
{{- else }}
apiVersion: extensions/v1beta1
{{- end }}
kind: Ingress
metadata:
  name: {{ $fullName }}
  namespace: {{ template "grafana.namespace" . }}
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
{{- if .Values.ingress.labels }}
{{ toYaml .Values.ingress.labels | indent 4 }}
{{- end }}
  {{- if .Values.ingress.annotations }}
  annotations:
    {{- range $key, $value := .Values.ingress.annotations }}
    {{ $key }}: {{ tpl $value $ | quote }}
    {{- end }}
  {{- end }}
spec:
  {{- if .Values.ingress.ingressClassName }}
  ingressClassName: {{ .Values.ingress.ingressClassName }}
  {{- end -}}
{{- if .Values.ingress.tls }}
  tls:
{{ tpl (toYaml .Values.ingress.tls) $ | indent 4 }}
{{- end }}
  rules:
  {{- if .Values.ingress.hosts  }}
  {{- range .Values.ingress.hosts }}
    - host: {{ tpl . $}}
      http:
        paths:
{{ if $extraPaths }}
{{ toYaml $extraPaths | indent 10 }}
{{- end }}
          - path: {{ $ingressPath }}
            {{- if $newAPI }}
            pathType: {{ $ingressPathType }}
            {{- end }}
            backend:
              {{- if $newAPI }}
              service:
                name: {{ $fullName }}
                port:
                  number: {{ $servicePort }}
              {{- else }}
              serviceName: {{ $fullName }}
              servicePort: {{ $servicePort }}
              {{- end }}
  {{- end }}
  {{- else }}
    - http:
        paths:
          - backend:
              {{- if $newAPI }}
              service:
                name: {{ $fullName }}
                port:
                  number: {{ $servicePort }}
            pathType: {{ $ingressPathType }}
              {{- else }}
              serviceName: {{ $fullName }}
              servicePort: {{ $servicePort }}
              {{- end }}
          {{- if $ingressPath }}
            path: {{ $ingressPath }}
            {{- end }}
  {{- end -}}
{{- end }}
