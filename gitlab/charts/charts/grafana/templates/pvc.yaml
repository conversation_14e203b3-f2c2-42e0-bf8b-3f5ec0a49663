{{- if and .Values.persistence.enabled (not .Values.persistence.existingClaim) (eq .Values.persistence.type "pvc")}}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ template "grafana.fullname" . }}
  namespace: {{ template "grafana.namespace" . }}
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
  {{- with .Values.persistence.annotations  }}
  annotations:
{{ toYaml . | indent 4 }}
  {{- end }}
  {{- with .Values.persistence.finalizers  }}
  finalizers:
{{ toYaml . | indent 4 }}
  {{- end }}
spec:
  accessModes:
    {{- range .Values.persistence.accessModes }}
    - {{ . | quote }}
    {{- end }}
  resources:
    requests:
      storage: {{ .Values.persistence.size | quote }}
  {{- if .Values.persistence.storageClassName }}
  storageClassName: {{ .Values.persistence.storageClassName }}
  {{- end -}}
  {{- with .Values.persistence.selectorLabels }}
  selector:
    matchLabels:
{{ toYaml . | indent 6 }}
  {{- end }}
{{- end -}}
