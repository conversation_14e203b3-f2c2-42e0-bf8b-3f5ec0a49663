apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "grafana.fullname" . }}
  namespace: {{ template "grafana.namespace" . }}
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
{{- with .Values.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
{{- end }}
data:
{{- if .Values.plugins }}
  plugins: {{ join "," .Values.plugins }}
{{- end }}
  grafana.ini: |
{{- range $key, $value := index .Values "grafana.ini" }}
    [{{ $key }}]
    {{- range $elem, $elemVal := $value }}
    {{- if kindIs "invalid" $elemVal }}
    {{ $elem }} =
    {{- else if kindIs "string" $elemVal }}
    {{ $elem }} = {{ tpl $elemVal $ }}
    {{- else }}
    {{ $elem }} = {{ $elemVal }}
    {{- end }}
    {{- end }}
{{- end }}

{{- if .Values.datasources }}
{{ $root := . }}
  {{- range $key, $value := .Values.datasources }}
  {{ $key }}: |
{{ tpl (toYaml $value | indent 4) $root }}
  {{- end -}}
{{- end -}}

{{- if .Values.notifiers }}
  {{- range $key, $value := .Values.notifiers }}
  {{ $key }}: |
{{ toYaml $value | indent 4 }}
  {{- end -}}
{{- end -}}

{{- if .Values.dashboardProviders }}
  {{- range $key, $value := .Values.dashboardProviders }}
  {{ $key }}: |
{{ toYaml $value | indent 4 }}
  {{- end -}}
{{- end -}}

{{- if .Values.dashboards  }}
  download_dashboards.sh: |
    #!/usr/bin/env sh
    set -euf
    {{- if .Values.dashboardProviders }}
      {{- range $key, $value := .Values.dashboardProviders }}
        {{- range $value.providers }}
    mkdir -p {{ .options.path }}
        {{- end }}
      {{- end }}
    {{- end }}

  {{- range $provider, $dashboards := .Values.dashboards }}
    {{- range $key, $value := $dashboards }}
      {{- if (or (hasKey $value "gnetId") (hasKey $value "url")) }}
    curl -skf \
    --connect-timeout 60 \
    --max-time 60 \
      {{- if not $value.b64content }}
    -H "Accept: application/json" \
        {{- if $value.token }}
    -H "Authorization: token {{ $value.token }}" \
        {{- end }}
    -H "Content-Type: application/json;charset=UTF-8" \
      {{ end }}
    {{- if $value.url -}}"{{ $value.url }}"{{- else -}}"https://grafana.com/api/dashboards/{{ $value.gnetId }}/revisions/{{- if $value.revision -}}{{ $value.revision }}{{- else -}}1{{- end -}}/download"{{- end -}}{{ if $value.datasource }} | sed '/-- .* --/! s/"datasource":.*,/"datasource": "{{ $value.datasource }}",/g'{{ end }}{{- if $value.b64content -}} | base64 -d {{- end -}} \
    > "/var/lib/grafana/dashboards/{{ $provider }}/{{ $key }}.json"
      {{- end -}}
    {{- end }}
  {{- end }}
{{- end }}
