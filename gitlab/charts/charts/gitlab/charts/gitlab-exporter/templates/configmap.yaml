{{- if .Values.enabled -}}
{{- include "database.datamodel.prepare" . -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "fullname" . }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "gitlab.standardLabels" . | nindent 4 }}
    {{- include "gitlab.commonLabels" . | nindent 4 }}
data:
  gitlab-exporter.yml.erb: |
    server:
      name: webrick
      listen_address: 0.0.0.0
      listen_port: {{ .Values.service.internalPort }}

    probes:
      db_common: &db_common
        methods:
          - probe_db
        opts:
          {{- with $.Values.local.psql.main }}
          connection_string: dbname={{ template "gitlab.psql.database" . }} user={{ template "gitlab.psql.username" . }} host={{ template "gitlab.psql.host" .  }} port={{ template "gitlab.psql.port" . }} password='<%= File.read({{ template "gitlab.psql.password.file" . }}).strip.gsub(/[\'\\]/) { |esc| '\\' + esc } %>'
          {{- end }}
      database:
        multiple: true
        ci_builds:
          class_name: Database::CiBuildsProber
          <<: *db_common
        tuple_stats:
          class_name: Database::TuplesProber
          <<: *db_common
        rows_count:
          class_name: Database::RowCountProber
          <<: *db_common
      database_bloat:
        class_name: Database::BloatProber
        <<: *db_common

      sidekiq: &sidekiq
        methods:
          - probe_queues
          - probe_workers
          - probe_retries
          - probe_stats
        opts:
          redis_url: {{ template "gitlab.redis.url" . }}
          redis_enable_client: false

      ruby: &ruby
        methods:
          - probe_gc
        opts:
          quantiles: false

      metrics:
        multiple: true
        ruby:
          <<: *ruby
        sidekiq:
          <<: *sidekiq
        ci_builds:
          class_name: Database::CiBuildsProber
          <<: *db_common
        tuple_stats:
          class_name: Database::TuplesProber
          <<: *db_common
        rows_count:
          class_name: Database::RowCountProber
          <<: *db_common

  configure: |
    {{- include "gitlab.scripts.configure.secrets" (dict "required" " " "optional" "redis postgres") | nindent 4 }}
# Leave this here - This line denotes end of block to the parser.
{{- end }}
