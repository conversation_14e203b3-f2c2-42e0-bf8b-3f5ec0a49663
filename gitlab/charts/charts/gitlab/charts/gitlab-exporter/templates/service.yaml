{{- if .Values.enabled -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "fullname" . }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "gitlab.standardLabels" . | nindent 4 }}
    {{- include "gitlab.commonLabels" . | nindent 4 }}
    {{- include "gitlab.serviceLabels" . | nindent 4 }}
  annotations:
    {{- include "gitlab.serviceAnnotations" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.externalPort }}
      targetPort: {{ .Values.service.internalPort }}
      protocol: TCP
      name: gitlab-exporter
  selector:
    {{- include "gitlab.selectorLabels" . | nindent 4 }}
{{- end }}
