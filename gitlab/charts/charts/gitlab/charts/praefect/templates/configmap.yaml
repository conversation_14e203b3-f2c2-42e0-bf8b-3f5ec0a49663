apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "fullname" . }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "gitlab.standardLabels" . | nindent 4 }}
    {{- include "gitlab.commonLabels" . | nindent 4 }}
data:
  configure: |
    set -e
    mkdir -p /init-secrets/praefect
    for f in gitaly_token praefect_token db_password
    do
      cp -v -r -L /init-config/${f}  /init-secrets/praefect/${f}
    done
    {{- if $.Values.global.praefect.tls.enabled }}
    cp -v -L /init-config/praefect.crt /init-secrets/praefect/praefect.crt
    cp -v -L /init-config/praefect.key /init-secrets/praefect/praefect.key
    {{- end }}
  config.toml.erb: |
    <% gitaly_token = File.read('/etc/gitlab-secrets/praefect/gitaly_token').strip.to_json %>
    <% praefect_token = File.read('/etc/gitlab-secrets/praefect/praefect_token').strip.to_json %>
    # TCP address to listen on
    listen_addr = '0.0.0.0:{{ include "gitlab.praefect.internalPort" . }}'

    {{- if $.Values.global.praefect.tls.enabled }}
    tls_listen_addr = '0.0.0.0:{{ include "gitlab.praefect.tls.internalPort" . }}'
    {{- end }}

    {{- if .Values.metrics.enabled }}
    prometheus_listen_addr = '0.0.0.0:{{ .Values.metrics.port  }}'
    {{- end }}

    {{- if $.Values.global.praefect.tls.enabled }}
    [tls]
    certificate_path = '/etc/gitlab-secrets/praefect/praefect.crt'
    key_path = '/etc/gitlab-secrets/praefect/praefect.key'
    {{- end }}

    [failover]
    enabled = {{ .Values.failover.enabled }}
    election_strategy = '{{ .Values.electionStrategy }}'
    read_only_after_failover = {{ .Values.failover.readonlyAfter }}

    [auth]
    token = <%= praefect_token %>
    transitioning = false

    [logging]
    {{- with .Values.logging }}
    {{- if .level }}
    level = "{{ .level }}"
    {{- end }}
    {{- if .format }}
    format = "{{ .format }}"
    {{- end }}
    {{- if .sentryDsn }}
    sentry_dsn = "{{ .sentryDsn }}"
    {{- end }}
    {{- if .rubySentryDsn }}
    ruby_sentry_dsn = "{{ .rubySentryDsn }}"
    {{- end }}
    dir = "/var/log/gitaly"
    {{- if .sentryEnvironment }}
    sentry_environment = "{{ .sentryEnvironment }}"
    {{- end }}
    {{- end }}

    {{- $scheme := "tcp" -}}
    {{- $port := include "gitlab.gitaly.internalPort" $ -}}
    {{- if $.Values.global.gitaly.tls.enabled -}}
    {{-   $scheme = "tls" -}}
    {{-   $port = include "gitlab.gitaly.tls.internalPort" $ -}}
    {{- end -}}
    {{- $globalContext := $ }}
    {{ range $.Values.global.praefect.virtualStorages }}
    {{- $storageName := .name }}
    [[virtual_storage]]
    name = '{{ $storageName }}'
    {{- range until (.gitalyReplicas | int) }}
    [[virtual_storage.node]]
    {{- $serviceName := include "gitlab.praefect.gitaly.serviceName" (dict "context" $globalContext "name" $storageName) -}}
    {{- $podAddress := include "gitlab.praefect.gitaly.qualifiedServiceName" (dict "context" $globalContext "index" . "name" $storageName) }}
    storage = '{{ $serviceName }}-{{ . }}'
    address = '{{ printf "%s://%s.%s.svc:%s" $scheme $podAddress $globalContext.Release.Namespace $port }}'
    token = <%= gitaly_token %>
    {{- end }}
    {{ end }}

    [database]
    host = '{{ template "gitlab.praefect.psql.host" . }}'
    port = {{ template "gitlab.praefect.psql.port" . }}
    user = '{{ template "gitlab.praefect.psql.user" . }}'
    password = <%= File.read("/etc/gitlab-secrets/praefect/db_password").strip.to_json %>
    dbname = '{{ template "gitlab.praefect.psql.dbName" . }}'
    sslmode = '{{ $.Values.global.praefect.psql.sslMode }}'
