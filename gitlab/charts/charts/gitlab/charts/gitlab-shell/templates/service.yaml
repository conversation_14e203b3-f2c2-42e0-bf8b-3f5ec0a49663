{{- if .Values.enabled -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "fullname" . }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "gitlab.standardLabels" . | nindent 4 }}
    {{- include "gitlab.commonLabels" . | nindent 4 }}
    {{- include "gitlab.serviceLabels" . | nindent 4 }}
  annotations:
    {{- include "gitlab.serviceAnnotations" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- if .Values.metrics.enabled }}
    - port: {{ .Values.metrics.port }}
      targetPort: {{ .Values.metrics.port }}
      protocol: TCP
      name: metrics
    {{- end }}
    - port: {{ include "gitlab.shell.port" . | int }}
      targetPort: {{ .Values.service.internalPort }}
      protocol: TCP
      name: ssh
      {{- if and (eq .Values.service.type "NodePort") .Values.service.nodePort }}
      nodePort: {{ .Values.service.nodePort | int64 }}
      {{- end }}
  {{- if or (eq .Values.service.type "NodePort") (eq .Values.service.type "LoadBalancer") }}
  externalTrafficPolicy: {{ .Values.service.externalTrafficPolicy }}
  {{- end }}
{{- if .Values.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.service.loadBalancerIP }}
{{- end }}
{{- if .Values.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
  {{- range $cidr := .Values.service.loadBalancerSourceRanges }}
    - {{ $cidr }}
  {{- end }}
{{- end }}
{{- if .Values.service.externalIPs }}
  externalIPs:
    {{- toYaml .Values.service.externalIPs | nindent 4  }}
{{- end }}
  selector:
    {{- include "gitlab.selectorLabels" . | nindent 4 }}
{{- end }}
