{{- $imageCfg := dict "global" .Values.global.image "local" .Values.image -}}
{{- $initImageCfg := dict "global" .Values.global.busybox.image "local" .Values.init.image -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "fullname" . }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "gitlab.standardLabels" . | nindent 4 }}
    {{- include "gitlab.commonLabels" . | nindent 4 }}
  annotations:
    {{- include "gitlab.deploymentAnnotations" . | nindent 4 }}
spec:
  {{- if .Values.deployment.strategy }}
  strategy:
    {{- .Values.deployment.strategy | toYaml | nindent 4 }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "gitlab.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "gitlab.standardLabels" . | nindent 8 }}
        {{- include "gitlab.commonLabels" . | nindent 8 }}
        {{- include "gitlab.podLabels" . | nindent 8 }}
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yml") . | sha256sum }}
        cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
      {{- range $key, $value := .Values.annotations }}
        {{ $key }}: {{ $value | quote }}
      {{- end }}
      {{- if .Values.metrics.enabled }}
        {{- include "gitlab.pages.metricsAnnotations" . | nindent 8 }}
      {{- end }}
    spec:
      {{- include "gitlab.nodeSelector" . | nindent 6 }}
      {{- if .Values.tolerations }}
      tolerations:
        {{- toYaml .Values.tolerations | nindent 8 }}
      {{- end }}
      securityContext:
        runAsUser: {{ .Values.securityContext.runAsUser }}
        fsGroup: {{ .Values.securityContext.fsGroup }}
      {{- if eq (default .Values.global.antiAffinity .antiAffinity) "hard" }}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - topologyKey: {{ default .Values.global.affinity.podAntiAffinity.topologyKey .Values.affinity.podAntiAffinity.topologyKey | quote }}
              labelSelector:
                matchLabels:
                  {{- include "gitlab.selectorLabels" . | nindent 18 }}
      {{- else if eq (default .Values.global.antiAffinity .antiAffinity) "soft" }}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 1
            podAffinityTerm:
              topologyKey: {{ default .Values.global.affinity.podAntiAffinity.topologyKey .Values.affinity.podAntiAffinity.topologyKey | quote }}
              labelSelector:
                matchLabels:
                  {{- include "gitlab.selectorLabels" . | nindent 18 }}
      {{- end }}
      {{- if or .Values.serviceAccount.enabled $.Values.global.serviceAccount.enabled }}
      serviceAccountName: {{ include "gitlab.serviceAccount.name" . }}
      {{- end }}
      initContainers:
        {{- include "gitlab.extraInitContainers" . | nindent 8 }}
        {{- include "gitlab.certificates.initContainer" $ | nindent 8 }}
        - name: configure
          command: ['sh', '/config/configure']
          image: {{ include "gitlab.busybox.image" (dict "local" $.Values.init "global" $.Values.global.busybox) | quote }}
          {{- include "gitlab.image.pullPolicy" $initImageCfg | indent 10 }}
          env:
          {{- include "gitlab.extraEnv" $ | nindent 12 }}
          volumeMounts:
          {{- include "gitlab.extraVolumeMounts" $ | nindent 10 }}
          - name: pages-config
            mountPath: /config
            readOnly: true
          - name: init-pages-secrets
            mountPath: /init-config
            readOnly: true
          - name: pages-secrets
            mountPath: /init-secrets
            readOnly: false
          resources:
            {{- toYaml $.Values.init.resources | nindent 12 }}
      {{- include "gitlab.image.pullSecrets" $imageCfg | indent 6 }}
      containers:
        {{- include "gitlab.extraContainers" . | nindent 8 }}
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ coalesce .Values.image.tag (include "gitlab.parseAppVersion" (dict "appVersion" .Chart.AppVersion "prepend" "true")) }}"
          {{- include "gitlab.image.pullPolicy" $imageCfg | indent 10 }}
          env:
          {{- include "gitlab.extraEnv" $ | nindent 12 }}
            - name: CONFIG_TEMPLATE_DIRECTORY
              value: '/etc/gitlab-pages/templates'
            - name: CONFIG_DIRECTORY
              value: '/etc/gitlab-pages'
            - name: PAGES_CONFIG_FILE
              value: '/etc/gitlab-pages/config'
            - name: GITLAB_PAGES_LOG_FORMAT
              value: {{ .Values.logFormat | quote }}
          volumeMounts:
            {{- include "gitlab.extraVolumeMounts" $ | nindent 12 }}
            - name: pages-config
              mountPath: '/etc/gitlab-pages/templates'
            - name: pages-secrets
              mountPath: '/etc/gitlab-secrets'
              readOnly: true
            {{- include "gitlab.certificates.volumeMount" . | nindent 12 }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- if .Values.metrics.enabled }}
          ports:
            - containerPort: {{ .Values.metrics.port | int }}
              name: metrics
          {{- end }}
          # readinessProbe should always hit port used by `listen-http`, as k8s won't use TLS or ProxyV2
          {{- $externalHttp := $.Values.global.pages.externalHttp }}
          readinessProbe:
            httpGet:
              path: {{ $.Values.statusUri | quote }}
              port: {{ if $externalHttp }}{{ $.Values.service.internalPort | int }}{{ else }}9090{{ end }}
            initialDelaySeconds: {{ $.Values.deployment.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ $.Values.deployment.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ $.Values.deployment.readinessProbe.timeoutSeconds }}
            successThreshold: {{ $.Values.deployment.readinessProbe.successThreshold }}
            failureThreshold: {{ $.Values.deployment.readinessProbe.failureThreshold }}
      volumes:
      {{- include "gitlab.extraVolumes" $ | nindent 6 }}
      - name: pages-config
        configMap:
          name: {{ template "fullname" . }}
      - name: init-pages-secrets
        projected:
          defaultMode: 0400
          sources:
          {{- include "gitlab.pages.mountSecrets" $ | nindent 10 }}
          {{- include "gitlab.minio.mountSecrets" $ | nindent 10 }}
          {{- include "gitlab.appConfig.objectStorage.mountSecrets" (dict "name" "pages" "config" $.Values.global.pages.objectStore) | nindent 10 }}
          {{- if not (empty $.Values.global.pages.externalHttps) }}
          - secret:
              name: {{ template "pages.tlsSecret" . }}
              items:
                - key: tls.crt
                  path: pages/{{ template "gitlab.pages.hostname" $ }}.crt
                - key: tls.key
                  path: pages/{{ template "gitlab.pages.hostname" $ }}.key
          {{- end }}
          {{- if eq $.Values.global.pages.accessControl true }}
          - secret:
              name: {{ template "oauth.gitlab-pages.secret" . }}
              items:
                - key: {{ template "oauth.gitlab-pages.appIdKey" . }}
                  path: pages/gitlab_appid
                - key: {{ template "oauth.gitlab-pages.appSecretKey" . }}
                  path: pages/gitlab_appsecret
          - secret:
              name: {{ template "gitlab.pages.authSecret.secret" . }}
              items:
                - key: {{ template "gitlab.pages.authSecret.key" . }}
                  path: pages/auth_secret
          {{- end }}
      - name: pages-secrets
        emptyDir:
          medium: "Memory"
      {{- include "gitlab.certificates.volumes" . | nindent 6 }}
