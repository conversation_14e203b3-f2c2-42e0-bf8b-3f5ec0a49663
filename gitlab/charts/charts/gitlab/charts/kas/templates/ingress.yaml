{{- if .Values.global.kas.enabled -}}
{{- if eq (include "gitlab.ingress.enabled" $) "true" -}}
{{- $hostname := include "gitlab.kas.hostname" . -}}
{{- $tlsSecret := include "kas.tlsSecret" . -}}
{{- $ingressCfg := dict "global" .Values.global.ingress "local" .Values.ingress "context" . -}}
apiVersion: {{ template "gitlab.ingress.apiVersion" $ingressCfg }}
kind: Ingress
metadata:
  name: {{ template "fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "gitlab.standardLabels" . | nindent 4 }}
    {{- include "gitlab.commonLabels" . | nindent 4 }}
  annotations:
    {{ include "ingress.class.annotation" $ingressCfg }}
    kubernetes.io/ingress.provider: "{{ template "gitlab.ingress.provider" $ingressCfg }}"
    {{ include "gitlab.certmanager_annotations" . }}
  {{- range $key, $value := merge .Values.ingress.annotations .Values.global.ingress.annotations }}
    {{ $key }}: {{ $value | quote }}
  {{- end }}
spec:
  {{ include "ingress.class.field" $ingressCfg }}
  rules:
    - host: {{ $hostname }}
      http:
        paths:
          - path: "{{ trimSuffix "/" $.Values.ingress.agentPath }}{{ $.Values.global.ingress.path }}"
            {{ if or ($.Capabilities.APIVersions.Has "networking.k8s.io/v1/Ingress") (eq $.Values.global.ingress.apiVersion "networking.k8s.io/v1") -}}
            pathType: {{ default "Prefix" $.Values.global.ingress.pathType }}
            backend:
              service:
                  name: {{ template "gitlab.kas.serviceName" . }}
                  port:
                    number: {{ .Values.service.externalPort }}
            {{- else -}}
            backend:
              serviceName: {{ template "gitlab.kas.serviceName" . }}
              servicePort: {{ .Values.service.externalPort }}
            {{- end }}
          - path: "{{ trimSuffix "/" $.Values.ingress.k8sApiPath }}{{ $.Values.global.ingress.path }}"
            {{ if or ($.Capabilities.APIVersions.Has "networking.k8s.io/v1/Ingress") (eq $.Values.global.ingress.apiVersion "networking.k8s.io/v1") -}}
            pathType: {{ default "Prefix" $.Values.global.ingress.pathType }}
            backend:
              service:
                name: {{ template "gitlab.kas.serviceName" . }}
                port:
                  number: {{ .Values.service.kubernetesApiPort }}
            {{- else -}}
            backend:
              serviceName: {{ template "gitlab.kas.serviceName" . }}
              servicePort: {{ .Values.service.kubernetesApiPort }}
            {{- end }}
  {{- if (and $tlsSecret (eq (include "gitlab.ingress.tls.enabled" $) "true" )) }}
  tls:
    - hosts:
      - {{ $hostname }}
      secretName: {{ $tlsSecret }}
  {{- else }}
  tls: []
  {{- end }}
{{- end -}}
{{- end -}}
