{{- if .Values.enabled -}}
{{- include "webservice.datamodel.prepare" $ -}}
{{/* BEGIN range deployments */}}
{{- range $.Values.deployments -}}
{{/*
From here on:
- `.` is `.deployments.xyz` value
- `.name` is the key (xyz)
*/}}
---
apiVersion: policy/v1beta1
kind: PodDisruptionBudget
metadata:
  name: {{ template "webservice.fullname.withSuffix" . }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "gitlab.standardLabels" $ | nindent 4 }}
    {{- include "webservice.labels" . | nindent 4 }}
    {{- include "webservice.commonLabels" . | nindent 4 }}
spec:
  maxUnavailable: {{ .pdb.maxUnavailable }}
  selector:
    matchLabels:
      {{- include "gitlab.selectorLabels" $ | nindent 6 }}
      {{ include "webservice.labels" . | nindent 6 }}
{{- end }}
{{/* END range deployments */}}
{{- end }}
