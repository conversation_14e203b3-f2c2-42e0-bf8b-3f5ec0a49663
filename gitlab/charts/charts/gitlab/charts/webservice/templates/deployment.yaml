{{- if $.Values.enabled }}
{{- $imageCfg := dict "global" $.Values.global.image "local" $.Values.image -}}
{{- $initImageCfg := dict "global" $.Values.global.busybox.image "local" $.Values.init.image -}}
{{- include "database.datamodel.prepare" $ -}}
{{- include "webservice.datamodel.prepare" $ -}}
{{/* BEGIN range deployments */}}
{{- range $.Values.deployments -}}
{{/*
From here on:
- `.` is `.deployments.xyz` value
- `.name` is the key (xyz)
*/}}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "webservice.fullname.withSuffix" . }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "gitlab.standardLabels" $ | nindent 4 }}
    {{- include "webservice.labels" . | nindent 4 }}
    {{- include "webservice.commonLabels" . | nindent 4 }}
    {{- if .deployment.labels -}}
    {{-   toYaml .deployment.labels | nindent 4 }}
    {{- end }}
  annotations:
    {{- include "gitlab.deploymentAnnotations" $ | nindent 4 }}
    {{- if .deployment.annotations -}}
    {{-   toYaml .deployment.annotations | nindent 4 }}
    {{- end }}
spec:
  # Don't provide replicas when HPA are present
  # replicas: {{ .hpa.minReplicas }}
  selector:
    matchLabels:
      {{- include "gitlab.selectorLabels" $ | nindent 6 }}
      {{ include "webservice.labels" . | nindent 6 }}
  {{- if .deployment.strategy }}
  strategy:
    {{- .deployment.strategy | toYaml | nindent 4 }}
  {{- end }}
  template:
    metadata:
      labels:
        {{- include "gitlab.standardLabels" $ | nindent 8 }}
        {{- include "webservice.labels" . | nindent 8 }}
        {{- include "gitlab.podLabels" $ | nindent 8 }}
        {{- include "webservice.commonLabels" . | nindent 8 }}
        {{- include "webservice.podLabels" . | nindent 8 }}
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yml") $ | sha256sum }}
        cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
      {{- range $key, $value := .pod.annotations }}
        {{ $key }}: {{ $value | quote }}
      {{- end }}
      {{- if $.Values.metrics.enabled }}
        {{- toYaml $.Values.metrics.annotations | nindent 8 }}
      {{- end }}
    spec:
      {{- if .tolerations }}
      tolerations:
        {{- toYaml .tolerations | nindent 8 }}
      {{- end }}
      {{- $nodeSelectors := dict "Values" (dict "global" (dict "nodeSelector" $.Values.global.nodeSelector) "nodeSelector" .nodeSelector) -}}
      {{- include "gitlab.nodeSelector" $nodeSelectors | nindent 6 }}
      {{- if $.Values.priorityClassName }}
      priorityClassName: {{ $.Values.priorityClassName }}
      {{- end }}
      securityContext:
        runAsUser: {{ $.Values.securityContext.runAsUser }}
        fsGroup: {{ $.Values.securityContext.fsGroup }}
      {{- if eq (default $.Values.global.antiAffinity $.Values.antiAffinity) "hard" }}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - topologyKey: {{ default $.Values.global.affinity.podAntiAffinity.topologyKey $.Values.affinity.podAntiAffinity.topologyKey | quote }}
              labelSelector:
                matchLabels:
                  {{- include "gitlab.selectorLabels" $ | nindent 18 }}
                  {{- include "webservice.labels" . | nindent 18}}
      {{- else if eq (default $.Values.global.antiAffinity $.Values.antiAffinity) "soft" }}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 1
            podAffinityTerm:
              topologyKey: {{ default $.Values.global.affinity.podAntiAffinity.topologyKey $.Values.affinity.podAntiAffinity.topologyKey | quote }}
              labelSelector:
                matchLabels:
                  {{- include "gitlab.selectorLabels" $ | nindent 18 }}
                  {{- include "webservice.labels" . | nindent 18}}
      {{- end }}
      {{- if or $.Values.serviceAccount.enabled $.Values.global.serviceAccount.enabled }}
      serviceAccountName: {{ include "gitlab.serviceAccount.name" $ }}
      {{- end }}
      automountServiceAccountToken: false
      initContainers:
        {{- include "gitlab.extraInitContainers" $ | nindent 8 }}
        {{- include "gitlab.certificates.initContainer" $ | nindent 8 }}
        - name: configure
          command: ['sh']
          args: [ '-c', 'sh -x /config-webservice/configure ; sh -x /config-workhorse/configure ; mkdir -p -m 3770 /tmp/gitlab']
          image: {{ include "gitlab.busybox.image" (dict "local" $.Values.init "global" $.Values.global.busybox) | quote }}
          {{- include "gitlab.image.pullPolicy" $initImageCfg | indent 10 }}
          env:
            {{- include "webservice.extraEnv" (dict "global" $.Values.global "local" .) | nindent 12 }}
          volumeMounts:
          {{- include "gitlab.extraVolumeMounts" $ | nindent 10 }}
          {{- include "gitlab.psql.ssl.volumeMount" $ | nindent 10 }}
          {{- include "gitlab.geo.psql.ssl.volumeMount" $ | nindent 10 }}
          - name: webservice-config
            mountPath: /config-webservice
            readOnly: true
          - name: workhorse-config
            mountPath: /config-workhorse
            readOnly: true
          - name: init-webservice-secrets
            mountPath: /init-config
            readOnly: true
          - name: webservice-secrets
            mountPath: /init-secrets
            readOnly: false
          - name: workhorse-secrets
            mountPath: /init-secrets-workhorse
            readOnly: false
          - name: shared-tmp
            mountPath: /tmp
            readOnly: false
          resources:
            {{- toYaml $.Values.init.resources | nindent 12 }}
        - name: dependencies
          image: {{ include "webservice.image" $ }}
          {{- include "gitlab.image.pullPolicy" $imageCfg | indent 10 }}
          args:
            - /scripts/wait-for-deps
          env:
            - name: GITALY_FEATURE_DEFAULT_ON
              value: "1"
            - name: CONFIG_TEMPLATE_DIRECTORY
              value: '/var/opt/gitlab/templates'
            - name: CONFIG_DIRECTORY
              value: '/srv/gitlab/config'
            - name: WORKHORSE_ARCHIVE_CACHE_DISABLED
              value: "1"
            {{- if $.Values.global.rails.bootsnap.enabled }}
            - name: ENABLE_BOOTSNAP
              value: "1"
            {{- end }}
            {{- include "webservice.extraEnv" (dict "global" $.Values.global "local" .) | nindent 12 }}
          volumeMounts:
            {{- include "gitlab.extraVolumeMounts" $ | nindent 12 }}
            {{- include "gitlab.certificates.volumeMount" $ | nindent 12 }}
            - name: webservice-config
              mountPath: '/var/opt/gitlab/templates'
            - name: webservice-secrets
              mountPath: '/etc/gitlab'
              readOnly: true
            - name: webservice-secrets
              mountPath: /srv/gitlab/config/secrets.yml
              subPath: rails-secrets/secrets.yml
              readOnly: true
          resources:
            {{- toYaml $.Values.init.resources | nindent 12 }}
      {{- include "gitlab.image.pullSecrets" $imageCfg | indent 6 }}
      containers:
        {{- include "gitlab.extraContainers" $ | nindent 8 }}
        - name: {{ $.Chart.Name }}
          image: {{ include "webservice.image" $ }}
          {{- include "gitlab.image.pullPolicy" $imageCfg | indent 10 }}
          ports:
            - containerPort: {{ $.Values.service.internalPort }}
              name: webservice
          env:
            - name: GITLAB_WEBSERVER
              value: {{ $.Values.webServer }}
            - name: TMPDIR
              value: "/tmp/gitlab"
            - name: GITALY_FEATURE_DEFAULT_ON
              value: "1"
            - name: CONFIG_TEMPLATE_DIRECTORY
              value: '/var/opt/gitlab/templates'
            - name: CONFIG_DIRECTORY
              value: '/srv/gitlab/config'
            {{- if $.Values.metrics.enabled }}
            - name: prometheus_multiproc_dir
              value: /metrics
            {{- end }}
            {{- if $.Values.global.rails.bootsnap.enabled }}
            - name: ENABLE_BOOTSNAP
              value: "1"
            {{- end }}
            - name: WORKER_PROCESSES
              value: "{{ .workerProcesses }}"
            - name: WORKER_TIMEOUT
              value: "{{ $.Values.global.webservice.workerTimeout }}"
            - name: INTERNAL_PORT
              value: "{{ $.Values.service.internalPort }}"
            {{- if eq $.Values.webServer "puma" }}
            - name: PUMA_THREADS_MIN
              value: "{{ .puma.threads.min }}"
            - name: PUMA_THREADS_MAX
              value: "{{ .puma.threads.max }}"
            - name: PUMA_WORKER_MAX_MEMORY
              value: "{{ .puma.workerMaxMemory }}"
            - name: DISABLE_PUMA_WORKER_KILLER
              value: "{{ .puma.disableWorkerKiller }}"
            {{- end }}
            - name: SHUTDOWN_BLACKOUT_SECONDS
              value: "{{ .shutdown.blackoutSeconds }}"
            {{- include "gitlab.tracing.env" $ | nindent 12 }}
            {{- if $.Values.global.tracing.urlTemplate }}
            - name: GITLAB_TRACING_URL
              value: {{ $.Values.global.tracing.urlTemplate | quote }}
            {{- end }}
            - name: WORKHORSE_ARCHIVE_CACHE_DISABLED
              value: "true"
            {{- include "webservice.extraEnv" (dict "global" $.Values.global "local" .) | nindent 12 }}
          volumeMounts:
            {{- if .sshHostKeys.mount }}
            - name: {{ .sshHostKeys.mountName }}
              mountPath: /etc/ssh
            {{- end }}
            {{- if $.Values.metrics.enabled }}
            - name: webservice-metrics
              mountPath: '/metrics'
            {{- end }}
            - name: webservice-config
              mountPath: '/var/opt/gitlab/templates'
            - name: webservice-secrets
              mountPath: '/etc/gitlab'
              readOnly: true
            - name: webservice-secrets
              mountPath: /srv/gitlab/config/secrets.yml
              subPath: rails-secrets/secrets.yml
            - name: webservice-config
              mountPath: '/srv/gitlab/config/initializers/smtp_settings.rb'
              subPath: smtp_settings.rb
            - name: webservice-config
              mountPath: '/srv/gitlab/INSTALLATION_TYPE'
              subPath: installation_type
            - name: shared-upload-directory
              mountPath: /srv/gitlab/public/uploads/tmp
              readOnly: false
            - name: shared-tmp
              mountPath: '/tmp'
              readOnly: false
            {{- if $.Values.global.email.smime.enabled }}
            - name: smime-creds
              mountPath: /home/<USER>/gitlab
              readOnly: true
            {{- end }}
            {{- include "gitlab.certificates.volumeMount" $ | nindent 12 }}
            {{- include "gitlab.extraVolumeMounts" $ | nindent 12 }}
          livenessProbe:
            httpGet:
              path: /-/liveness
              port: {{ $.Values.service.internalPort }}
            initialDelaySeconds: {{ .deployment.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .deployment.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .deployment.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .deployment.livenessProbe.successThreshold }}
            failureThreshold: {{ .deployment.livenessProbe.failureThreshold }}
          readinessProbe:
            httpGet:
              path: /-/readiness
              port: {{ $.Values.service.internalPort }}
            initialDelaySeconds: {{ .deployment.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .deployment.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .deployment.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .deployment.readinessProbe.successThreshold }}
            failureThreshold: {{ .deployment.readinessProbe.failureThreshold }}
          lifecycle:
            preStop:
              exec:
                command: ["/bin/bash", "-c", "pkill -SIGINT -o ruby"]
          resources:
            {{- toYaml .resources | nindent 12 }}
        - name: gitlab-workhorse
          image: "{{ coalesce $.Values.workhorse.image (include "workhorse.repository" $) }}:{{ coalesce $.Values.workhorse.tag (include "gitlab.versionTag" $ ) }}"
          {{- include "gitlab.image.pullPolicy" $imageCfg | indent 10 }}
          ports:
            - containerPort: {{ $.Values.service.workhorseInternalPort }}
              name: workhorse
          env:
            - name: TMPDIR
              value: "/tmp/gitlab"
            - name: GITLAB_WORKHORSE_EXTRA_ARGS
              value: {{ .workhorse.extraArgs | quote }}
            - name: GITLAB_WORKHORSE_LISTEN_PORT
              value: {{ default 8181 $.Values.service.workhorseInternalPort | int | quote }}
            - name: GITLAB_WORKHORSE_LOG_FORMAT
              value: {{ .workhorse.logFormat | quote }}
            - name: CONFIG_TEMPLATE_DIRECTORY
              value: '/var/opt/gitlab/templates'
            - name: CONFIG_DIRECTORY
              value: '/srv/gitlab/config'
            {{- if .workhorse.monitoring.exporter.enabled }}
            - name: GITLAB_WORKHORSE_PROM_LISTEN_ADDR
              value: "0.0.0.0:{{ $.Values.workhorse.monitoring.exporter.port }}"
            {{- end }}
            {{- if .workhorse.sentryDSN }}
            - name: GITLAB_WORKHORSE_SENTRY_DSN
              value: {{ .workhorse.sentryDSN }}
            {{- end }}
            {{- include "gitlab.tracing.env" $ | nindent 12 }}
            {{- include "webservice.extraEnv" (dict "global" $.Values.global "local" .) | nindent 12 }}
          volumeMounts:
            - name: workhorse-config
              mountPath: '/var/opt/gitlab/templates'
            - name: workhorse-secrets
              mountPath: '/etc/gitlab'
              readOnly: true
            - name: shared-upload-directory
              mountPath: /srv/gitlab/public/uploads/tmp
              readOnly: false
            - name: shared-tmp
              mountPath: '/tmp'
              readOnly: false
            {{- include "gitlab.certificates.volumeMount" $ | nindent 12 }}
            {{- include "gitlab.extraVolumeMounts" $ | nindent 12 }}
          livenessProbe:
            exec:
              command:
              - /scripts/healthcheck
            initialDelaySeconds: {{ .workhorse.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .workhorse.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .workhorse.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .workhorse.livenessProbe.successThreshold }}
            failureThreshold: {{ .workhorse.livenessProbe.failureThreshold }}
          readinessProbe:
            exec:
              command:
              - /scripts/healthcheck
            initialDelaySeconds: {{ .workhorse.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .workhorse.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .workhorse.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .workhorse.readinessProbe.successThreshold }}
            failureThreshold: {{ .workhorse.readinessProbe.failureThreshold }}
          resources:
            {{- toYaml .workhorse.resources | nindent 12 }}
      {{- if .deployment.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .deployment.terminationGracePeriodSeconds | int }}
      {{- end }}
      volumes:
      {{- if .sshHostKeys.mount }}
      {{- include "webservice.sshHostKeys.volume" (dict "local" . "Release" $.Release "Values" $.Values) | nindent 6 }}
      {{- end }}
      {{- include "gitlab.extraVolumes" $ | nindent 6 }}
      {{- include "gitlab.psql.ssl.volume" $ | nindent 6 }}
      {{- include "gitlab.geo.psql.ssl.volume" $ | nindent 6 }}
      - name: shared-tmp
        {{- include "gitlab.volume.emptyDir" .sharedTmpDir | nindent 8 }}
      {{- if $.Values.metrics.enabled }}
      - name: webservice-metrics
        emptyDir:
          medium: "Memory"
      {{- end }}
      - name: webservice-config
        configMap:
          name: {{ .fullname }}
      - name: workhorse-config
        configMap:
            name: {{ $.Release.Name }}-workhorse-{{ .name }}
      - name: init-webservice-secrets
        projected:
          defaultMode: 0400
          sources:
          - secret:
              name: {{ template "gitlab.rails-secrets.secret" $ }}
              items:
                - key: secrets.yml
                  path: rails-secrets/secrets.yml
          - secret:
              name: {{ template "gitlab.gitlab-shell.authToken.secret" $ }}
              items:
                - key: {{ template "gitlab.gitlab-shell.authToken.key" $ }}
                  path: shell/.gitlab_shell_secret
          {{- include "gitlab.appConfig.incomingEmail.mountSecrets" $ | nindent 10 }}
          {{- include "gitlab.appConfig.serviceDeskEmail.mountSecrets" $ | nindent 10 }}
          {{- include "gitlab.gitaly.clientSecrets" $ | nindent 10 }}
          {{- include "gitlab.redis.secrets" $ | nindent 10 }}
          {{- range $.Values.local.psql }}
          {{-   include "gitlab.psql.secret" . | nindent 10 }}
          {{- end }}
          {{- if include "gitlab.geo.secondary" $ }}
          - secret:
              name: {{ template "gitlab.geo.psql.password.secret" $ }}
              items:
                - key: {{ template "gitlab.geo.psql.password.key" $ }}
                  path: postgres/geo-psql-password
          {{- end }}
          - secret:
              name: {{ template "gitlab.registry.certificate.secret" $ }}
              items:
                - key: registry-auth.key
                  path: registry/gitlab-registry.key
          {{- include "gitlab.registry.notificationSecret.mount" $ | nindent 10 }}
          - secret:
              name: {{ template "gitlab.workhorse.secret" $ }}
              items:
                - key: {{ template "gitlab.workhorse.key" $ }}
                  path: gitlab-workhorse/secret
          {{- include "gitlab.pages.mountSecrets" $ | nindent 10 }}
          {{- include "gitlab.kas.mountSecrets" $ | nindent 10 }}
          {{- include "gitlab.minio.mountSecrets" $ | nindent 10 }}
          {{- include "gitlab.appConfig.objectStorage.mountSecrets" (dict "name" "object_store" "config" $.Values.global.appConfig.object_store) | nindent 10 }}
          {{- include "gitlab.appConfig.objectStorage.mountSecrets" (dict "name" "artifacts" "config" $.Values.global.appConfig.artifacts) | nindent 10 }}
          {{- include "gitlab.appConfig.objectStorage.mountSecrets" (dict "name" "lfs" "config" $.Values.global.appConfig.lfs) | nindent 10 }}
          {{- include "gitlab.appConfig.objectStorage.mountSecrets" (dict "name" "uploads" "config" $.Values.global.appConfig.uploads) | nindent 10 }}
          {{- include "gitlab.appConfig.objectStorage.mountSecrets" (dict "name" "packages" "config" $.Values.global.appConfig.packages) | nindent 10 }}
          {{- include "gitlab.appConfig.objectStorage.mountSecrets" (dict "name" "external_diffs" "config" $.Values.global.appConfig.externalDiffs) | nindent 10 }}
          {{- include "gitlab.appConfig.objectStorage.mountSecrets" (dict "name" "terraform_state" "config" $.Values.global.appConfig.terraformState) | nindent 10 }}
          {{- include "gitlab.appConfig.objectStorage.mountSecrets" (dict "name" "dependency_proxy" "config" $.Values.global.appConfig.dependencyProxy) | nindent 10 }}
          {{- include "gitlab.appConfig.objectStorage.mountSecrets" (dict "name" "pages" "config" $.Values.global.pages.objectStore) | nindent 10 }}
          {{- include "gitlab.appConfig.ldap.servers.mountSecrets" $ | nindent 10 }}
          {{- include "gitlab.appConfig.omniauth.mountSecrets" $ | nindent 10 }}
          {{- if and $.Values.global.smtp.enabled $.Values.global.smtp.authentication }}
          - secret:
              name: {{ $.Values.global.smtp.password.secret | required "Missing required secret containing the SMTP password. Make sure to set `global.smtp.password.secret`" }}
              items:
                - key: {{ $.Values.global.smtp.password.key }}
                  path: smtp/smtp-password
          {{- end }}
          {{- if and $.Values.global.appConfig.smartcard.enabled $.Values.global.appConfig.smartcard.CASecret }}
          - secret:
              name: {{ $.Values.global.appConfig.smartcard.CASecret }}
              items:
                - key: "ca.crt"
                  path: rails-secrets/smartcard-ca.crt
          {{- end }}
     {{- if $.Values.global.email.smime.enabled }}
      - name: smime-creds
        secret:
          secretName: {{ $.Values.global.email.smime.secretName }}
          items:
            - key: {{ $.Values.global.email.smime.keyName }}
              path: .gitlab_smime_key
            - key: {{ $.Values.global.email.smime.certName }}
              path: .gitlab_smime_cert
      {{- end }}
      - name: webservice-secrets
        emptyDir:
          medium: "Memory"
      - name: workhorse-secrets
        emptyDir:
          medium: "Memory"
      - name: shared-upload-directory
        {{- include "gitlab.volume.emptyDir" .sharedUploadDir | nindent 8 }}
      {{- include "gitlab.certificates.volumes" $ | nindent 6 }}
{{- end }}{{/* END range deployments */}}
{{- end }}
