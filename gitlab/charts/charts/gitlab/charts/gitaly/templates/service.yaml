{{- if eq (include "gitlab.gitaly.includeInternalResources" $) "true" -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "gitlab.gitaly.serviceName" . }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "gitlab.standardLabels" . | nindent 4 }}
    {{- include "gitlab.commonLabels" . | nindent 4 }}
    {{- include "gitlab.serviceLabels" . | nindent 4 }}
  annotations:
    {{- include "gitlab.serviceAnnotations" . | nindent 4 }}
{{ include (print $.Template.BasePath "/_service_spec.yaml") . }}
{{- end }}
