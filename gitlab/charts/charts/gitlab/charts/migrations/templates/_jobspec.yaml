{{- if .Values.enabled }}
{{- $imageCfg := dict "global" .Values.global.image "local" .Values.image -}}
{{- $initImageCfg := dict "global" .Values.global.busybox.image "local" .Values.init.image -}}
{{- include "database.datamodel.prepare" . -}}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ template "migrations.jobname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "gitlab.standardLabels" . | nindent 4 }}
    {{- include "gitlab.commonLabels" . | nindent 4 }}
spec:
  activeDeadlineSeconds: {{ .Values.activeDeadlineSeconds }}
  backoffLimit: {{ .Values.backoffLimit }}
  template:
    metadata:
      {{- if .Values.annotations }}
      annotations:
      {{- range $key, $value := .Values.annotations }}
        {{ $key }}: {{ $value | quote }}
      {{- end }}
      {{- end }}
      labels:
        {{- include "gitlab.standardLabels" . | nindent 8 }}
        {{- include "gitlab.commonLabels" . | nindent 8 }}
        {{- include "gitlab.podLabels" . | nindent 8 }}
    spec:
      {{- if .Values.tolerations }}
      tolerations:
        {{- toYaml .Values.tolerations | nindent 8 }}
      {{- end }}
      securityContext:
        runAsUser: {{ .Values.securityContext.runAsUser }}
        fsGroup: {{ .Values.securityContext.fsGroup }}
      {{- if or .Values.serviceAccount.enabled .Values.global.serviceAccount.enabled }}
      serviceAccountName: {{ include "gitlab.serviceAccount.name" . }}
      {{- end }}
      automountServiceAccountToken: false
      initContainers:
        {{- include "gitlab.extraInitContainers" . | nindent 8 }}
        {{- include "gitlab.certificates.initContainer" . | nindent 8 }}
        - name: configure
          command: ['sh', '/config/configure']
          image: {{ include "gitlab.busybox.image" (dict "local" .Values.init "global" $.Values.global.busybox) | quote }}
          {{- include "gitlab.image.pullPolicy" $initImageCfg | indent 10 }}
          env:
          {{- include "gitlab.extraEnv" . | nindent 10 }}
          volumeMounts:
          {{- include "gitlab.extraVolumeMounts" . | nindent 10 }}
          {{- include "gitlab.psql.ssl.volumeMount" . | nindent 10 }}
          {{- include "gitlab.geo.psql.ssl.volumeMount" . | nindent 10 }}
          - name: migrations-config
            mountPath: /config
            readOnly: true
          - name: init-migrations-secrets
            mountPath: /init-config
            readOnly: true
          - name: migrations-secrets
            mountPath: /init-secrets
            readOnly: false
          resources:
            {{- toYaml .Values.init.resources | nindent 12 }}
      restartPolicy: OnFailure
      {{- include "gitlab.image.pullSecrets" $imageCfg | indent 6 }}
      containers:
        {{- include "gitlab.extraContainers" . | nindent 8 }}
        - name: {{ .Chart.Name }}
          image: "{{ coalesce .Values.image.repository (include "image.repository" .) }}:{{ coalesce .Values.image.tag (include "gitlab.versionTag" . ) }}"
          args:
            - /scripts/wait-for-deps
          {{- if include "gitlab.geo.secondary" $ }}
            - /scripts/geo-db-migrate
          {{- else }}
            - /scripts/db-migrate
          {{- end}}
          {{- include "gitlab.image.pullPolicy" $imageCfg | indent 10 }}
          env:
            - name: CONFIG_TEMPLATE_DIRECTORY
              value: '/var/opt/gitlab/templates'
            - name: CONFIG_DIRECTORY
              value: '/srv/gitlab/config'
            - name: BYPASS_SCHEMA_VERSION
              value: 'true'
            {{- if include "gitlab.geo.secondary" $ }}
            - name: DB_SCHEMA_TARGET
              value: 'geo'
            {{- end }}
            {{- if .Values.global.rails.bootsnap.enabled }}
            - name: ENABLE_BOOTSNAP
              value: '1'
            {{- end }}
            {{- include "gitlab.extraEnv" . | nindent 12 }}
          volumeMounts:
            - name: migrations-config
              mountPath: '/var/opt/gitlab/templates'
            - name: migrations-secrets
              mountPath: '/etc/gitlab'
              readOnly: true
            - name: migrations-secrets
              mountPath: /srv/gitlab/config/secrets.yml
              subPath: rails-secrets/secrets.yml
            - name: migrations-secrets
              mountPath: /srv/gitlab/config/initial_root_password
              subPath: migrations/initial_root_password
            - name: migrations-secrets
              mountPath: /srv/gitlab/config/gitlab_shared_runners_registration_token
              subPath: migrations/gitlab_shared_runners_registration_token
            {{- if .Values.global.gitlab.license.secret }}
            - name: migrations-secrets
              mountPath: /srv/gitlab/config/Gitlab.gitlab-license
              subPath: migrations/enterprise_license
            {{- end }}
            {{- include "gitlab.extraVolumeMounts" . | nindent 12 }}
            {{- include "gitlab.certificates.volumeMount" . | nindent 12 }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      volumes:
      {{- include "gitlab.extraVolumes" . | nindent 6 }}
      {{- include "gitlab.psql.ssl.volume" . | nindent 6 }}
      {{- include "gitlab.geo.psql.ssl.volume" . | nindent 6 }}
      - name: migrations-config
        configMap:
          name: {{ template "fullname" . }}
      - name: init-migrations-secrets
        projected:
          defaultMode: 0400
          sources:
          {{- if eq $.Values.global.pages.accessControl true }}
          - secret:
              name: {{ template "oauth.gitlab-pages.secret" . }}
              items:
                - key: {{ template "oauth.gitlab-pages.appIdKey" . }}
                  path: oauth-secrets/gitlab-pages/appid
                - key: {{ template "oauth.gitlab-pages.appSecretKey" . }}
                  path: oauth-secrets/gitlab-pages/appsecret
          - configMap:
              name: {{ template "fullname" . }}
              items:
                - key: pages_redirect_uri
                  path: oauth-secrets/gitlab-pages/redirecturi
          {{- end }}
          - secret:
              name: {{ template "gitlab.rails-secrets.secret" . }}
              items:
                - key: secrets.yml
                  path: rails-secrets/secrets.yml
          {{- include "gitlab.gitaly.clientSecrets" . | nindent 10 }}
          {{- include "gitlab.redis.secrets" . | nindent 10 }}
          {{- range $.Values.local.psql }}
          {{-   include "gitlab.psql.secret" . | nindent 10 }}
          {{- end }}
          {{- if include "gitlab.geo.secondary" $ }}
          - secret:
              name: {{ template "gitlab.geo.psql.password.secret" . }}
              items:
                - key: {{ template "gitlab.geo.psql.password.key" . }}
                  path: postgres/geo-psql-password
          {{- end }}
          - secret:
              name: {{ template "gitlab.migrations.initialRootPassword.secret" . }}
              items:
                - key: {{ template "gitlab.migrations.initialRootPassword.key" . }}
                  path: migrations/initial_root_password
          - secret:
              name: {{ template "gitlab.gitlab-runner.registrationToken.secret" . }}
              items:
                - key: runner-registration-token
                  path: migrations/gitlab_shared_runners_registration_token
          {{- if .Values.global.gitlab.license.secret }}
          - secret:
              name: {{ .Values.global.gitlab.license.secret }}
              items:
                - key: {{ template "gitlab.migrations.license.key" . }}
                  path: migrations/enterprise_license
          {{- end }}
      - name: migrations-secrets
        emptyDir:
          medium: "Memory"
      {{- include "gitlab.certificates.volumes" . | nindent 6 }}
      {{- include "gitlab.nodeSelector" . | nindent 6 }}
{{- end }}
