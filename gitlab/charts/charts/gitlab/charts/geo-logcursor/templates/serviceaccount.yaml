{{- $enabled := or .Values.serviceAccount.enabled .Values.global.serviceAccount.enabled -}}
{{- $create := or .Values.serviceAccount.create .Values.global.serviceAccount.create -}}
{{- if and $enabled $create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "gitlab.serviceAccount.name" . }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "gitlab.standardLabels" . | nindent 4 }}
    {{- include "gitlab.commonLabels" . | nindent 4 }}
  {{- with default .Values.serviceAccount.annotations .Values.global.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
