{{- if .Values.rbac.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    {{- range $key, $value := .Values.rbac.serviceAccountAnnotations }}
    {{   $key }}: {{ $value | quote }}
    {{- end }}
  name: {{ include "gitlab-runner.fullname" . }}
  labels:
    app: {{ include "gitlab-runner.fullname" . }}
    chart: {{ include "gitlab-runner.chart" . }}
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
{{- if .Values.rbac.imagePullSecrets }}
imagePullSecrets:
  {{- range .Values.rbac.imagePullSecrets }}
  - name: {{ . | quote }}
  {{- end }}
{{- end }}
{{- end -}}
