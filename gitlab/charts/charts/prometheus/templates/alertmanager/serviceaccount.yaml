{{- if and .Values.alertmanager.enabled .Values.serviceAccounts.alertmanager.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    {{- include "prometheus.alertmanager.labels" . | nindent 4 }}
  name: {{ template "prometheus.serviceAccountName.alertmanager" . }}
{{ include "prometheus.namespace" . | indent 2 }}
  annotations:
{{ toYaml .Values.serviceAccounts.alertmanager.annotations | indent 4 }}
{{- end -}}
