{{- if and .Values.server.enabled .Values.rbac.create (empty .Values.server.namespaces) (empty .Values.server.useExistingClusterRoleName) -}}
apiVersion: {{ template "rbac.apiVersion" . }}
kind: ClusterRoleBinding
metadata:
  labels:
    {{- include "prometheus.server.labels" . | nindent 4 }}
  name: {{ template "prometheus.server.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ template "prometheus.serviceAccountName.server" . }}
{{ include "prometheus.namespace" . | indent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ template "prometheus.server.fullname" . }}
{{- end }}
