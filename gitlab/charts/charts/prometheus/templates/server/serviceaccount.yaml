{{- if .Values.server.enabled -}}
{{- if .Values.serviceAccounts.server.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    {{- include "prometheus.server.labels" . | nindent 4 }}
  name: {{ template "prometheus.serviceAccountName.server" . }}
{{ include "prometheus.namespace" . | indent 2 }}
  annotations:
{{ toYaml .Values.serviceAccounts.server.annotations | indent 4 }}
{{- end }}
{{- end }}
