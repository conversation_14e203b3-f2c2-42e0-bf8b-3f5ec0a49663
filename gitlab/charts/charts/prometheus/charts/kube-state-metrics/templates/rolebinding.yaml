{{- if and (eq  .Values.rbac.create true) (eq .Values.rbac.useClusterRole false) -}}
{{- range (split "," $.Values.namespaces) }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    {{- include "kube-state-metrics.labels" . | indent 4 }}
  name: {{ template "kube-state-metrics.fullname" $ }}
  namespace: {{ . }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
{{- if (not $.Values.rbac.useExistingRole) }}
  name: {{ template "kube-state-metrics.fullname" $ }}
{{- else }}
  name: {{ $.Values.rbac.useExistingRole }}
{{- end }}
subjects:
- kind: ServiceAccount
  name: {{ template "kube-state-metrics.serviceAccountName" $ }}
  namespace: {{ template "kube-state-metrics.namespace" $ }}
{{- end -}}
{{- end -}}
