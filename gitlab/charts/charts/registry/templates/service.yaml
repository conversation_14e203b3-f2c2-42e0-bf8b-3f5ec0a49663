{{- if .Values.enabled -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "registry.fullname" . }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "gitlab.standardLabels" . | nindent 4 }}
    {{- include "gitlab.commonLabels" . | nindent 4 }}
    {{- include "gitlab.serviceLabels" . | nindent 4 }}
  annotations:
    {{- include "gitlab.serviceAnnotations" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
{{- if (and (eq .Values.service.type "ClusterIP") (not (empty .Values.service.clusterIP))) }}
  clusterIP: {{ .Values.service.clusterIP }}
{{- end }}
{{- if (and (eq .Values.service.type "LoadBalancer") (not (empty .Values.service.loadBalancerIP))) }}
  loadBalancerIP: {{ .Values.service.loadBalancerIP }}
{{- end }}
  ports:
  - port: {{ .Values.service.externalPort }}
    targetPort: {{ .Values.service.internalPort }}
    protocol: TCP
    name: {{ .Values.service.name }}
  {{- if eq .Values.debug.prometheus.enabled true }}
  - port: {{ .Values.debug.addr.port }}
    targetPort: {{ .Values.debug.addr.port }}
    protocol: TCP
    name: {{ .Values.service.name }}-prometheus
  {{- end }}
  selector:
    app: {{ template "name" . }}
    release: {{ .Release.Name }}
{{- end -}}
