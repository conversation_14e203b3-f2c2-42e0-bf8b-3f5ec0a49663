{{- if not .Values.sentinel.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "redis.fullname" . }}-master
  namespace: {{ .Release.Namespace | quote }}
  labels:
    app: {{ template "redis.name" . }}
    chart: {{ template "redis.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    {{- if .Values.master.service.labels -}}
    {{- toYaml .Values.master.service.labels | nindent 4 }}
    {{- end -}}
{{- if .Values.master.service.annotations }}
  annotations: {{- toYaml .Values.master.service.annotations | nindent 4 }}
{{- end }}
spec:
  type: {{ .Values.master.service.type }}
  {{- if and (eq .Values.master.service.type "LoadBalancer") .Values.master.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.master.service.loadBalancerIP }}
  {{- end }}
  {{- if and (eq .Values.master.service.type "LoadBalancer") .Values.master.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
  {{- with .Values.master.service.loadBalancerSourceRanges }}
{{- toYaml . | nindent 4 }}
{{- end }}
  {{- end }}
  ports:
    - name: redis
      port: {{ .Values.master.service.port }}
      targetPort: redis
      {{- if .Values.master.service.nodePort }}
      nodePort: {{ .Values.master.service.nodePort }}
      {{- end }}
  selector:
    app: {{ template "redis.name" . }}
    release: {{ .Release.Name }}
    role: master
{{- end }}
