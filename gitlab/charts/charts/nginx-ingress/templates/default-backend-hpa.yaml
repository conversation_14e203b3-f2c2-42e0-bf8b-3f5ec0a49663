{{- if and .Values.defaultBackend.enabled .Values.defaultBackend.autoscaling.enabled }}
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  labels:
    {{- include "gitlab.standardLabels" . | nindent 4 }}
    {{- include "gitlab.commonLabels" . | nindent 4 }}
    component: "{{ .Values.defaultBackend.name }}"
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: default-backend
  name: {{ template "ingress-nginx.defaultBackend.fullname" . }}
  namespace: {{ .Release.Namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ template "ingress-nginx.defaultBackend.fullname" . }}
  minReplicas: {{ .Values.defaultBackend.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.defaultBackend.autoscaling.maxReplicas }}
  metrics:
{{- with .Values.defaultBackend.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        targetAverageUtilization: {{ . }}
{{- end }}
{{- with .Values.defaultBackend.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        targetAverageUtilization: {{ . }}
{{- end }}
{{- end }}
