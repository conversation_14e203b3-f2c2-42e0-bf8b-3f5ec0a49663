{{- if .Values.global.minio.enabled -}}
{{- if eq (include "gitlab.ingress.enabled" $) "true" -}}
{{- $minioHostname := include "gitlab.minio.hostname" . -}}
{{- $tlsSecret := include "minio.tlsSecret" . -}}
{{- $ingressCfg := dict "global" .Values.global.ingress "local" .Values.ingress "context" . -}}

apiVersion: {{ template "gitlab.ingress.apiVersion" $ingressCfg }}
kind: Ingress
metadata:
  name: {{ template "minio.fullname" . }}
  namespace: {{ $.Release.Namespace }}
  labels:
{{- include "gitlab.standardLabels" . | nindent 4 }}
{{- include "gitlab.commonLabels" . | nindent 4 }}
  annotations:
    {{ include "ingress.class.annotation" $ingressCfg }}
    kubernetes.io/ingress.provider: "{{ template "gitlab.ingress.provider" $ingressCfg }}"
    {{- if eq "nginx" .Values.global.ingress.provider }}
    nginx.ingress.kubernetes.io/proxy-body-size: {{ .Values.ingress.proxyBodySize | quote }}
    nginx.ingress.kubernetes.io/proxy-read-timeout: {{ .Values.ingress.proxyReadTimeout | quote }}
    nginx.ingress.kubernetes.io/proxy-request-buffering: {{ .Values.ingress.proxyBuffering | quote }}
    nginx.ingress.kubernetes.io/proxy-buffering: {{ .Values.ingress.proxyBuffering | quote }}
    {{- end }}
    {{ include "gitlab.certmanager_annotations" . }}
  {{- range $key, $value := merge .Values.ingress.annotations .Values.global.ingress.annotations }}
    {{ $key }}: {{ $value | quote }}
  {{- end }}
spec:
  {{ include "ingress.class.field" $ingressCfg }}
  rules:
    - host: {{ $minioHostname }}
      http:
        paths:
          - path: {{ coalesce $.Values.ingress.path $.Values.global.ingress.path }}
            {{ if or ($.Capabilities.APIVersions.Has "networking.k8s.io/v1/Ingress") (eq $.Values.global.ingress.apiVersion "networking.k8s.io/v1") -}}
            pathType: {{ default "Prefix" $.Values.global.ingress.pathType }}
            backend:
              service:
                  name: {{ template "minio.fullname" . }}-svc
                  port:
                    number: {{ .Values.servicePort }}
           {{- else -}}
            backend:
              serviceName: {{ template "minio.fullname" . }}-svc
              servicePort: {{ .Values.servicePort }}
            {{- end -}}
  {{- if (and $tlsSecret (eq (include "gitlab.ingress.tls.enabled" $) "true" )) }}
  tls:
    - hosts:
      - {{ $minioHostname }}
      secretName: {{ $tlsSecret }}
  {{- else }}
  tls: []
  {{- end }}
{{- end -}}
{{- end -}}
