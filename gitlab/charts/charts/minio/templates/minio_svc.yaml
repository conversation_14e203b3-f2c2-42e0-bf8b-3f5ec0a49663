{{- if .Values.global.minio.enabled -}}
kind: Service
apiVersion: v1
metadata:
  name: {{ template "minio.fullname" . }}-svc
  namespace: {{ $.Release.Namespace }}
  labels:
{{- include "gitlab.standardLabels" . | nindent 4 }}
{{- include "gitlab.commonLabels" . | nindent 4 }}
  annotations:
{{ include "gitlab.serviceAnnotations" . | indent 4 }}
spec:
  type: {{ .Values.serviceType }}
  {{- if eq .Values.serviceType "LoadBalancer" }}
  loadBalancerIP: {{ default "" .Values.minioLoadBalancerIP }}
  {{- end }}
  selector:
    app: {{ template "minio.name" . }}
    release: {{ .Release.Name }}
    component: app
  ports:
    - name: service
      port: 9000
      targetPort: {{ .Values.servicePort }}
      protocol: TCP
{{- end }}
