## Set default image, imageTag, and imagePullPolicy.
## Distributed Minio ref: https://docs.minio.io/docs/distributed-minio-quickstart-guide
##
image: "minio/minio"
imageTag: "RELEASE.2017-12-28T01-21-00Z"
# imagePullPolicy: IfNotPresent
# pullSecrets: []

minioMc:
  image: minio/mc
  tag: "RELEASE.2018-07-13T00-53-22Z"

ingress:
  apiVersion:
  enabled:
  proxyReadTimeout: 900
  proxyBodySize: "0"
  proxyBuffering: "off"
  tls: {}
    # secretName:
    # enabled: true
  annotations: {}
  configureCertmanager:
  path: # /

## Support for tolerations for pod scheduling
tolerations: []

global:
  ingress:
    enabled:
    annotations: {}
    tls: {}
      # secretName:
      # enabled: true
  hosts:
    domain: example.com
    hostSuffix:
    https: true
    tls: {}
      # secretName:
    minio: {}
      #name: minio.example.com
      #https: false
  minio:
    enabled: true

common:
  labels: {}

## Control the InitContainer behaviors.
init:
  image: {}
    # repository:
    # tag:
    # pullPolicy: IfNotPresent
  script: |-
    sed -e 's@ACCESS_KEY@'"$(cat /config/accesskey)"'@' -e 's@SECRET_KEY@'"$(cat /config/secretkey)"'@' /config/config.json > /minio/config.json
  resources:
    requests:
      cpu: 50m
## Set default Minio config file path, volume mount path and
## number of nodes (only used for Minio distributed mode)
## Distributed Minio ref: https://docs.minio.io/docs/distributed-minio-quickstart-guide
##
configPath: ""
mountPath: "/export"
replicas: 4

## loadBalancerIP for the Minio Service (optional, cloud specific)
## ref: http://kubernetes.io/docs/user-guide/services/#type-loadbalancer
##
# minioLoadBalancerIP:

## Enable persistence using Persistent Volume Claims
## ref: http://kubernetes.io/docs/user-guide/persistent-volumes/
##
persistence:
  enabled: true

  ## minio data Persistent Volume Storage Class
  ## If defined, storageClassName: <storageClass>
  ## If set to "-", storageClassName: "", which disables dynamic provisioning
  ## If undefined (the default) or set to null, no storageClassName spec is
  ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
  ##   GKE, AWS & OpenStack)
  ##
  # storageClass: "-"
  accessMode: ReadWriteOnce
  size: 10Gi

  ## If subPath is set mount a sub folder of a volume instead of the root of the volume.
  ## This is especially handy for volume plugins that don't natively support sub mounting (like glusterfs).
  ##
  subPath: ""

  ## if volumeName is set, use this existing PersistentVolume
  # volumeName:

  ## Only bind to a volume with the following exactly matched labels with values.
  ## ref: https://kubernetes.io/docs/concepts/storage/persistent-volumes/#selector
  matchLabels: {}

  ## Only bind to a volume with the following exppression matched labels.
  ## ref: https://kubernetes.io/docs/concepts/storage/persistent-volumes/#selector
  matchExpressions: []

## Expose the Minio service to be accessed from outside the cluster (LoadBalancer service).
## or access it from within the cluster (ClusterIP service). Set the service type and the port to serve it.
## ref: http://kubernetes.io/docs/user-guide/services/
##
serviceType: ClusterIP
servicePort: 9000

## Node labels for pod assignment
## Ref: https://kubernetes.io/docs/user-guide/node-selection/
##
nodeSelector: {}

## Configure resource requests and limits
## ref: http://kubernetes.io/docs/user-guide/compute-resources/
##
resources:
  requests:
    memory: 128Mi
    cpu: 100m

# Additational pod annotations
podAnnotations: {}

# Additional pod labels
podLabels: {}

## Create a buckets after minio install
## An array of items, with name, policy, and purge
defaultBuckets:
  - name: registry
  - name: git-lfs
  - name: runner-cache
  - name: gitlab-uploads
  - name: gitlab-artifacts
  - name: gitlab-backups
  - name: gitlab-packages
  - name: tmp
  - name: gitlab-pseudo
  - name: gitlab-mr-diffs
  - name: gitlab-terraform-state
  - name: gitlab-dependency-proxy
  - name: gitlab-pages
  #
  ## If enabled, must be a string with length > 0
  # - name: example
  ## Can be one of none|download|upload|public, defaults to `none`
  #   policy: none
  ## Can be omitted. If present, and `true`, will `rm -rf` the bucket
  #   purge: false

## https://docs.minio.io/docs/minio-bucket-notification-guide
##
minioConfig:
  region: "us-east-1"
  browser: "on"
  domain: ""
  logger:
    console:
      enable: true
    file:
      enable: false
      filename: ""
  aqmp:
    enable: false
    url: ""
    exchange: ""
    routingKey: ""
    exchangeType: ""
    deliveryMode: 0
    mandatory: false
    immediate: false
    durable: false
    internal: false
    noWait: false
    autoDeleted: false
  nats:
    enable: false
    address: ""
    subject: ""
    username: ""
    password: ""
    token: ""
    secure: false
    pingInterval: 0
    enableStreaming: false
    clusterID: ""
    clientID: ""
    async: false
    maxPubAcksInflight: 0
  elasticsearch:
    enable: false
    format: "namespace"
    url: ""
    index: ""
  redis:
    enable: false
    format: "namespace"
    address: ""
    password: ""
    key: ""
  postgresql:
    enable: false
    format: "namespace"
    connectionString: ""
    table: ""
    host: ""
    port: ""
    user: ""
    password: ""
    database: ""
  kafka:
    enable: false
    brokers: "null"
    topic: ""
  webhook:
    enable: false
    endpoint: ""
  mysql:
    enable: false
    format: "namespace"
    dsnString: ""
    table: ""
    host: ""
    port: ""
    user: ""
    password: ""
    database: ""
  mqtt:
    enable: false
    broker: ""
    topic: ""
    qos: 0
    clientId: ""
    username: ""
    password: ""
networkPolicy:
  enabled: false
  allowExternal: true
maxUnavailable: 1

## Allow to overwrite under which User and Group we're running.
securityContext:
  runAsUser: 1000
  fsGroup: 1000

deployment:
  strategy:
    type: Recreate
    rollingUpdate: null
