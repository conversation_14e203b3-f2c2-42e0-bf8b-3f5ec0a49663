{{- if .Values.webhook.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.webhook.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ template "webhook.serviceAccountName" . }}
  namespace: {{ .Release.Namespace | quote }}
  {{- if .Values.webhook.serviceAccount.annotations }}
  annotations:
{{ toYaml .Values.webhook.serviceAccount.annotations | indent 4 }}
  {{- end }}
  labels:
    app: {{ include "webhook.name" . }}
    app.kubernetes.io/name: {{ include "webhook.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: "webhook"
    {{- include "labels" . | nindent 4 }}
{{- if .Values.global.imagePullSecrets }}
imagePullSecrets: {{ toYaml .Values.global.imagePullSecrets | nindent 2 }}
{{- end -}}
{{- end -}}
