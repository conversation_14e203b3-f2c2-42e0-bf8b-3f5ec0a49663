{{- if .Values.cainjector.enabled -}}
{{- if .Values.cainjector.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.cainjector.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ template "cainjector.serviceAccountName" . }}
  namespace: {{ .Release.Namespace | quote }}
  {{- if .Values.cainjector.serviceAccount.annotations }}
  annotations:
{{ toYaml .Values.cainjector.serviceAccount.annotations | indent 4 }}
  {{- end }}
  labels:
    app: {{ include "cainjector.name" . }}
    app.kubernetes.io/name: {{ include "cainjector.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: "cainjector"
    {{- include "labels" . | nindent 4 }}
{{- if .Values.global.imagePullSecrets }}
imagePullSecrets: {{ toYaml .Values.global.imagePullSecrets | nindent 2 }}
{{- end }}
{{- end -}}
{{- end -}}
