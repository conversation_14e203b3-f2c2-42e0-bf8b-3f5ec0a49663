{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
{{- if .Values.global.imagePullSecrets }}
imagePullSecrets: {{ toYaml .Values.global.imagePullSecrets | nindent 2 }}
{{- end }}
automountServiceAccountToken: {{ .Values.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ template "cert-manager.serviceAccountName" . }}
  namespace: {{ .Release.Namespace | quote }}
  {{- if .Values.serviceAccount.annotations }}
  annotations:
{{ toYaml .Values.serviceAccount.annotations | indent 4 }}
  {{- end }}
  labels:
    app: {{ include "cert-manager.name" . }}
    app.kubernetes.io/name: {{ include "cert-manager.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: "controller"
    {{- include "labels" . | nindent 4 }}
{{- end }}
