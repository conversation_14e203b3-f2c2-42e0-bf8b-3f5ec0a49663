{{- if .Values.psp.create }}
apiVersion: {{ include "podsecuritypolicy.apiVersion" . }}
kind: PodSecurityPolicy
metadata:
  name: {{ template "postgresql.fullname" . }}
  labels:
    app: {{ template "postgresql.name" . }}
    chart: {{ template "postgresql.chart" . }}
    release: {{ .Release.Name | quote }}
    heritage: {{ .Release.Service | quote }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "postgresql.tplValue" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  privileged: false
  volumes:
    - 'configMap'
    - 'secret'
    - 'persistentVolumeClaim'
    - 'emptyDir'
    - 'projected'
  hostNetwork: false
  hostIPC: false
  hostPID: false
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  supplementalGroups:
    rule: 'MustRunAs'
    ranges:
      - min: 1
        max: 65535
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: 1
        max: 65535
  readOnlyRootFilesystem: false
{{- end }}
