{{- if and (or (.Files.Glob "files/conf.d/*.conf") .Values.postgresqlExtendedConf) (not .Values.extendedConfConfigMap)}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "postgresql.fullname" . }}-extended-configuration
  labels:
    app: {{ template "postgresql.name" . }}
    chart: {{ template "postgresql.chart" . }}
    release: {{ .Release.Name | quote }}
    heritage: {{ .Release.Service | quote }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "postgresql.tplValue" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
data:
{{- with .Files.Glob "files/conf.d/*.conf" }}
{{ .AsConfig | indent 2 }}
{{- end }}
{{ with .Values.postgresqlExtendedConf }}
  override.conf: |
{{- range $key, $value := . }}
    {{ $key | snakecase }}={{ $value }}
{{- end }}
{{- end }}
{{- end }}
