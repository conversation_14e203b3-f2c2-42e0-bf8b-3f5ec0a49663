{{- if .Values.replication.enabled }}
{{- $serviceAnnotations := coalesce .Values.slave.service.annotations .Values.service.annotations -}}
{{- $serviceType := coalesce .Values.slave.service.type .Values.service.type -}}
{{- $serviceLoadBalancerIP := coalesce .Values.slave.service.loadBalancerIP .Values.service.loadBalancerIP -}}
{{- $serviceLoadBalancerSourceRanges := coalesce .Values.slave.service.loadBalancerSourceRanges .Values.service.loadBalancerSourceRanges -}}
{{- $serviceClusterIP := coalesce .Values.slave.service.clusterIP .Values.service.clusterIP -}}
{{- $serviceNodePort := coalesce .Values.slave.service.nodePort .Values.service.nodePort -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "postgresql.fullname" . }}-read
  labels:
    app: {{ template "postgresql.name" . }}
    chart: {{ template "postgresql.chart" . }}
    release: {{ .Release.Name | quote }}
    heritage: {{ .Release.Service | quote }}
  annotations:
  {{- if .Values.commonAnnotations }}
  {{- include "postgresql.tplValue" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
  {{- if $serviceAnnotations }}
  {{- include "postgresql.tplValue" (dict "value" $serviceAnnotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  type: {{ $serviceType }}
  {{- if and $serviceLoadBalancerIP (eq $serviceType "LoadBalancer") }}
  loadBalancerIP: {{ $serviceLoadBalancerIP }}
  {{- end }}
  {{- if and (eq $serviceType "LoadBalancer") $serviceLoadBalancerSourceRanges }}
  loadBalancerSourceRanges: {{- include "postgresql.tplValue" (dict "value" $serviceLoadBalancerSourceRanges "context" $) | nindent 4 }}
  {{- end }}
  {{- if and (eq $serviceType "ClusterIP") $serviceClusterIP }}
  clusterIP: {{ $serviceClusterIP }}
  {{- end }}
  ports:
    - name: tcp-postgresql
      port:  {{ template "postgresql.port" . }}
      targetPort: tcp-postgresql
      {{- if $serviceNodePort }}
      nodePort: {{ $serviceNodePort }}
      {{- end }}
  selector:
    app: {{ template "postgresql.name" . }}
    release: {{ .Release.Name | quote }}
    role: slave
{{- end }}
