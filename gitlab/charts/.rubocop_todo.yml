# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2021-03-23 00:00:00 UTC using RuboCop version 0.93.1.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 31
# Cop supports --auto-correct.
CodeReuse/ActiveRecord:
  Exclude:
    - 'spec/scripts/lib/version_fetcher_spec.rb'
    - 'spec/scripts/manage_version_spec.rb'

# Offense count: 5
# Cop supports --auto-correct.
Cop/LineBreakAfterGuardClauses:
  Exclude:
    - 'spec/configuration/certificates_spec.rb'
    - 'spec/gitlab_test_helper.rb'

# Offense count: 1
# Cop supports --auto-correct.
Cop/LineBreakAroundConditionalBlock:
  Exclude:
    - 'scripts/manage_version.rb'

# Offense count: 5
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, IndentationWidth.
# SupportedStyles: with_first_argument, with_fixed_indentation
Layout/ArgumentAlignment:
  Exclude:
    - 'spec/features/backups_spec.rb'
    - 'spec/integration/check_config_spec.rb'
    - 'spec/spec_helper.rb'

# Offense count: 5
# Cop supports --auto-correct.
Layout/EmptyLineAfterGuardClause:
  Exclude:
    - 'spec/configuration/certificates_spec.rb'
    - 'spec/gitlab_test_helper.rb'

# Offense count: 2
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: empty_lines, no_empty_lines
Layout/EmptyLinesAroundBlockBody:
  Exclude:
    - 'Rakefile'
    - 'spec/features/backups_spec.rb'

# Offense count: 6
# Cop supports --auto-correct.
# Configuration parameters: AllowForAlignment, AllowBeforeTrailingComments, ForceEqualSignAlignment.
Layout/ExtraSpacing:
  Exclude:
    - 'spec/configuration/certificates_spec.rb'
    - 'spec/configuration/mailroom_spec.rb'
    - 'spec/configuration/redis_spec.rb'
    - 'spec/helm_template_helper.rb'

# Offense count: 16
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, IndentationWidth.
# SupportedStyles: consistent, consistent_relative_to_receiver, special_for_inner_method_call, special_for_inner_method_call_in_parentheses
Layout/FirstArgumentIndentation:
  Exclude:
    - 'spec/configuration/gitlab-yml-erb_spec.rb'
    - 'spec/configuration/sidekiq_spec.rb'

# Offense count: 27
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, IndentationWidth.
# SupportedStyles: special_inside_parentheses, consistent, align_braces
Layout/FirstHashElementIndentation:
  Exclude:
    - 'spec/configuration/database_spec.rb'
    - 'spec/configuration/mailroom_spec.rb'

# Offense count: 3
# Cop supports --auto-correct.
# Configuration parameters: AllowMultipleStyles, EnforcedHashRocketStyle, EnforcedColonStyle, EnforcedLastArgumentHashStyle.
# SupportedHashRocketStyles: key, separator, table
# SupportedColonStyles: key, separator, table
# SupportedLastArgumentHashStyles: always_inspect, always_ignore, ignore_implicit, ignore_explicit
Layout/HashAlignment:
  Exclude:
    - 'spec/configuration/database_spec.rb'

# Offense count: 2
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: symmetrical, new_line, same_line
Layout/MultilineHashBraceLayout:
  Exclude:
    - 'spec/configuration/mailroom_spec.rb'

# Offense count: 1
# Cop supports --auto-correct.
Layout/SpaceAfterColon:
  Exclude:
    - 'spec/gitlab_test_helper.rb'

# Offense count: 239
# Cop supports --auto-correct.
Layout/SpaceAfterComma:
  Exclude:
    - 'spec/configuration/certificates_spec.rb'
    - 'spec/configuration/database_spec.rb'
    - 'spec/configuration/gitaly_spec.rb'
    - 'spec/configuration/mailroom_spec.rb'
    - 'spec/configuration/redis_spec.rb'
    - 'spec/helm_template_helper.rb'

# Offense count: 2
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyleInsidePipes.
# SupportedStylesInsidePipes: space, no_space
Layout/SpaceAroundBlockParameters:
  Exclude:
    - 'spec/scripts/manage_version_spec.rb'

# Offense count: 11
# Cop supports --auto-correct.
# Configuration parameters: AllowForAlignment, EnforcedStyleForExponentOperator.
# SupportedStylesForExponentOperator: space, no_space
Layout/SpaceAroundOperators:
  Exclude:
    - 'spec/configuration/database_spec.rb'
    - 'spec/configuration/mailroom_spec.rb'
    - 'spec/configuration/redis_spec.rb'
    - 'spec/gitlab_test_helper.rb'
    - 'spec/helm_template_helper.rb'

# Offense count: 9
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyleForEmptyBraces.
# SupportedStyles: space, no_space
# SupportedStylesForEmptyBraces: space, no_space
Layout/SpaceBeforeBlockBraces:
  Exclude:
    - 'spec/configuration/labels_spec.rb'
    - 'spec/helm_template_helper.rb'

# Offense count: 1
# Cop supports --auto-correct.
Layout/SpaceBeforeComma:
  Exclude:
    - 'spec/helm_template_helper.rb'

# Offense count: 16
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, EnforcedStyleForEmptyBrackets.
# SupportedStyles: space, no_space, compact
# SupportedStylesForEmptyBrackets: space, no_space
Layout/SpaceInsideArrayLiteralBrackets:
  Exclude:
    - 'spec/configuration/database_spec.rb'
    - 'spec/helm_template_helper.rb'
    - 'spec/integration/check_config_spec.rb'

# Offense count: 2
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyleForEmptyBraces, SpaceBeforeBlockParameters.
# SupportedStyles: space, no_space
# SupportedStylesForEmptyBraces: space, no_space
Layout/SpaceInsideBlockBraces:
  Exclude:
    - 'spec/configuration/workhorse_spec.rb'

# Offense count: 29
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyleForEmptyBraces.
# SupportedStyles: space, no_space, compact
# SupportedStylesForEmptyBraces: space, no_space
Layout/SpaceInsideHashLiteralBraces:
  Exclude:
    - 'spec/configuration/labels_spec.rb'
    - 'spec/configuration/mailroom_spec.rb'
    - 'spec/configuration/redis_spec.rb'
    - 'spec/scripts/lib/version_fetcher_spec.rb'

# Offense count: 4
# Cop supports --auto-correct.
# Configuration parameters: AllowInHeredoc.
Layout/TrailingWhitespace:
  Exclude:
    - 'spec/configuration/database_spec.rb'
    - 'spec/configuration/labels_spec.rb'

# Offense count: 1
Lint/MixedRegexpCaptureTypes:
  Exclude:
    - 'scripts/lib/version.rb'

# Offense count: 1
Lint/UselessAssignment:
  Exclude:
    - 'spec/helm_template_helper.rb'

# Offense count: 1
# Configuration parameters: IgnoredMethods.
Metrics/CyclomaticComplexity:
  Max: 14

# Offense count: 1
# Configuration parameters: IgnoredMethods.
Metrics/PerceivedComplexity:
  Max: 15

# Offense count: 1
# Configuration parameters: ExpectMatchingDefinition, CheckDefinitionPathHierarchy, Regex, IgnoreExecutableScripts, AllowedAcronyms.
# AllowedAcronyms: CLI, DSL, ACL, API, ASCII, CPU, CSS, DNS, EOF, GUID, HTML, HTTP, HTTPS, ID, IP, JSON, LHS, QPS, RAM, RHS, RPC, SLA, SMTP, SQL, SSH, TCP, TLS, TTL, UDP, UI, UID, UUID, URI, URL, UTF8, VM, XML, XMPP, XSRF, XSS
Naming/FileName:
  Exclude:
    - 'spec/configuration/gitlab-yml-erb_spec.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: PreferredName.
Naming/RescuedExceptionsVariableName:
  Exclude:
    - 'scripts/manage_version.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: AutoCorrect, SafeMultiline.
Performance/EndWith:
  Exclude:
    - 'scripts/support/changelog/Dangerfile'

# Offense count: 2
# Cop supports --auto-correct.
Performance/RegexpMatch:
  Exclude:
    - 'scripts/support/changelog/Dangerfile'
    - 'spec/spec_helper.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: AutoCorrect, SafeMultiline.
Performance/StartWith:
  Exclude:
    - 'scripts/support/changelog/Dangerfile'

# Offense count: 5
Security/Open:
  Exclude:
    - 'scripts/lib/version_fetcher.rb'
    - 'spec/features/backups_spec.rb'
    - 'spec/gitlab_test_helper.rb'

# Offense count: 4
# Cop supports --auto-correct.
Security/YAMLLoad:
  Exclude:
    - 'spec/configuration/gitaly_spec.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: percent_q, bare_percent
Style/BarePercentLiterals:
  Exclude:
    - 'scripts/support/changelog/Dangerfile'

# Offense count: 8
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, ProceduralMethods, FunctionalMethods, IgnoredMethods, AllowBracesOnProceduralOneLiners, BracesRequiredMethods.
# SupportedStyles: line_count_based, semantic, braces_for_chaining, always_braces
# ProceduralMethods: benchmark, bm, bmbm, create, each_with_object, measure, new, realtime, tap, with_object
# FunctionalMethods: let, let!, subject, watch
# IgnoredMethods: lambda, proc, it
Style/BlockDelimiters:
  Exclude:
    - 'spec/configuration/database_spec.rb'
    - 'spec/configuration/redis_spec.rb'
    - 'spec/helm_template_helper.rb'

# Offense count: 3
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SingleLineConditionsOnly, IncludeTernaryExpressions.
# SupportedStyles: assign_to_condition, assign_inside_condition
Style/ConditionalAssignment:
  Exclude:
    - 'scripts/lib/version.rb'
    - 'scripts/manage_version.rb'

# Offense count: 3
# Cop supports --auto-correct.
Style/DefWithParentheses:
  Exclude:
    - 'spec/helm_template_helper.rb'

# Offense count: 37
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: always, always_true, never
Style/FrozenStringLiteralComment:
  Enabled: false

# Offense count: 5
# Configuration parameters: MinBodyLength.
Style/GuardClause:
  Exclude:
    - 'scripts/lib/version.rb'
    - 'scripts/lib/version_mapping.rb'
    - 'scripts/manage_version.rb'

# Offense count: 2
# Cop supports --auto-correct.
# Configuration parameters: UseHashRocketsWithSymbolValues, PreferHashRocketsForNonAlnumEndingSymbols.
# SupportedStyles: ruby19, hash_rockets, no_mixed_keys, ruby19_no_mixed_keys
Style/HashSyntax:
  Exclude:
    - 'spec/spec_helper.rb'

# Offense count: 12
# Cop supports --auto-correct.
Style/IfUnlessModifier:
  Exclude:
    - 'scripts/manage_version.rb'
    - 'scripts/support/metadata/Dangerfile'
    - 'spec/configuration/workhorse_spec.rb'
    - 'spec/gitlab_test_helper.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: IgnoredMethods.
Style/MethodCallWithoutArgsParentheses:
  Exclude:
    - 'spec/spec_helper.rb'

# Offense count: 1
Style/MixinUsage:
  Exclude:
    - 'spec/spec_helper.rb'

# Offense count: 2
# Cop supports --auto-correct.
Style/MultilineWhenThen:
  Exclude:
    - 'spec/helm_template_helper.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: literals, strict
Style/MutableConstant:
  Exclude:
    - 'db/migrate/**/*'
    - 'db/post_migrate/**/*'
    - 'db/geo/migrate/**/*'
    - 'scripts/lib/version.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, MinBodyLength.
# SupportedStyles: skip_modifier_ifs, always
Style/Next:
  Exclude:
    - 'spec/configuration/workhorse_spec.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: .
# SupportedStyles: compact, exploded
Style/RaiseArgs:
  Exclude:
    - 'scripts/manage_version.rb'

# Offense count: 1
# Cop supports --auto-correct.
Style/RedundantBegin:
  Exclude:
    - 'spec/gitlab_test_helper.rb'

# Offense count: 2
# Cop supports --auto-correct.
Style/RedundantFileExtensionInRequire:
  Exclude:
    - 'Rakefile'
    - 'spec/scripts/lib/version_fetcher_spec.rb'

# Offense count: 2
# Cop supports --auto-correct.
Style/RedundantFreeze:
  Exclude:
    - 'scripts/tag_auto_deploy.rb'

# Offense count: 9
# Cop supports --auto-correct.
# Configuration parameters: AllowMultipleReturnValues.
Style/RedundantReturn:
  Exclude:
    - 'spec/gitlab_test_helper.rb'

# Offense count: 3
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, AllowInnerSlashes.
# SupportedStyles: slashes, percent_r, mixed
Style/RegexpLiteral:
  Exclude:
    - 'scripts/lib/version.rb'
    - 'scripts/support/changelog/Dangerfile'
    - 'scripts/support/metadata/Dangerfile'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: implicit, explicit
Style/RescueStandardError:
  Exclude:
    - 'spec/gitlab_test_helper.rb'

# Offense count: 3
# Cop supports --auto-correct.
# Configuration parameters: ConvertCodeThatCanStartToReturnNil, AllowedMethods.
# AllowedMethods: present?, blank?, presence, try, try!
Style/SafeNavigation:
  Exclude:
    - 'scripts/lib/version.rb'
    - 'scripts/manage_version.rb'

# Offense count: 16
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: only_raise, only_fail, semantic
Style/SignalException:
  Exclude:
    - 'scripts/support/changelog/Dangerfile'
    - 'scripts/support/metadata/Dangerfile'
    - 'spec/features/backups_spec.rb'
    - 'spec/helm_template_helper.rb'

# Offense count: 30
# Cop supports --auto-correct.
Style/SingleArgumentDig:
  Exclude:
    - 'spec/configuration/pages_spec.rb'
    - 'spec/configuration/praefect_spec.rb'
    - 'spec/configuration/redis_spec.rb'
    - 'spec/configuration/webservice_deployments_spec.rb'

# Offense count: 2
# Cop supports --auto-correct.
Style/SlicingWithRange:
  Exclude:
    - 'scripts/lib/version_fetcher.rb'
    - 'scripts/tag_auto_deploy.rb'

# Offense count: 1
# Configuration parameters: AllowModifier.
Style/SoleNestedConditional:
  Exclude:
    - 'scripts/manage_version.rb'

# Offense count: 3
# Cop supports --auto-correct.
# Configuration parameters: .
# SupportedStyles: use_perl_names, use_english_names
Style/SpecialGlobalVars:
  EnforcedStyle: use_perl_names

# Offense count: 6
# Cop supports --auto-correct.
Style/StderrPuts:
  Exclude:
    - 'scripts/lib/version_mapping.rb'
    - 'scripts/manage_version.rb'
    - 'scripts/tag_auto_deploy.rb'

# Offense count: 3
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: single_quotes, double_quotes
Style/StringLiteralsInInterpolation:
  Exclude:
    - 'scripts/support/changelog/Dangerfile'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyleForMultiline.
# SupportedStylesForMultiline: comma, consistent_comma, no_comma
Style/TrailingCommaInArguments:
  Exclude:
    - 'spec/configuration/sidekiq_spec.rb'

# Offense count: 7
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyleForMultiline.
# SupportedStylesForMultiline: comma, consistent_comma, no_comma
Style/TrailingCommaInArrayLiteral:
  Exclude:
    - 'spec/configuration/gitaly_spec.rb'
    - 'spec/configuration/labels_spec.rb'
    - 'spec/configuration/sidekiq_spec.rb'
    - 'spec/integration/check_config_spec.rb'

# Offense count: 53
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyleForMultiline.
# SupportedStylesForMultiline: comma, consistent_comma, no_comma
Style/TrailingCommaInHashLiteral:
  Exclude:
    - 'spec/configuration/certificates_spec.rb'
    - 'spec/configuration/database_spec.rb'
    - 'spec/configuration/gitaly_spec.rb'
    - 'spec/configuration/mailroom_spec.rb'
    - 'spec/configuration/redis_spec.rb'

# Offense count: 2
# Cop supports --auto-correct.
# Configuration parameters: ExactNameMatch, AllowPredicates, AllowDSLWriters, IgnoreClassMethods, AllowedMethods.
# AllowedMethods: to_ary, to_a, to_c, to_enum, to_h, to_hash, to_i, to_int, to_io, to_open, to_path, to_proc, to_r, to_regexp, to_str, to_s, to_sym
Style/TrivialAccessors:
  Exclude:
    - 'spec/helm_template_helper.rb'

# Offense count: 9
# Cop supports --auto-correct.
# Configuration parameters: WordRegex.
# SupportedStyles: percent, brackets
Style/WordArray:
  EnforcedStyle: percent
  MinSize: 4

# Offense count: 113
# Cop supports --auto-correct.
# Configuration parameters: AutoCorrect, AllowHeredoc, AllowURI, URISchemes, IgnoreCopDirectives, IgnoredPatterns.
# URISchemes: http, https
Layout/LineLength:
  Max: 285
