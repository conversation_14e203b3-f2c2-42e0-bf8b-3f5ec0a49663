dependencies:
- name: gitlab
  version: '*.*.*'
- name: certmanager-issuer
  version: '*.*.*'
- name: minio
  version: '*.*.*'
- name: registry
  version: '*.*.*'
- name: cert-manager
  version: 1.5.4
  repository: https://charts.jetstack.io/
  condition: certmanager.install
  alias: certmanager
- name: prometheus
  version: 15.0.4
  repository: https://prometheus-community.github.io/helm-charts
  condition: prometheus.install
- name: postgresql
  version: 8.9.4
  repository: https://charts.bitnami.com/bitnami
  condition: postgresql.install
- name: gitlab-runner
  version: 0.38.0
  repository: https://charts.gitlab.io/
  condition: gitlab-runner.install
- name: grafana
  version: 6.9.1
  repository: https://grafana.github.io/helm-charts
  condition: global.grafana.enabled
- name: redis
  version: 11.3.4
  repository: https://charts.bitnami.com/bitnami
  condition: redis.install
- name: nginx-ingress
  condition: nginx-ingress.enabled
  version: '*.*.*'
