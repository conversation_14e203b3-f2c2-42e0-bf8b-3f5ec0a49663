{{- $sharedSecretEnabled := index .Values "shared-secrets" "enabled" -}}
{{- if $sharedSecretEnabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "shared-secrets.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "gitlab.standardLabels" . | nindent 4 }}
    {{- include "gitlab.commonLabels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-3"
    "helm.sh/hook-delete-policy": hook-succeeded,before-hook-creation
data:
  generate-secrets: |
    {{- include (print $.Template.BasePath "/shared-secrets/_generate_secrets.sh.tpl") . | nindent 4 }}
{{- end -}}
