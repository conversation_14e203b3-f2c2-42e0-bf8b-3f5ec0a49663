{{- if .Values.upgradeCheck.enabled }}
{{- $imageCfg := dict "global" .Values.global.image "local" .Values.upgradeCheck.image -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "fullname" . }}-upgrade-check
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "gitlab.standardLabels" . | nindent 4 }}
    {{- include "gitlab.commonLabels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-upgrade
    "helm.sh/hook-weight": "-11"
    "helm.sh/hook-delete-policy": before-hook-creation
data:
  runcheck: |
    {{- include (print $.Template.BasePath "/_runcheck.tpl") . | nindent 4 }}
---
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ template "fullname" . }}-upgrade-check
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "gitlab.standardLabels" . | nindent 4 }}
    {{- include "gitlab.commonLabels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-upgrade
    "helm.sh/hook-weight": "-10"
    "helm.sh/hook-delete-policy": hook-succeeded,before-hook-creation
spec:
  backoffLimit: 2
  template:
    metadata:
      labels:
        app: {{ template "name" . }}
        release: {{ .Release.Name }}
    spec:
      {{- if .Values.upgradeCheck.nodeSelector }}
        nodeSelector: {{ toYaml .Values.upgradeCheck.nodeSelector | nindent 8 }}
      {{- else }}
      {{- include "gitlab.nodeSelector" . | nindent 6 }}
      {{- end }}
      {{- if .Values.upgradeCheck.tolerations }}
      tolerations:
        {{- toYaml .Values.upgradeCheck.tolerations | nindent 8 }}
      {{- end }}
      securityContext:
        runAsUser: {{ .Values.upgradeCheck.securityContext.runAsUser }}
        fsGroup: {{ .Values.upgradeCheck.securityContext.fsGroup }}
      restartPolicy: Never
      {{- include "gitlab.image.pullSecrets" $imageCfg | nindent 6}}
      containers:
        - name: run-check
          image: {{ include "gitlab.busybox.image" (dict "local" .Values.upgradeCheck "global" $.Values.global.busybox) | quote }}
          command: ['/bin/sh', '/scripts/runcheck']
          {{- include "gitlab.image.pullPolicy" $imageCfg | indent 10 }}
          env:
            - name: GITLAB_VERSION
              value: '{{ coalesce .Values.global.gitlabVersion .Chart.AppVersion }}'
            - name: CHART_VERSION
              value: '{{ .Chart.Version }}'
          volumeMounts:
            - name: chart-info
              mountPath: /chart-info
            - name: scripts
              mountPath: /scripts
{{- if .Values.postgresql.install }}
            - name: postgresql-secret
              mountPath: /etc/secrets/postgresql
{{- end }}
          resources:
            {{- toYaml .Values.upgradeCheck.resources | nindent 12 }}
      volumes:
      - name: chart-info
        configMap:
          name: {{ template "fullname" . }}-chart-info
          optional: true
      - name: scripts
        configMap:
          name: {{ template "fullname" . }}-upgrade-check
{{- if .Values.postgresql.install }}
      - name: postgresql-secret
        secret:
          secretName: {{ template "gitlab.psql.password.secret" . }}
          optional: true
{{- end }}
{{- end }}
