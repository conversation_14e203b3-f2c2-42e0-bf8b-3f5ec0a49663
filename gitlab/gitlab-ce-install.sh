kube<PERSON>l create secret generic gitlab-initial-root-password --from-literal=password="Pass@1337"

helm upgrade --install gitlab gitlab/gitlab \
    --namespace gitlab \
    --set global.edition=ce \
    --set global.hosts.domain=k8s.hak3.today \
    --set global.ingress.configureCertmanager=false \
    --set global.ingress.class=nginx \
    --set global.ingress.tls.enabled=true \
    --set global.ingress.tls.secretName=hak3-today-cert \
    --set global.initialRootPassword.secret=gitlab-initial-root-password \
    --set global.initialRootPassword.key=password \
    --set global.kas.enabled=true \
    --set certmanager.installCRDs=false \
    --set certmanager.install=false  \
    --set nginx-ingress.enabled=false \
    --set prometheus.install=false \
    --set minio.persistence.size=256Gi \
    --set minio.replicaCount=1 \
    --set postgresql.livenessProbe.initialDelaySeconds=420 \
    --set postgresql.readinessProbe.initialDelaySeconds=420 \
    --set postgresql.persistence.size=128Gi \
    --set postgresql.metrics.enabled=false \
    --set postgresql.volumePermissions.enabled=true \
    --set redis.metrics.enabled=false\
    --set gitlab-runner.runners.privileged=true \
    --set gitlab.toolbox.backups.cron.enabled=true \
    --set gitlab.toolbox.backups.cron.successfulJobsHistoryLimit=1 \
    --set gitlab.toolbox.backups.cron.extraArgs="--maximum-backups 3" \
    --set gitlab.toolbox.backups.cron.schedule="0 2 * * *" \
    --set global.appConfig.backups.bucket=gitlab-backups \
    --set global.appConfig.backups.tmpBucket=tmp \
    --set registry.enabled=false \
    --set gitlab.gitlab-exporter.enabled=false \
    --set gitlab.gitlab-shell.minReplicas=1
