apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "gitlab-runner.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels:
    app: {{ include "gitlab-runner.fullname" . }}
    chart: {{ include "gitlab-runner.chart" . }}
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
data:
  entrypoint: |
    #!/bin/bash
    set -e

    export CONFIG_PATH_FOR_INIT="{{ ternary "/etc/gitlab-runner/" "/home/<USER>/.gitlab-runner/" (and (hasKey .Values.podSecurityContext "runAsUser") (eq 0 (.Values.podSecurityContext.runAsUser | int64))) }}"
    mkdir -p ${CONFIG_PATH_FOR_INIT}
    cp /configmaps/config.toml ${CONFIG_PATH_FOR_INIT}

    {{- if eq (include "gitlab-runner.isSessionServerAllowed" . ) "true" }}
    quit() {
      kill -TERM "$child"
    }

    trap quit QUIT TERM

    bash /configmaps/set-session-server-address &
    child=$!
    wait "$child"

    bash /configmaps/set-session-server-port &
    child=$!
    wait "$child"
    {{- end }}

    # Set up environment variables for cache
    if [[ -f /secrets/accesskey && -f /secrets/secretkey ]]; then
      export CACHE_S3_ACCESS_KEY=$(cat /secrets/accesskey)
      export CACHE_S3_SECRET_KEY=$(cat /secrets/secretkey)
    fi

    if [[ -f /secrets/gcs-applicaton-credentials-file ]]; then
      export GOOGLE_APPLICATION_CREDENTIALS="/secrets/gcs-applicaton-credentials-file"
    elif [[ -f /secrets/gcs-application-credentials-file ]]; then
      export GOOGLE_APPLICATION_CREDENTIALS="/secrets/gcs-application-credentials-file"
    else
      if [[ -f /secrets/gcs-access-id && -f /secrets/gcs-private-key ]]; then
        export CACHE_GCS_ACCESS_ID=$(cat /secrets/gcs-access-id)
        # echo -e used to make private key multiline (in google json auth key private key is oneline with \n)
        export CACHE_GCS_PRIVATE_KEY=$(echo -e $(cat /secrets/gcs-private-key))
      fi
    fi

    if [[ -f /secrets/azure-account-name && -f /secrets/azure-account-key ]]; then
      export CACHE_AZURE_ACCOUNT_NAME=$(cat /secrets/azure-account-name)
      export CACHE_AZURE_ACCOUNT_KEY=$(cat /secrets/azure-account-key)
    fi

    if [[ -f /secrets/runner-registration-token ]]; then
      export REGISTRATION_TOKEN=$(cat /secrets/runner-registration-token)
    fi

    if [[ -f /secrets/runner-token ]]; then
      export CI_SERVER_TOKEN=$(cat /secrets/runner-token)
    fi

    # Register the runner
    if ! sh /configmaps/register-the-runner; then
      exit 1
    fi

    # Run pre-entrypoint-script
    if ! bash /configmaps/pre-entrypoint-script; then
      exit 1
    fi

    # Start the runner
    exec /entrypoint run \
    {{- if and .Values.runners.executor (ne "shell" ((.Values.runners.executor) | toString)) }}
      --user=gitlab-runner \
    {{- end }}
      --working-directory=/home/<USER>

  config.toml: |
    shutdown_timeout = {{ .Values.shutdown_timeout }}
    concurrent = {{ .Values.concurrent }}
    check_interval = {{ .Values.checkInterval }}
    log_level = {{ default "info" .Values.logLevel | quote }}
    {{- if .Values.logFormat }}
    log_format = {{ .Values.logFormat | quote }}
    {{- end }}
    {{- if .Values.metrics.enabled }}
    listen_address = ":{{ .Values.metrics.port }}"
    {{- end }}
    {{- if .Values.sentryDsn }}
    sentry_dsn = "{{ .Values.sentryDsn }}"
    {{- end }}
    {{- if .Values.connectionMaxAge }}
    connection_max_age = "{{ .Values.connectionMaxAge }}"
    {{- end }}
    {{- if eq (include "gitlab-runner.isSessionServerAllowed" . ) "true" }}
    [session_server]
      session_timeout = {{ include "gitlab-runner.server-session-timeout" . }}
      listen_address = "0.0.0.0:{{ include "gitlab-runner.server-session-internal-port" . }}"
      advertise_address = "SESSION_SERVER_ADDRESS:SESSION_SERVER_PORT"
    {{- end }}

  {{ if .Values.runners.config }}
  config.template.toml: {{ tpl .Values.runners.config $ | toYaml | indent 2 }}
  {{ end }}

  register-the-runner: |
    #!/bin/sh
    signal_handler() {
      if [ ! -d "/proc/$register_pid" ]; then
        wait $register_pid
      fi
      exit
    }
    trap 'signal_handler' QUIT INT

    MAX_REGISTER_ATTEMPTS=30

    # Reset/unset the not needed flags when an authentication token
    RUN_UNTAGGED="{{ ternary "--run-untagged=true" "" (and (hasKey .Values.runners "runUntagged") .Values.runners.runUntagged) }}"
    ACCESS_LEVEL="{{ ternary "--access-level=ref_protected" "" (and (hasKey .Values.runners "protected") .Values.runners.protected) }}"

    {{- if eq (include "gitlab-runner.isAuthToken" . ) "true" }}
    RUN_UNTAGGED=""
    ACCESS_LEVEL=""
    unset REGISTER_LOCKED
    unset RUNNER_TAG_LIST
    {{- end }}

    for i in $(seq 1 "${MAX_REGISTER_ATTEMPTS}"); do
      echo "Registration attempt ${i} of ${MAX_REGISTER_ATTEMPTS}"
      /entrypoint register \
        {{- if and (hasKey .Values.runners "name") .Values.runners.name }}
        --name={{ .Values.runners.name | quote -}} \
        {{- end }}
        {{- if and (hasKey .Values.runners "maximumTimeout") .Values.runners.maximumTimeout }}
        --maximum-timeout={{ .Values.runners.maximumTimeout | quote -}} \
        {{- end }}
        {{- if eq (include "gitlab-runner.isAuthToken" . ) "false" }}
        ${RUN_UNTAGGED} \
        ${ACCESS_LEVEL} \
        {{- end }}
        {{- if .Values.runners.config }}
        --template-config /configmaps/config.template.toml \
        {{- else if .Values.runners.configPath }}
        --template-config {{ .Values.runners.configPath }} \
        {{- end }}
        --non-interactive &

      register_pid=$!
      wait $register_pid
      retval=$?

      if [ ${retval} = 0 ]; then
        break
      elif [ ${i} = ${MAX_REGISTER_ATTEMPTS} ]; then
        exit 1
      fi

      sleep 5
    done

  check-live: |
    #!/bin/bash
    set -eou pipefail

    # default timeout is 3 seconds, can be overriden
    VERIFY_TIMEOUT=${1:-${VERIFY_TIMEOUT:-3}}

    if ! /usr/bin/pgrep -f ".*register-the-runner"  > /dev/null && ! /usr/bin/pgrep -f "gitlab.*runner"  > /dev/null ; then
      exit 1
    fi

    status=0
    # empty --url= helps `gitlab-runner verify` select all configured runners (otherwise filters for $CI_SERVER_URL)
    verify_output=$(timeout "${VERIFY_TIMEOUT}" gitlab-runner verify --url= 2>&1) || status=$?

    # timeout exit code is 143 with busybox, and 124 with coreutils
    if (( status == 143 )) || (( status == 124 )) ; then
      echo "'gitlab-runner verify' terminated by timeout, not a conclusive failure" >&2
      exit 0
    elif (( status > 0 )) ; then
      exit ${status}
    fi

    grep -qE "is (alive|valid)" <<<"${verify_output}"

  {{- if eq (include "gitlab-runner.isSessionServerAllowed" . ) "true" }}
  set-session-server-address: |
    #!/bin/bash

    if [[ -n "${SESSION_SERVER_ADDRESS}" ]]; then
      sed -i -e "s/SESSION_SERVER_ADDRESS/$SESSION_SERVER_ADDRESS/g" ${CONFIG_PATH_FOR_INIT}/config.toml
    else
      APISERVER=https://${KUBERNETES_SERVICE_HOST:-kubernetes.default.svc}:${KUBERNETES_SERVICE_PORT:-443} \
        && SERVICEACCOUNT=/var/run/secrets/kubernetes.io/serviceaccount \
        && NAMESPACE=$(cat ${SERVICEACCOUNT}/namespace) \
        && TOKEN=$(cat ${SERVICEACCOUNT}/token) \
        && CACERT=${SERVICEACCOUNT}/ca.crt \
        && header="Authorization: Bearer ${TOKEN}"

      SERVICEURL=${APISERVER}/api/v1/namespaces/${NAMESPACE}/services/{{ include "gitlab-runner.server-session-service-name" . }}

      has_address=false
      while [ "${has_address}" = false ]; do
        SERVICEIP=$(curl —-silent \
          --cacert ${CACERT} \
          --header "${header}" \
          -X GET ${SERVICEURL} 2>/dev/null \
          | grep '"ip":' | grep -o '[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}' | head -1)

        # for aws, the hostname is available but not the external IP
        SERVICEHOSTNAME=$(curl —-silent \
          --cacert ${CACERT} \
          --header "${header}" \
          -X GET ${SERVICEURL} 2>/dev/null \
          | grep '"hostname":' | cut -d ":" -f2 | xargs)

        ADDRESS="${SERVICEHOSTNAME:-$SERVICEIP}"

        if [ -z "${ADDRESS}" ]
        then
          echo "Service LoadBalancer External Address not yet available"
          has_address=false
          sleep 5
        else
          has_address=true
          sed -i -e "s/SESSION_SERVER_ADDRESS/$ADDRESS/g" ${CONFIG_PATH_FOR_INIT}/config.toml
        fi
      done
    fi

  set-session-server-port: |
    #!/bin/bash

    {{- if and (eq (include "gitlab-runner.server-session-service-type" .) "NodePort") (not .Values.sessionServer.nodePort)}}
    APISERVER=https://${KUBERNETES_SERVICE_HOST:-kubernetes.default.svc}:${KUBERNETES_SERVICE_PORT:-443} \
      && SERVICEACCOUNT=/var/run/secrets/kubernetes.io/serviceaccount \
      && NAMESPACE=$(cat ${SERVICEACCOUNT}/namespace) \
      && TOKEN=$(cat ${SERVICEACCOUNT}/token) \
      && CACERT=${SERVICEACCOUNT}/ca.crt \
      && header="Authorization: Bearer ${TOKEN}"

    SERVICEURL=${APISERVER}/api/v1/namespaces/${NAMESPACE}/services/{{ include "gitlab-runner.server-session-service-name" . }}

    has_port=false
    while [ "${has_port}" = false ]; do
      SERVICEPORT=$(curl —-silent \
          --cacert ${CACERT} \
          --header "${header}" \
          -X GET ${SERVICEURL} 2>/dev/null \
          | grep '"nodePort":' | cut -d ":" -f2 | xargs)
      if [ -z "${SERVICEPORT}" ]; then
        echo "Service nodePort not yet available"
        sleep 5
      else
        has_port=true
        sed -i -e "s/SESSION_SERVER_PORT/${SERVICEPORT}/g" ${CONFIG_PATH_FOR_INIT}/config.toml
      fi
    done

    {{- else if .Values.sessionServer.nodePort}}
    sed -i -e "s/SESSION_SERVER_PORT/{{.Values.sessionServer.nodePort}}/g" ${CONFIG_PATH_FOR_INIT}/config.toml

    {{- else }}
    sed -i -e "s/SESSION_SERVER_PORT/{{include "gitlab-runner.server-session-external-port" .}}/g" ${CONFIG_PATH_FOR_INIT}/config.toml

    {{- end }}

  {{ end }}

  pre-entrypoint-script: |
{{ .Values.preEntrypointScript | default "" | indent 4 }}

{{ if not (empty .Values.configMaps) }}{{ toYaml .Values.configMaps | indent 2 }}{{ end }}
