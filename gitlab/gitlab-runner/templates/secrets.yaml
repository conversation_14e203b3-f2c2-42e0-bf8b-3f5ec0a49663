{{- if or .Values.runnerRegistrationToken .Values.runnerToken -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "gitlab-runner.secret" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels:
    app: {{ include "gitlab-runner.fullname" . }}
    chart: {{ include "gitlab-runner.chart" . }}
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
type: Opaque
data:
  runner-registration-token: {{ default "" .Values.runnerRegistrationToken | b64enc | quote }}
  runner-token: {{ default "" .Values.runnerToken | b64enc | quote }}
{{- end -}}
