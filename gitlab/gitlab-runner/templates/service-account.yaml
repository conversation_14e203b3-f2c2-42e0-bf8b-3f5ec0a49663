{{- /* TODO: Remove references to `.Values.rbac` */ -}}
{{- if or .Values.serviceAccount.create (and .Values.rbac.create (kindIs "invalid" .Values.serviceAccount.create))   -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  {{- if or .Values.serviceAccount.annotations .Values.rbac.serviceAccountAnnotations }}
  annotations:
    {{- range $key, $value := merge .Values.serviceAccount.annotations .Values.rbac.serviceAccountAnnotations }}
    {{   $key }}: {{ tpl ($value) $ | quote }}
    {{- end }}
  {{- end}}
  name: {{ include "gitlab-runner.serviceAccountName" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels:
    app: {{ include "gitlab-runner.fullname" . }}
    chart: {{ include "gitlab-runner.chart" . }}
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
{{- if or .Values.serviceAccount.imagePullSecrets .Values.rbac.imagePullSecrets }}
imagePullSecrets:
  {{- range concat .Values.serviceAccount.imagePullSecrets .Values.rbac.imagePullSecrets }}
  - name: {{ . | quote }}
  {{- end }}
{{- end }}
{{- end -}}
