{{- if include "gitlab-runner.gitlabUrl" . }}
Your GitLab Runner should now be registered against the GitLab instance reachable at: {{ include "gitlab-runner.gitlabUrl" . }}
{{- else -}}
#############################################################################################
## WARNING: You did not specify an gitlabUrl in your 'helm install' call.                  ##
#############################################################################################

This deployment will be incomplete until you provide the URL that your
GitLab instance is reachable at:

    helm upgrade {{ .Release.Name }} \
        --set gitlabUrl=http://gitlab.your-domain.com,runnerRegistrationToken=your-registration-token \
        gitlab/gitlab-runner
{{- end -}}

{{- if not .Values.runners.config }}
#############################################################################################
## WARNING: You don't seem to be using the GitLab Runner config template functionality.    ##
## Configuring the GitLab Runner through that template is recommended as other             ##
## configuration options will be deprecated in Helm Chart 1.0. Read more at                ##
## https://docs.gitlab.com/runner/install/kubernetes.html#using-configuration-template.    ##
#############################################################################################
{{- end }}

{{- $runnerNamespace := regexFind "\\s*namespace\\s*=.+\\s*" (tpl .Values.runners.config $) | regexFind "=.+" | trimPrefix "=" | trim -}}
{{- if regexMatch "\\s*namespace\\s*=" .Values.runners.config }}

Runner namespace {{ $runnerNamespace }} was found in runners.config template.
{{- if .Values.runners.namespace }}

#############################################################################################
## WARNING: You have set the namespace in runners.config and also set in deprecated        ##
## runner.namespace element. The runners.config namespace will be ignored.                 ##
#############################################################################################
{{- end }}
{{- end }}
{{- if and .Values.rbac.create (kindIs "invalid" .Values.serviceAccount.create) }}

#############################################################################################
## WARNING: You enabled `rbac` without specifying if a service account should be created.  ##
## Please set `serviceAccount.create` to either `true` or `false`.                         ##
## For backwards compatibility a service account will be created.                          ##
#############################################################################################
{{- end }}
{{- if .Values.rbac.serviceAccountName }}

#############################################################################################
## WARNING: You have set the deprecated field `rbac.serviceAccountName`.                   ##
## Please use `serviceAccount.name` instead.                                               ##
#############################################################################################
{{- end }}
{{- if .Values.rbac.serviceAccountAnnotations }}

#############################################################################################
## WARNING: You have set the deprecated field `rbac.serviceAccountAnnotations`.            ##
## Please use `serviceAccount.annotations` instead.                                        ##
#############################################################################################
{{- end }}
{{- if .Values.rbac.imagePullSecrets }}

#############################################################################################
## WARNING: You have set the deprecated field `rbac.imagePullSecrets`.                     ##
## Please use `serviceAccount.imagePullSecrets` instead.                                   ##
#############################################################################################
{{- end }}


{{- if and (hasKey .Values "unregisterRunners") .Values.unregisterRunners (not (empty .Values.deploymentLifecycle)) }}

#############################################################################################
## WARNING: You have a custom lifecycle set on the container.                              ##
## The custom lifecycle setting will take precedence over the unregistrRunner settings     ##
## Consider adding the following command as a preStop hook                                 ##
## e.g.                                                                                    ##
## preStop:                                                                                ##
##   exec:                                                                                 ##
##     command: ["/entrypoint", "unregister", "--all-runners"]                             ##
#############################################################################################
{{- end }}
