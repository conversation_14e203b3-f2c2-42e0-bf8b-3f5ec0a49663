{{- if .Values.rbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: {{ if .Values.rbac.clusterWideAccess }}"ClusterRoleBinding"{{ else }}"RoleBinding"{{ end }}
metadata:
  name: {{ include "gitlab-runner.fullname" . }}
  labels:
    app: {{ include "gitlab-runner.fullname" . }}
    chart: {{ include "gitlab-runner.chart" . }}
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
  {{ if not .Values.rbac.clusterWideAccess -}}
  namespace: {{ .Release.Namespace | quote }}
  {{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: {{ if .Values.rbac.clusterWideAccess }}"ClusterRole"{{ else }}"Role"{{ end }}
  name: {{ include "gitlab-runner.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "gitlab-runner.serviceAccountName" . }}
  namespace: "{{ .Release.Namespace }}"
{{- end -}}
