name: Build Images

on:
  push:
    branches: master
  pull_request:
      paths:
        - docker-compose.yml
        - services

jobs:
  build:
    strategy:
      matrix:
        profile:
          - auto
          - invoke
          - comfy
          - download
    runs-on: ubuntu-latest
    name: ${{ matrix.profile }}
    steps:
      - uses: actions/checkout@v3
      - run: docker compose --profile ${{ matrix.profile }} build --progress plain
