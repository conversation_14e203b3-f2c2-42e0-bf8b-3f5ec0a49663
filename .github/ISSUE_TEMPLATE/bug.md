---
name: Bug
about: Report a bug
title: ""
labels: bug
assignees: ""
---

<!--  PLEASE FILL THIS OUT, IT WILL MAKE BOTH OF OUR LIVES EASIER -->

**Has this issue been opened before?**

- [ ] It is not in the [FAQ](https://github.com/AbdBarho/stable-diffusion-webui-docker/wiki/FAQ), I checked.
- [ ] It is not in the [issues](https://github.com/AbdBarho/stable-diffusion-webui-docker/issues?q=), I searched.

**Describe the bug**

<!--  tried to run the app, my cat exploded -->

**Which UI**

auto or auto-cpu or invoke or sygil?

**Hardware / Software**

- OS: [e.g. Windows 10 / Ubuntu 22.04]
- OS version: <!--  on windows, use the command `winver` to find out, on ubuntu `lsb_release -d`  -->
- WSL version (if applicable): <!-- get using `wsl -l -v` -->
- Docker Version: <!--  get using `docker version` -->
- Docker compose version: <!--  get using `docker compose version` -->
- Repo version: <!-- tag, commit sha, or "from master" -->
- RAM:
- GPU/VRAM:

**Steps to Reproduce**

1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Additional context**
Any other context about the problem here. If applicable, add screenshots to help explain your problem.
