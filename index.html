<!doctype html>
<html lang="en">

<head>
    <title>Online - Reverse Shell Generator</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" href="favicon.ico" type="image/ico" sizes="16x16">
    <link href="assets/bootstrap.min.css" rel="stylesheet">
    <script src="assets/jquery-3.5.1.slim.min.js"></script>
    <script src="assets/popper-1.16.1.min.js"></script>
    <script src="assets/bootstrap-4.5.2.min.js"></script>
    <script src="assets/axios.min.js"></script>
    <link rel="stylesheet" href="css/dark-mode.css">
    <link rel="stylesheet" href="css/light-mode.css">
    <link rel="stylesheet" href="css/meme-mode.css">
    <meta name="description" content="Online Reverse Shell generator with Local Storage functionality, URI & Base64 Encoding, MSFVenom Generator, and Raw Mode. Great for CTFs." />
    <meta name="title" content="Online - Reverse Shell Generator">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.revshells.com/">
    <meta property="og:title" content="Online - Reverse Shell Generator">
    <meta property="og:description" content="Online Reverse Shell generator with Local Storage functionality, URI & Base64 Encoding, MSFVenom Generator, and Raw Mode. Great for CTFs.">
    <meta property="og:image"
        content="https://user-images.githubusercontent.com/58673953/111243529-9d646f80-85d7-11eb-986c-9842747dc2e7.png">
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://www.revshells.com/">
    <meta property="twitter:title" content="Online - Reverse Shell Generator">
    <meta property="twitter:description" content="Online Reverse Shell generator with Local Storage functionality, URI & Base64 Encoding, MSFVenom Generator, and Raw Mode. Great for CTFs.">
    <meta property="twitter:image"
        content="https://user-images.githubusercontent.com/58673953/111243529-9d646f80-85d7-11eb-986c-9842747dc2e7.png">

    <link rel="canonical" href="https://revshells.com" />

    <!-- GitHub Ribbon -->
    <a href="https://github.com/0dayCTF/reverse-shell-generator" class="github-corner"
        aria-label="View source on GitHub"><svg width="80" height="80" viewBox="0 0 250 250"
            style="fill:#fff; color:#151513; position: absolute; top: 0; border: 0; right: 0;" aria-hidden="true">
            <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path>
            <path
                d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2"
                fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path>
            <path
                d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z"
                fill="currentColor" class="octo-body"></path>
        </svg></a>
    <style>
    </style>
</head>

<body class="bg-black">
    <div class="container d-flex flex-column vh-100">
        <div class="custom-control float-right" style="margin-left: -25px;">
            <label for="theme-selector" class="card-title col-auto col-form-label float-left"
                style="font-size:1rem">Theme</label>

            <select class="custom-select" id="theme-selector">
                <option class="os-item" value="dark">Dark</option>
                <option class="os-item" value="light">Light</option>
                <option class="os-item" value="meme">Meme</option>
            </select>
        </div>

        <!-- Header -->
        <div class="mb-auto row justify-content-center mb-5">
            <h2 class="rainbow">Reverse Shell Generator</h2>
        </div>

        <div class="row">

            <!-- IP & Port Configuration -->
            <div class="col-12 col-md-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-body">
                        <h5 class="card-title mb-0">
                            <b>IP & Port</b>
                        </h5>

                        <!-- card-text -->
                        <div class="card-text h-100">
                            <form class="row justify-content-center align-items-center h-100">

                                <!-- IP -->
                                <div class="col-auto mt-4">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span id="ip-label" class="input-group-text">IP</span>
                                        </div>
                                        <input id="ip" type="text" class="form-control form-control-lg text-center px-1"
                                            size="12VW" aria-label="IP"
                                            aria-describedby="ip-label">
                                    </div>
                                </div>

                                <!-- Port -->
                                <div class="col-auto mt-4">
                                    <div class="input-group" data-toggle="tooltip">
                                        <div class="input-group-prepend">
                                            <span id="port-label" class="input-group-text">Port</span>
                                        </div>
                                        <input id="port" type="text" class="form-control form-control-lg text-center"
                                            size="4vw" maxlength="5" aria-label="Port"
                                            aria-describedby="port-label">
                                        <div class="input-group-append">
                                            <button id="inc-port" class="btn btn-secondary btn-sm" type="button"
                                                style="font-size: 1rem" data-toggle="tooltip"
                                                title="Increase port number by one">
                                                +1
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div id="port-privileges-warning" class="col-12 small text-center"
                                    style="visibility: hidden">
                                    <span class="highlighted-warning">root</span>
                                    privileges required.
                                </div>

                            </form>
                        </div>
                        <!-- /card-text -->

                    </div>
                </div>
            </div>
            <!-- /IP & Port Configuration -->

            <!-- Listener -->
            <div class="col-12 col-md-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-body">

                        <!-- Advanced switch -->
                        <div class="custom-control custom-switch float-right">
                            <input id="listener-advanced-switch" type="checkbox" class="custom-control-input" checked>
                            <label for="listener-advanced-switch" class="custom-control-label small pr-2 pb-1"
                                style="padding-top:2px" data-toggle="tooltip" title="Display advanced settings">
                                Advanced
                            </label>
                        </div>

                        <h5 class="card-title"><b>Listener</b></h5>

                        <div class="card-text">

                            <!-- Command -->
                            <div class="row justify-content-center">
                                <div class="col-auto position-relative" style="min-width: 15vw">
                                    <pre class="prompt-sign">🚀</pre>
                                    <pre id="listener-command" class="bg-dark border text-wrap text-break p-4 pl-5 mb-2" spellcheck="false"
                                        style="outline: none; font-size:1em;" contenteditable="true"></pre>
                                </div>
                            </div>

                            <!-- Advanced section-->
                            <form class="row justify-content-center collapse" id="listener-advanced">
                                <label for="listener-selection" class="col-auto col-form-label">Type</label>
                                <div class="col-auto">
                                    <select class="custom-select" id="listener-selection">
                                        <!-- filled by init()-->
                                    </select>
                                </div>
                            </form>

                        </div>
                        <!-- /card-text -->

                        <!-- Copy button -->
                        <button id="copy-listener" type="button" class="btn btn-primary float-right"
                            data-toggle="tooltip" title="Copy to clipboard">
                            Copy
                        </button>
                    </div>
                </div>
            </div>
            <!-- /listener -->

        </div>

        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="reverse-tab" data-toggle="tab" href="#reverse" role="tab"
                    aria-controls="reverse" aria-selected="true">Reverse</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="bind-tab" data-toggle="tab" href="#bind" role="tab" aria-controls="bind"
                    aria-selected="false">Bind</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="msfvenom-tab" data-toggle="tab" href="#msfvenom" role="tab"
                    aria-controls="msfvenom" aria-selected="false">MSFVenom</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="hoaxshell-tab" data-toggle="tab" href="#hoaxshell" role="tab"
                    aria-controls="msfvenom" aria-selected="false">HoaxShell</a>
            </li>
            <!-- <li class="nav-item">
                <a class="nav-link disabled" id="settings-tab" data-toggle="tab" href="#settings" role="tab"
                    aria-controls="settings" aria-selected="false" aria-disabled="true">Settings</a>
            </li> -->
        </ul>

        <div class="tab-content">
            <!-- Reverse Shell Tab-->
            <div class="tab-pane active" id="reverse" role="tabpanel" aria-labelledby="reverse-tab">
                <div class="card shadow mb-5">
                    <div class="card-body">

                        <!-- Show all advanced switch -->
                        <div class="custom-control custom-switch float-right">
                            <input id="revshell-advanced-switch" type="checkbox" class="custom-control-input" checked>
                            <label for="revshell-advanced-switch" class="custom-control-label small pr-2 pb-1"
                                style="padding-top:2px" data-toggle="tooltip" title="Display all advanced settings">
                                Show Advanced
                            </label>
                            <img src="assets/floppy-disk-solid.svg" class="download-svg" data-toggle="tooltip" title="Download Payload">
                        </div>
                        <!-- /Show all advanced switch -->

                        <!---Filter OS-->
                        <div class="row">
                            <label for="os-options" class="col-auto col-form-label float-left"
                                style="font-size:1rem">OS</label>
                            <div class="col-auto">
                                <select id="os-options" class="custom-select">
                                    <option class="os-item" value="all">All</option>
                                    <option class="os-item" value="linux">Linux</option>
                                    <option class="os-item" value="windows">Windows</option>
                                    <option class="os-item" value="mac">Mac</option>
                                </select>
                            </div>
                        </div>
                        <!---/Filter OS-->

                        <div class="card-text mt-4">
                            <div class="row">

                                <!-- Left column: Reverse shell selection -->
                                <div class="col-12 col-md-3">
                                    <div id="reverse-shell-selection" class="list-group overflow-auto"
                                        style="max-height: 520px">
                                        <!-- filled by init()-->
                                    </div>
                                </div>

                                <!-- Right column -->
                                <div class="col-12 col-md-9 d-flex flex-column">

                                    <!-- Reverse Shell Command -->
                                    <div class="row flex-grow-1">
                                        <div class="col position-relative">
                                            <pre class="prompt-sign">🚀</pre>
                                            <pre id="reverse-shell-command"
                                                class="bg-dark border pre-wrap text-break p-4 pl-5" spellcheck="false"
                                                style="outline: none; font-size:1em;" contenteditable="true"></pre>
                                        </div>
                                    </div>

                                    <!-- Advanced section -->
                                    <form class="row justify-content-center collapse" id="revshell-advanced">

                                        <!-- Shell -->
                                        <div class="col-auto mr-3">
                                            <div class="row">
                                                <label for="shell" class="col-auto col-form-label">Shell</label>
                                                <div class="col-auto">
                                                    <select id="shell" class="custom-select">
                                                        <!-- filled by init()-->
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Encoding -->
                                        <div class="col-auto ml-3">
                                            <div class="form-group row">
                                                <label for="encoding" class="col-auto col-form-label">Encoding</label>
                                                <div class="col-auto">
                                                    <select id="encoding" class="custom-select">
                                                        <option value="None">None</option>
                                                        <option value="encodeURL">URL Encode</option>
                                                        <option value="encodeURLDouble">Double URL Encode</option>
                                                        <option value="Base64">Base64</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                    </form>
                                    <!-- /Advanced section -->

                                    <div class="row justify-content-end mr-1">
                                            <!-- Toasts for Reverse Shell Auto Copy -->
                                    <div id="clipboard-toast" class="toast position-fixed text-white" data-delay="1200"
                                    style="bottom: 16px; left: 50%; transform: translateX(-50%); background: rgba(32, 32, 32, .85)">
                                    <div class="toast-body">
                                        Copied to clipboard
                                    </div>
                                    </div>

                                    <div id="clipboard-failure-toast" class="toast position-fixed text-white" data-delay="1200"
                                    style="bottom: 16px; left: 50%; transform: translateX(-50%); background: rgba(255, 0, 0, .85)">
                                    <div class="toast-body">
                                        Error copying to clipboard
                                    </div>
                                    </div>
                                    <!-- Auto-copy switch -->
                                        <!-- <div class="custom-control custom-switch mt-2 mr-3" data-toggle="tooltip"
                                            title="Auto-copy to clipboard when selecting a new shell type">
                                            <input id="auto-copy-switch" class="custom-control-input" type="checkbox"
                                                checked>
                                            <label for="auto-copy-switch" class="custom-control-label small pr-2 pb-1"
                                                style="padding-top: 2px">Auto-copy</label>
                                        </div> -->

                                        <!-- Raw button -->
                                        <button type="button" class="raw-listener btn btn-primary float-right mr-3"
                                                data-toggle="tooltip" title="View raw">
                                            Raw
                                        </button>

                                        <!-- Copy button -->
                                        <button id="copy-reverse-shell-command" data-toggle="tooltip"
                                            title="Copy to clipboard" type="button" class="btn btn-primary float-right">
                                            Copy
                                        </button>
                                    </div>

                                </div>
                                <!-- /Right column -->

                            </div>
                        </div>
                        <!-- /card-text -->

                    </div>
                </div>
            </div>
            <!-- /Reverse Shell Tab -->

            <!-- Bind Shell Tab -->
            <div class="tab-pane" id="bind" role="tabpanel" aria-labelledby="bind-tab">
                <div class="card shadow mb-5">
                    <div class="card-body">
                        <img src="assets/floppy-disk-solid.svg" class="download-svg float-right" data-toggle="tooltip" title="Download Payload">
                        <div class="card-text mt-4">
                            <div class="row">
                                <!-- Left column: Bind selection -->
                                <div class="col-12 col-md-3">
                                    <div id="bind-shell-selection" class="list-group overflow-auto" style="max-height: 520px">
                                        <!-- filled by init()-->
                                    </div>
                                </div>
                                <!-- Right column -->
                                <div class="col-12 col-md-9 d-flex flex-column">
                                    <!-- Command -->
                                    <div class="row flex-grow-1">
                                        <div class="col position-relative">
                                            <pre class="prompt-sign">🚀</pre>
                                            <pre id="bind-shell-command" class="bg-dark border pre-wrap text-break p-4 pl-5" spellcheck="false"
                                                style="outline: none; font-size:1em;" contenteditable="true"></pre>
                                        </div>
                                    </div>

                                    <div class="row justify-content-end mr-1">
                                        <!-- Auto-copy switch -->
                                        <!-- <div class="custom-control custom-switch mt-2 mr-3" data-toggle="tooltip"
                                            title="Auto-copy to clipboard when selecting a new shell type">
                                            <input id="auto-copy-switch" class="custom-control-input" type="checkbox" checked>
                                            <label for="auto-copy-switch" class="custom-control-label small pr-2 pb-1"
                                                style="padding-top: 2px">Auto-copy</label>
                                        </div> -->
                                        <!-- Raw button -->
                                        <button type="button" class="raw-listener btn btn-primary float-right mr-3"
                                                data-toggle="tooltip" title="View raw">
                                            Raw
                                        </button>

                                        <!-- Copy button -->
                                        <button id="copy-bind-shell-command" data-toggle="tooltip" title="Copy to clipboard"
                                            type="button" class="btn btn-primary float-right">
                                            Copy
                                        </button>
                                    </div>
                                </div>
                                <!-- /Right column -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /Bind Shell Tab -->

            <!-- MSFVenom Tab -->
            <div class="tab-pane" id="msfvenom" role="tabpanel" aria-labelledby="msfvenom-tab">
                <div class="card shadow mb-5">
                    <div class="card-body">
                        <img src="assets/floppy-disk-solid.svg" class="download-svg float-right" data-toggle="tooltip" title="Download Payload">
                        <div class="card-text mt-4">
                            <div class="row">
                                    <!-- Left column: MSFVenom selection -->
                                    <div class="col-12 col-md-3">
                                        <div id="msfvenom-selection" class="list-group overflow-auto"
                                            style="max-height: 520px">
                                            <!-- filled by init()-->
                                        </div>
                                    </div>

                                    <!-- Right column -->
                                    <div class="col-12 col-md-9 d-flex flex-column">

                                        <!-- Command -->
                                        <div class="row flex-grow-1">
                                            <div class="col position-relative">
                                                <pre class="prompt-sign">🚀</pre>
                                                <pre id="msfvenom-command"
                                                    class="bg-dark border pre-wrap text-break p-4 pl-5" spellcheck="false"
                                                    style="outline: none; font-size:1em;" contenteditable="true"></pre>
                                            </div>
                                        </div>

                                        <div class="row justify-content-end mr-1">
                                            <!-- Auto-copy switch -->
                                            <!-- <div class="custom-control custom-switch mt-2 mr-3" data-toggle="tooltip"
                                                title="Auto-copy to clipboard when selecting a new shell type">
                                                <input id="auto-copy-switch" class="custom-control-input"
                                                    type="checkbox" checked>
                                                <label for="auto-copy-switch"
                                                    class="custom-control-label small pr-2 pb-1"
                                                    style="padding-top: 2px">Auto-copy</label>
                                            </div> -->

                                            <!-- Raw button -->
                                            <button type="button" class="raw-listener btn btn-primary float-right mr-3"
                                                    data-toggle="tooltip" title="View raw">
                                                Raw
                                            </button>

                                            <!-- Copy button -->
                                            <button id="copy-msfvenom-command" data-toggle="tooltip"
                                                title="Copy to clipboard" type="button"
                                                class="btn btn-primary float-right">
                                                Copy
                                            </button>
                                        </div>
                                    </div>
                                    <!-- /Right column -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /MSFVenom Tab -->

            <!-- HoaxShell Tab -->
            <div class="tab-pane" id="hoaxshell" role="tabpanel" aria-labelledby="hoaxshell-tab">
                <div class="card shadow mb-5">
                    <div class="card-body">
                        <img src="assets/floppy-disk-solid.svg" class="download-svg float-right" data-toggle="tooltip" title="Download Payload">
                        <div class="card-text mt-4">
                            <div class="row">
                                    <!-- Left column: HoaxShell selection -->
                                    <div class="col-12 col-md-3">
                                        <div id="hoaxshell-selection" class="list-group overflow-auto"
                                            style="max-height: 520px">
                                            <!-- filled by init()-->
                                        </div>
                                    </div>

                                    <!-- Right column -->
                                    <div class="col-12 col-md-9 d-flex flex-column">

                                        <!-- Command -->
                                        <div class="row flex-grow-1">
                                            <div class="col position-relative">
                                                <pre class="prompt-sign">🚀</pre>
                                                <pre id="hoaxshell-command"
                                                    class="bg-dark border pre-wrap text-break p-4 pl-5" spellcheck="false"
                                                    style="outline: none; font-size:1em;" contenteditable="true"></pre>
                                            </div>
                                        </div>

                                        <div class="row justify-content-end mr-1">

                                            <!-- Download button -->
                                            <a href="https://github.com/t3l3machus/hoaxshell/tree/main/revshells" target="_blank"><button type="button" class="btn btn-primary float-right mr-3"
                                                    data-toggle="tooltip" title="Download Listener">
                                                Download Listener
                                            </button></a>

                                            <!-- Copy button -->
                                            <button id="copy-hoaxshell-command" data-toggle="tooltip"
                                                title="Copy to clipboard" type="button"
                                                class="btn btn-primary float-right">
                                                Copy
                                            </button>
                                        </div>
                                    </div>
                                    <!-- /Right column -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /hoaxshell Tab -->
    </div>

    <!-- RSG data -->
    <script src="js/data.js"></script>

    <!-- RSG raw link generator -->
    <script src="js/raw-link.js"></script>

    <!-- Light switch -->
    <script src="js/light-mode-switch.js"></script>

    <!-- Main js -->
    <script src="js/script.js"></script>
</body>

</html>
