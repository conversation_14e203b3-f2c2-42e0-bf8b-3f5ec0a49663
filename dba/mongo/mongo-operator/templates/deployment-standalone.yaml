{{- if not .Values.replicaSet.enabled }}
apiVersion: apps/v1
kind: {{ if .Values.useStatefulSet }}{{ "StatefulSet" }}{{- else }}{{ "Deployment" }}{{- end }}
metadata:
  name: {{ template "mongodb.fullname" . }}
  labels:
    app: {{ template "mongodb.name" . }}
    chart: {{ template "mongodb.chart" . }}
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
    {{- with .Values.labels }}
{{ toYaml . | indent 4 }}
    {{- end }}
  {{- with .Values.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
  {{- end }}
spec:
  {{- if .Values.useStatefulSet }}
  serviceName: {{ template "mongodb.serviceName" . }}
  updateStrategy:
  {{- else }}
  strategy:
  {{- end }}
    type: {{ .Values.updateStrategy.type }}
    {{- if (eq "Recreate" .Values.updateStrategy.type) }}
    rollingUpdate: null
    {{- end }}
  selector:
    matchLabels:
      app: {{ template "mongodb.name" . }}
      release: "{{ .Release.Name }}"
  template:
    metadata:
      labels:
        app: {{ template "mongodb.name" . }}
        release: "{{ .Release.Name }}"
        chart: {{ template "mongodb.chart" . }}
      {{- if .Values.podLabels }}
{{ toYaml .Values.podLabels | indent 8 }}
      {{- end }}
      {{- if or .Values.podAnnotations .Values.metrics.enabled }}
      annotations:
{{- if .Values.podAnnotations }}
{{ toYaml .Values.podAnnotations | indent 8 }}
{{- end }}
{{- if .Values.metrics.enabled }}
{{ toYaml .Values.metrics.podAnnotations | indent 8 }}
{{- end }}
      {{- end }}
    spec:
      {{- if .Values.schedulerName }}
      schedulerName: "{{ .Values.schedulerName }}"
      {{- end }}
      {{- if .Values.priorityClassName }}
      priorityClassName: {{ .Values.priorityClassName }}
      {{- end }}
      {{- if .Values.securityContext.enabled }}
      securityContext:
        fsGroup: {{ .Values.securityContext.fsGroup }}
      {{- end }}
      {{- if .Values.affinity }}
      affinity:
{{ toYaml .Values.affinity | indent 8 }}
      {{- end -}}
      {{- if .Values.nodeSelector }}
      nodeSelector:
{{ toYaml .Values.nodeSelector | indent 8 }}
      {{- end }}
      {{- if .Values.tolerations }}
      tolerations:
{{ toYaml .Values.tolerations | indent 8 }}
      {{- end }}
{{- include "mongodb.imagePullSecrets" . | indent 6 }}
      initContainers:
      {{- if .Values.extraInitContainers }}
{{ tpl .Values.extraInitContainers . | indent 6}}
      {{- end }}
      {{- if and .Values.volumePermissions.enabled .Values.persistence.enabled }}
      - name: volume-permissions
        image: {{ template "mongodb.volumePermissions.image" . }}
        imagePullPolicy: {{ .Values.volumePermissions.image.pullPolicy | quote }}
        command: ["chown", "-R", "{{ .Values.securityContext.runAsUser }}:{{ .Values.securityContext.fsGroup }}", "{{ .Values.persistence.mountPath }}"]
        securityContext:
          runAsUser: 0
        resources: {{ toYaml .Values.volumePermissions.resources | nindent 10 }}
        volumeMounts:
        - name: data
          mountPath: {{ .Values.persistence.mountPath }}
      {{- end }}
      containers:
      - name: {{ template "mongodb.fullname" . }}
        image: {{ template "mongodb.image" . }}
        imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
        {{- if .Values.securityContext.enabled }}
        securityContext:
          runAsNonRoot: true
          runAsUser: {{ .Values.securityContext.runAsUser }}
        {{- end }}
        env:
        {{- if .Values.image.debug}}
        - name: BITNAMI_DEBUG
          value: "true"
        {{- end }}
        {{- if .Values.usePassword }}
        {{- if and .Values.mongodbUsername .Values.mongodbDatabase }}
        - name: MONGODB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ if .Values.existingSecret }}{{ .Values.existingSecret }}{{- else }}{{ template "mongodb.fullname" . }}{{- end }}
              key: mongodb-password
        {{- end }}
        - name: MONGODB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ if .Values.existingSecret }}{{ .Values.existingSecret }}{{- else }}{{ template "mongodb.fullname" . }}{{- end }}
              key: mongodb-root-password
        {{- end }}
        {{- if .Values.mongodbUsername }}
        - name: MONGODB_USERNAME
          value: {{ .Values.mongodbUsername | quote }}
        {{- end }}
        - name: MONGODB_SYSTEM_LOG_VERBOSITY
          value: {{ .Values.mongodbSystemLogVerbosity | quote }}
        - name: MONGODB_DISABLE_SYSTEM_LOG
          {{- if .Values.mongodbDisableSystemLog }}
          value: "yes"
          {{- else }}
          value: "no"
          {{- end }}
        {{- if .Values.mongodbDatabase }}
        - name: MONGODB_DATABASE
          value: {{ .Values.mongodbDatabase | quote }}
        {{- end }}
        - name: MONGODB_ENABLE_IPV6
        {{- if .Values.mongodbEnableIPv6 }}
          value: "yes"
        {{- else }}
          value: "no"
        {{- end }}
        - name: MONGODB_ENABLE_DIRECTORY_PER_DB
        {{- if .Values.mongodbDirectoryPerDB }}
          value: "yes"
        {{- else }}
          value: "no"
        {{- end }}
        {{- if .Values.mongodbExtraFlags }}
        - name: MONGODB_EXTRA_FLAGS
          value: {{ .Values.mongodbExtraFlags | join " " | quote }}
        {{- end }}
        {{- if .Values.extraEnvVars }}
        {{- include "mongodb.tplValue" ( dict "value" .Values.extraEnvVars "context" $ ) | nindent 8 }}
        {{- end }}
        ports:
        - name: mongodb
          containerPort: 27017
        {{- if .Values.livenessProbe.enabled }}
        livenessProbe:
          exec:
            command:
            - mongo
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
          successThreshold: {{ .Values.livenessProbe.successThreshold }}
          failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
        {{- end }}
        {{- if .Values.readinessProbe.enabled }}
        readinessProbe:
          exec:
            command:
            - mongo
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
          successThreshold: {{ .Values.readinessProbe.successThreshold }}
          failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
        {{- end }}
        volumeMounts:
        - name: data
          mountPath: {{ .Values.persistence.mountPath }}
          subPath: {{ .Values.persistence.subPath }}
        {{- if  or (.Files.Glob "files/docker-entrypoint-initdb.d/*[sh|js|json]") (.Values.initConfigMap) }}
        - name: custom-init-scripts
          mountPath: /docker-entrypoint-initdb.d
        {{- end }}
        {{- if .Values.configmap }}
        - name: config
          mountPath: /opt/bitnami/mongodb/conf/mongodb.conf
          subPath: mongodb.conf
        {{- end }}
        {{- if .Values.extraVolumeMounts }}
{{ toYaml .Values.extraVolumeMounts | indent 8}}
        {{- end }}
        resources:
{{ toYaml .Values.resources | indent 10 }}
{{- if .Values.metrics.enabled }}
      - name: metrics
        image: {{ template "mongodb.metrics.image" . }}
        imagePullPolicy: {{ .Values.metrics.image.pullPolicy | quote }}
        {{- if .Values.securityContext.enabled }}
        securityContext:
          runAsNonRoot: true
          runAsUser: {{ .Values.securityContext.runAsUser }}
        {{- end }}
        env:
        {{- if .Values.usePassword }}
        - name: MONGODB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ if .Values.existingSecret }}{{ .Values.existingSecret }}{{- else }}{{ template "mongodb.fullname" . }}{{- end }}
              key: mongodb-root-password
        command: [ 'sh', '-c', '/bin/mongodb_exporter --mongodb.uri mongodb://root:${MONGODB_ROOT_PASSWORD}@localhost:{{ .Values.service.port }}/admin {{ .Values.metrics.extraArgs }}' ]
        {{- else }}
        command: [ 'sh', '-c', '/bin/mongodb_exporter --mongodb.uri mongodb://localhost:{{ .Values.service.port }} {{ .Values.metrics.extraArgs }}' ]
        {{- end }}
        ports:
        - name: metrics
          containerPort: 9216
        {{- if .Values.metrics.livenessProbe.enabled }}
        livenessProbe:
          httpGet:
            path: /metrics
            port: metrics
          initialDelaySeconds: {{ .Values.metrics.livenessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.metrics.readinessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.metrics.livenessProbe.timeoutSeconds }}
          failureThreshold: {{ .Values.metrics.livenessProbe.failureThreshold }}
          successThreshold: {{ .Values.metrics.livenessProbe.successThreshold }}
        {{- end }}
        {{- if .Values.metrics.readinessProbe.enabled }}
        readinessProbe:
          httpGet:
            path: /metrics
            port: metrics
          initialDelaySeconds: {{ .Values.metrics.readinessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.metrics.readinessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.metrics.readinessProbe.timeoutSeconds }}
          failureThreshold: {{ .Values.metrics.readinessProbe.failureThreshold }}
          successThreshold: {{ .Values.metrics.readinessProbe.successThreshold }}
        {{- end }}
        resources:
{{ toYaml .Values.metrics.resources | indent 10 }}
{{- end }}
{{- if .Values.sidecars }}
{{ toYaml .Values.sidecars | indent 6 }}
{{- end }}
      volumes:
      {{- if (.Files.Glob "files/docker-entrypoint-initdb.d/*[sh|js|json]") }}
      - name: custom-init-scripts
        configMap:
          name: {{ template "mongodb.fullname" . }}-init-scripts
      {{- end }}
      {{- if (.Values.initConfigMap) }}
      - name: custom-init-scripts
        configMap:
          name: {{ .Values.initConfigMap.name }}
      {{- end }}
      - name: data
      {{- if not .Values.useStatefulSet }}
      {{- if .Values.persistence.enabled }}
        persistentVolumeClaim:
          claimName: {{ if .Values.persistence.existingClaim }}{{ .Values.persistence.existingClaim }}{{- else }}{{ template "mongodb.fullname" . }}{{- end }}

      {{- else }}
        nfs:
          path: /opt/nfstorage/data/mongo
          server: ***********
      {{- end -}}
      {{- end -}}
      {{- if .Values.configmap }}
      - name: config
        configMap:
          name: {{ template "mongodb.fullname" . }}
      {{- end }}
      {{- if .Values.extraVolumes }}
{{ toYaml .Values.extraVolumes | indent 6}}
      {{- end }}
{{- if .Values.useStatefulSet }}
{{- if .Values.persistence.enabled }}
  volumeClaimTemplates:
    - metadata:
        name: data
        annotations:
        {{- range $key, $value := .Values.persistence.annotations }}
          {{ $key }}: "{{ $value }}"
        {{- end }}
      spec:
        accessModes:
        {{- range .Values.persistence.accessModes }}
          - {{ . | quote }}
        {{- end }}
        resources:
          requests:
            storage: {{ .Values.persistence.size | quote }}
        {{ include "mongodb.storageClass" . }}
{{- else }}
        - name: data
          emptyDir: {}
{{- end }}
{{- end }}
{{- end -}}
