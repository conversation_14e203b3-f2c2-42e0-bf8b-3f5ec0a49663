{{- if .Values.replicaSet.enabled }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ template "mongodb.fullname" . }}-primary
  labels:
    app: {{ template "mongodb.name" . }}
    chart: {{ template "mongodb.chart" . }}
    heritage: {{ .Release.Service }}
    release: {{ .Release.Name }}
    {{- with .Values.labels }}
{{ toYaml . | indent 4 }}
    {{- end }}
  {{- with .Values.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
  {{- end }}
spec:
  serviceName: {{ template "mongodb.fullname" . }}-headless
  replicas: 1
  updateStrategy:
    type: {{ .Values.updateStrategy.type }}
    {{- if (eq "Recreate" .Values.updateStrategy.type) }}
    rollingUpdate: null
    {{- end }}
  selector:
    matchLabels:
      app: {{ template "mongodb.name" . }}
      release: {{ .Release.Name }}
      component: primary
  template:
    metadata:
      labels:
        app: {{ template "mongodb.name" . }}
        chart: {{ template "mongodb.chart" . }}
        release: {{ .Release.Name }}
        component: primary
      {{- if .Values.podLabels }}
{{ toYaml .Values.podLabels | indent 8 }}
      {{- end }}
      {{- if or .Values.podAnnotations .Values.metrics.enabled }}
      annotations:
{{- if .Values.podAnnotations }}
{{ toYaml .Values.podAnnotations | indent 8 }}
{{- end }}
{{- if .Values.metrics.enabled }}
{{ toYaml .Values.metrics.podAnnotations | indent 8 }}
{{- end }}
      {{- end }}
    spec:
      {{- if .Values.schedulerName }}
      schedulerName: "{{ .Values.schedulerName }}"
      {{- end }}
      {{- if .Values.priorityClassName }}
      priorityClassName: {{ .Values.priorityClassName }}
      {{- end }}
      {{- if .Values.securityContext.enabled }}
      securityContext:
        fsGroup: {{ .Values.securityContext.fsGroup }}
      {{- end }}
      {{- if .Values.affinity }}
      affinity:
{{ toYaml .Values.affinity | indent 8 }}
      {{- end -}}
      {{- if .Values.nodeSelector }}
      nodeSelector:
{{ toYaml .Values.nodeSelector | indent 8 }}
      {{- end }}
      {{- if .Values.tolerations }}
      tolerations:
{{ toYaml .Values.tolerations | indent 8 }}
      {{- end }}
{{- include "mongodb.imagePullSecrets" . | indent 6 }}
      initContainers:
      {{- if .Values.extraInitContainers }}
{{ tpl .Values.extraInitContainers . | indent 6}}
      {{- end }}
      {{- if and .Values.volumePermissions.enabled .Values.persistence.enabled }}
      - name: volume-permissions
        image: {{ template "mongodb.volumePermissions.image" . }}
        imagePullPolicy: {{ .Values.volumePermissions.image.pullPolicy | quote }}
        command: ["chown", "-R", "{{ .Values.securityContext.runAsUser }}:{{ .Values.securityContext.fsGroup }}", "{{ .Values.persistence.mountPath }}"]
        securityContext:
          runAsUser: 0
        resources: {{ toYaml .Values.volumePermissions.resources | nindent 10 }}
        volumeMounts:
        - name: datadir
          mountPath: {{ .Values.persistence.mountPath }}
      {{- end }}
      containers:
        - name: {{ template "mongodb.name" . }}-primary
          image: {{ template "mongodb.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- if .Values.securityContext.enabled }}
          securityContext:
            runAsNonRoot: true
            runAsUser: {{ .Values.securityContext.runAsUser }}
          {{- end }}
          ports:
          - containerPort: {{ .Values.service.port }}
            name: mongodb
          env:
          {{- if .Values.image.debug}}
          - name: BITNAMI_DEBUG
            value: "true"
          {{- end }}
          - name: MONGODB_SYSTEM_LOG_VERBOSITY
            value: {{ .Values.mongodbSystemLogVerbosity | quote }}
          - name: MONGODB_DISABLE_SYSTEM_LOG
            {{- if .Values.mongodbDisableSystemLog }}
            value: "yes"
            {{- else }}
            value: "no"
            {{- end }}
          - name: MONGODB_POD_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.name
          - name: MONGODB_REPLICA_SET_MODE
            value: "primary"
          - name: MONGODB_REPLICA_SET_NAME
            value: {{ .Values.replicaSet.name | quote }}
            {{- if .Values.replicaSet.useHostnames }}
          - name: MONGODB_ADVERTISED_HOSTNAME
            value: "$(MONGODB_POD_NAME).{{ template "mongodb.fullname" . }}-headless.{{ .Release.Namespace }}.svc.{{ .Values.clusterDomain }}"
            {{- end }}
          {{- if .Values.mongodbUsername }}
          - name: MONGODB_USERNAME
            value: {{ .Values.mongodbUsername | quote }}
          {{- end }}
          {{- if .Values.mongodbDatabase }}
          - name: MONGODB_DATABASE
            value: {{ .Values.mongodbDatabase | quote }}
          {{- end }}
            {{- if .Values.usePassword }}
            {{- if and .Values.mongodbUsername .Values.mongodbDatabase }}
          - name: MONGODB_PASSWORD
            valueFrom:
              secretKeyRef:
                name: {{ if .Values.existingSecret }}{{ .Values.existingSecret }}{{- else }}{{ template "mongodb.fullname" . }}{{- end }}
                key: mongodb-password
            {{- end }}
          - name: MONGODB_ROOT_PASSWORD
            valueFrom:
              secretKeyRef:
                name: {{ if .Values.existingSecret }}{{ .Values.existingSecret }}{{- else }}{{ template "mongodb.fullname" . }}{{- end }}
                key: mongodb-root-password
          - name: MONGODB_REPLICA_SET_KEY
            valueFrom:
              secretKeyRef:
                name: {{ if .Values.existingSecret }}{{ .Values.existingSecret }}{{- else }}{{ template "mongodb.fullname" . }}{{- end }}
                key: mongodb-replica-set-key
            {{- end }}
          - name: MONGODB_ENABLE_IPV6
          {{- if .Values.mongodbEnableIPv6 }}
            value: "yes"
          {{- else }}
            value: "no"
          {{- end }}
          - name: MONGODB_ENABLE_DIRECTORY_PER_DB
          {{- if .Values.mongodbDirectoryPerDB }}
            value: "yes"
          {{- else }}
            value: "no"
          {{- end }}
          {{- if .Values.mongodbExtraFlags }}
          - name: MONGODB_EXTRA_FLAGS
            value: {{ .Values.mongodbExtraFlags | join " " | quote }}
          {{- end }}
          {{- if .Values.extraEnvVars }}
          {{- include "mongodb.tplValue" ( dict "value" .Values.extraEnvVars "context" $ ) | nindent 10 }}
          {{- end }}
          {{- if .Values.livenessProbe.enabled }}
          livenessProbe:
            exec:
              command:
                - pgrep
                - mongod
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.livenessProbe.successThreshold }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.readinessProbe.enabled }}
          readinessProbe:
            exec:
              command:
                - mongo
                - --eval
                - "db.adminCommand('ping')"
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.readinessProbe.successThreshold }}
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
          {{- end }}
          volumeMounts:
            - name: datadir
              mountPath: {{ .Values.persistence.mountPath }}
              subPath: {{ .Values.persistence.subPath }}
            {{- if  or (.Files.Glob "files/docker-entrypoint-initdb.d/*[sh|js|json]") (.Values.initConfigMap) }}
            - name: custom-init-scripts
              mountPath: /docker-entrypoint-initdb.d
            {{- end }}
            {{- if .Values.configmap }}
            - name: config
              mountPath: /opt/bitnami/mongodb/conf/mongodb.conf
              subPath: mongodb.conf
            {{- end }}
          {{- if .Values.extraVolumeMounts }}
{{ toYaml .Values.extraVolumeMounts | indent 12}}
          {{- end }}
          resources:
{{ toYaml .Values.resources | indent 12 }}
{{- if .Values.metrics.enabled }}
        - name: metrics
          image: {{ template "mongodb.metrics.image" . }}
          imagePullPolicy: {{ .Values.metrics.image.pullPolicy | quote }}
          {{- if .Values.securityContext.enabled }}
          securityContext:
            runAsNonRoot: true
            runAsUser: {{ .Values.securityContext.runAsUser }}
          {{- end }}
          env:
          {{- if .Values.usePassword }}
          - name: MONGODB_ROOT_PASSWORD
            valueFrom:
              secretKeyRef:
                name: {{ if .Values.existingSecret }}{{ .Values.existingSecret }}{{- else }}{{ template "mongodb.fullname" . }}{{- end }}
                key: mongodb-root-password
          command: [ 'sh', '-c', '/bin/mongodb_exporter --mongodb.uri mongodb://root:${MONGODB_ROOT_PASSWORD}@localhost:{{ .Values.service.port }}/admin {{ .Values.metrics.extraArgs }}' ]
          {{- else }}
          command: [ 'sh', '-c', '/bin/mongodb_exporter --mongodb.uri mongodb://localhost:{{ .Values.service.port }} {{ .Values.metrics.extraArgs }}' ]
          {{- end }}
          ports:
          - name: metrics
            containerPort: 9216
          {{- if .Values.metrics.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: /metrics
              port: metrics
            initialDelaySeconds: {{ .Values.metrics.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.metrics.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.metrics.livenessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.metrics.livenessProbe.failureThreshold }}
            successThreshold: {{ .Values.metrics.livenessProbe.successThreshold }}
          {{- end }}
          {{- if .Values.metrics.readinessProbe.enabled }}
          readinessProbe:
            httpGet:
              path: /metrics
              port: metrics
            initialDelaySeconds: {{ .Values.metrics.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.metrics.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.metrics.readinessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.metrics.readinessProbe.failureThreshold }}
            successThreshold: {{ .Values.metrics.readinessProbe.successThreshold }}
          {{- end }}
          resources:
{{ toYaml .Values.metrics.resources | indent 12 }}
{{- end }}
{{- if .Values.sidecars }}
{{ toYaml .Values.sidecars | indent 8 }}
{{- end }}
      volumes:
        {{- if (.Files.Glob "files/docker-entrypoint-initdb.d/*[sh|js|json]") }}
        - name: custom-init-scripts
          configMap:
            name: {{ template "mongodb.fullname" . }}-init-scripts
        {{- end }}
        {{- if (.Values.initConfigMap) }}
        - name: custom-init-scripts
          configMap:
            name: {{ .Values.initConfigMap.name }}
        {{- end }}
        {{- if .Values.configmap }}
        - name: config
          configMap:
            name: {{ template "mongodb.fullname" . }}
        {{- end }}
        {{- if .Values.extraVolumes }}
{{ toYaml .Values.extraVolumes | indent 8}}
        {{- end }}
{{- if .Values.persistence.enabled }}
  volumeClaimTemplates:
    - metadata:
        name: datadir
        annotations:
        {{- range $key, $value := .Values.persistence.annotations }}
          {{ $key }}: "{{ $value }}"
        {{- end }}
      spec:
        accessModes:
        {{- range .Values.persistence.accessModes }}
          - {{ . | quote }}
        {{- end }}
        resources:
          requests:
            storage: {{ .Values.persistence.size | quote }}
        {{ include "mongodb.storageClass" . }}
{{- else }}
        - name: datadir
          emptyDir: {}
{{- end }}
{{- end }}
