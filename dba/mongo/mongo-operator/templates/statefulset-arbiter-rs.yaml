{{- if .Values.replicaSet.enabled }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ template "mongodb.fullname" . }}-arbiter
  labels:
    app: {{ template "mongodb.name" . }}
    chart: {{ template "mongodb.chart" . }}
    heritage: {{ .Release.Service }}
    release: {{ .Release.Name }}
    {{- with .Values.labels }}
{{ toYaml . | indent 4 }}
    {{- end }}
  {{- with .Values.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
  {{- end }}
spec:
  selector:
    matchLabels:
      app: {{ template "mongodb.name" . }}
      release: {{ .Release.Name }}
      component: arbiter
  serviceName: {{ template "mongodb.fullname" . }}-headless
  replicas: {{ .Values.replicaSet.replicas.arbiter }}
  updateStrategy:
    type: {{ .Values.updateStrategy.type }}
    {{- if (eq "Recreate" .Values.updateStrategy.type) }}
    rollingUpdate: null
    {{- end }}
  template:
    metadata:
      labels:
        app: {{ template "mongodb.name" . }}
        chart: {{ template "mongodb.chart" . }}
        release: {{ .Release.Name }}
        component: arbiter
      {{- if .Values.podLabels }}
{{ toYaml .Values.podLabels | indent 8 }}
      {{- end }}
      {{- if .Values.podAnnotations }}
      annotations:
{{ toYaml .Values.podAnnotations | indent 8 }}
      {{- end }}
    spec:
      {{- if .Values.schedulerName }}
      schedulerName: "{{ .Values.schedulerName }}"
      {{- end }}
      {{- if .Values.priorityClassName }}
      priorityClassName: {{ .Values.priorityClassName }}
      {{- end }}
      {{- if .Values.securityContext.enabled }}
      securityContext:
        fsGroup: {{ .Values.securityContext.fsGroup }}
      {{- end }}
      {{- if .Values.affinityArbiter }}
      affinity:
{{ toYaml .Values.affinityArbiter | indent 8 }}
      {{- end -}}
      {{- if .Values.nodeSelector }}
      nodeSelector:
{{ toYaml .Values.nodeSelector | indent 8 }}
      {{- end }}
      {{- if .Values.tolerations }}
      tolerations:
{{ toYaml .Values.tolerations | indent 8 }}
      {{- end }}
{{- include "mongodb.imagePullSecrets" . | indent 6 }}
      {{- if .Values.extraInitContainers }}
      initContainers:
{{ tpl .Values.extraInitContainers . | indent 6}}
      {{- end }}
      containers:
        - name: {{ template "mongodb.name" . }}-arbiter
          image: {{ template "mongodb.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- if .Values.securityContext.enabled }}
          securityContext:
            runAsNonRoot: true
            runAsUser: {{ .Values.securityContext.runAsUser }}
          {{- end }}
          ports:
          - containerPort: {{ .Values.service.port }}
            name: mongodb
          env:
          {{- if .Values.image.debug}}
          - name: BITNAMI_DEBUG
            value: "true"
          {{- end }}
          - name: MONGODB_SYSTEM_LOG_VERBOSITY
            value: {{ .Values.mongodbSystemLogVerbosity | quote }}
          - name: MONGODB_DISABLE_SYSTEM_LOG
            {{- if .Values.mongodbDisableSystemLog }}
            value: "yes"
            {{- else }}
            value: "no"
            {{- end }}
          - name: MONGODB_POD_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.name
          - name: MONGODB_REPLICA_SET_MODE
            value: "arbiter"
          - name: MONGODB_PRIMARY_HOST
            value: {{ template "mongodb.fullname" . }}
          - name: MONGODB_REPLICA_SET_NAME
            value: {{ .Values.replicaSet.name | quote }}
            {{- if .Values.replicaSet.useHostnames }}
          - name: MONGODB_ADVERTISED_HOSTNAME
            value: "$(MONGODB_POD_NAME).{{ template "mongodb.fullname" . }}-headless.{{ .Release.Namespace }}.svc.{{ .Values.clusterDomain }}"
            {{- end }}
            {{- if .Values.usePassword }}
          - name: MONGODB_PRIMARY_ROOT_PASSWORD
            valueFrom:
              secretKeyRef:
                name: {{ if .Values.existingSecret }}{{ .Values.existingSecret }}{{- else }}{{ template "mongodb.fullname" . }}{{- end }}
                key: mongodb-root-password
          - name: MONGODB_REPLICA_SET_KEY
            valueFrom:
              secretKeyRef:
                name: {{ if .Values.existingSecret }}{{ .Values.existingSecret }}{{- else }}{{ template "mongodb.fullname" . }}{{- end }}
                key: mongodb-replica-set-key
            {{- end }}
          - name: MONGODB_ENABLE_IPV6
          {{- if .Values.mongodbEnableIPv6 }}
            value: "yes"
          {{- else }}
            value: "no"
          {{- end }}
          - name: MONGODB_ENABLE_DIRECTORY_PER_DB
          {{- if .Values.mongodbDirectoryPerDB }}
            value: "yes"
          {{- else }}
            value: "no"
          {{- end }}
          {{- if .Values.mongodbExtraFlags }}
          - name: MONGODB_EXTRA_FLAGS
            value: {{ .Values.mongodbExtraFlags | join " " | quote }}
          {{- end }}
          {{- if .Values.extraEnvVars }}
          {{- include "mongodb.tplValue" ( dict "value" .Values.extraEnvVars "context" $ ) | nindent 10 }}
          {{- end }}
          {{- if .Values.livenessProbe.enabled }}
          livenessProbe:
            tcpSocket:
              port: mongodb
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.livenessProbe.successThreshold }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.readinessProbe.enabled }}
          readinessProbe:
            tcpSocket:
              port: mongodb
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.readinessProbe.successThreshold }}
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
          {{- end }}
          volumeMounts:
          {{- if .Values.configmap }}
            - name: config
              mountPath: /opt/bitnami/mongodb/conf/mongodb.conf
              subPath: mongodb.conf
          {{- end }}
          resources:
{{ toYaml .Values.resourcesArbiter | indent 12 }}
{{- if .Values.extraVolumeMountsArbiter }}
          volumeMounts:
{{ toYaml .Values.extraVolumeMountsArbiter | indent 12}}
{{- end }}
{{- if .Values.sidecarsArbiter }}
{{ toYaml .Values.sidecarsArbiter | indent 8 }}
{{- end }}
      volumes:
      {{- if .Values.configmap }}
        - name: config
          configMap:
            name: {{ template "mongodb.fullname" . }}
      {{- end }}
      {{- if .Values.extraVolumesArbiter }}
{{ toYaml .Values.extraVolumesArbiter | indent 8 }}
      {{- end }}
{{- end }}
