annotations:
  category: Database
  images: |
    - name: kubectl
      image: docker.io/bitnami/kubectl:1.31.3-debian-12-r1
    - name: mongodb
      image: docker.io/bitnami/mongodb:8.0.4-debian-12-r0
    - name: mongodb-exporter
      image: docker.io/bitnami/mongodb-exporter:0.43.0-debian-12-r0
    - name: nginx
      image: docker.io/bitnami/nginx:1.27.3-debian-12-r0
    - name: os-shell
      image: docker.io/bitnami/os-shell:12-debian-12-r33
  licenses: Apache-2.0
apiVersion: v2
appVersion: 8.0.4
dependencies:
- name: common
  repository: oci://registry-1.docker.io/bitnamicharts
  tags:
  - bitnami-common
  version: 2.x.x
description: MongoDB(R) is a relational open source NoSQL database. Easy to use, it
  stores data in JSON-like documents. Automated scalability and high-performance.
  Ideal for developing cloud native applications.
home: https://bitnami.com
icon: https://bitnami.com/assets/stacks/mongodb/img/mongodb-stack-220x234.png
keywords:
- mongodb
- database
- nosql
- cluster
- replicaset
- replication
maintainers:
- name: Broadcom, Inc. All Rights Reserved.
  url: https://github.com/bitnami/charts
name: mongodb
sources:
- https://github.com/bitnami/charts/tree/main/bitnami/mongodb
version: 16.3.3
