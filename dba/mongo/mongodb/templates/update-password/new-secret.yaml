{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.passwordUpdateJob.enabled (include "mongodb.createSecret" .) (not ( include "mongodb.createPreviousSecret" . )) (not .Values.passwordUpdateJob.previousPasswords.existingSecret) }}
{{- $rootPassword := .Values.auth.rootPassword }}
{{- $metricsPassword := .Values.metrics.password }}
{{- $customUsers := include "mongodb.customUsers" . }}
{{- $customPasswords := join "," (include "mongodb.customPasswords" .) }}
{{- $customDatabases := include "mongodb.customDatabases" . }}
{{- $replicationPassword := .Values.auth.replicationPassword }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-new-secret" (include "common.names.fullname" .) | trunc 63 | trimSuffix "-" }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/part-of: mongodb
  {{- $defaultAnnotations := dict "helm.sh/hook" "pre-upgrade" "helm.sh/hook-delete-policy" "hook-succeeded" }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.commonAnnotations $defaultAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
type: Opaque
data:
  mongodb-root-password: {{ required "The new root password is required!" $rootPassword | b64enc | quote }}
  {{- if and (not (empty $customUsers)) (not (empty $customDatabases)) }}
  mongodb-passwords: {{ required "The new custom passwords are required!" $customPasswords | b64enc | quote }}
  {{- end }}
  {{- if .Values.metrics.username }}
  mongodb-metrics-password: {{ required "The new metrics password is required!" $metricsPassword | b64enc | quote }}
  {{- end }}
{{- end }}
