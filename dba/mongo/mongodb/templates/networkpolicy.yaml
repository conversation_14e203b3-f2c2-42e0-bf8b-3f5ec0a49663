{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.networkPolicy.enabled }}
kind: NetworkPolicy
apiVersion: {{ include "common.capabilities.networkPolicy.apiVersion" . }}
metadata:
  name: {{ include "mongodb.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  {{- $labels := include "common.tplvalues.merge" ( dict "values" ( list .Values.labels .Values.commonLabels ) "context" . ) }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $labels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: mongodb
  {{- if or .Values.annotations .Values.commonAnnotation }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.podLabels .Values.commonLabels ) "context" . ) }}
  podSelector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: mongodb
  policyTypes:
    - Ingress
    - Egress
  {{- if .Values.networkPolicy.allowExternalEgress }}
  egress:
    - {}
  {{- else }}
  egress:
    # Allow dns resolution
    - ports:
        - port: 53
          protocol: UDP
        - port: 53
          protocol: TCP
    # Allow connection to other cluster pods
    - ports:
        - port: {{ .Values.containerPorts.mongodb }}
      to:
        - podSelector:
            matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 14 }}
    {{- if (and .Values.externalAccess.enabled .Values.externalAccess.autoDiscovery.enabled) }}
    {{- $kubernetesEndpoints := lookup "v1" "Endpoints" (include "common.names.namespace" .) "kubernetes" }}
    {{- range $kubernetesEndpoints.subsets }}
    # Allow connection to API server, required by auto-discovery containers
    - ports:
      {{- range .ports }}
        - port: {{ .port }}
      {{- end }}
      to:
        {{- range .addresses }}
        - ipBlock:
            cidr: {{ printf "%s/32" .ip }}
        {{- end }}
    {{- end }}
    {{- end }}
    {{- if .Values.networkPolicy.extraEgress }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.rts.networkPolicy.extraEgress "context" $ ) | nindent 4 }}
    {{- end }}
  {{- end }}
  ingress:
    - ports:
        - port: {{ .Values.containerPorts.mongodb }}
      {{- if .Values.metrics.enabled }}
        - port: {{ .Values.metrics.containerPort }}
      {{- end }}
      {{- if not .Values.networkPolicy.allowExternal }}
      from:
        - podSelector:
            matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 14 }}
              app.kubernetes.io/component: mongodb
        {{- if .Values.networkPolicy.addExternalClientAccess }}
        - podSelector:
            matchLabels: 
              {{ template "common.names.fullname" . }}-client: "true"
        {{- end }}
        {{- if .Values.networkPolicy.ingressPodMatchLabels }}
        - podSelector:
            matchLabels: {{- include "common.tplvalues.render" (dict "value" .Values.networkPolicy.ingressPodMatchLabels "context" $ ) | nindent 14 }}
        {{- end }}
        {{- $ingressNSMatchLabels := coalesce .Values.networkPolicy.ingressNSMatchLabels .Values.networkPolicy.namespaceSelector }}
        {{- if $ingressNSMatchLabels }}
        - namespaceSelector:
            matchLabels: {{- include "common.tplvalues.render" (dict "value" $ingressNSMatchLabels "context" $ ) | nindent 14 }}
          {{- $ingressNSPodMatchLabels := coalesce .Values.networkPolicy.ingressNSPodMatchLabels .Values.networkPolicy.podSelector }}
          {{- if $ingressNSPodMatchLabels }}
          podSelector:
            matchLabels: {{- include "common.tplvalues.render" (dict "value" $ingressNSPodMatchLabels "context" $ ) | nindent 14 }}
          {{- end }}
        {{- end }}
      {{- end }}
    {{- $extraIngress := coalesce .Values.networkPolicy.extraIngress .Values.networkPolicy.customRules }}
    {{- if $extraIngress }}
    {{- include "common.tplvalues.render" ( dict "value" $extraIngress "context" $ ) | nindent 4 }}
    {{- end }}
{{- end }}