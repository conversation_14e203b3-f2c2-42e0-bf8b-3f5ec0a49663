apiVersion: v1
kind: ConfigMap
metadata:
  name: etcd-workbench-config

data:
  etcd-workbench.conf: |
    [server]
    # Configure the port the service will run on.
    port = 8002
    # Configure the timeout for executing instructions to ETCD server, in milliseconds.
    etcdExecuteTimeoutMillis = 3000
    # Configure data storage directory.
    dataDir = ./data
    # If Authentication is turned on, in order to ensure that user data is not easily cracked,
    # configure the data signature key to encrypt and protect it. It must be 16 characters.
    configEncryptKey = etcdWorkbench@*?

    [auth]
    # If set to true, user must log in to use etcd workbench, and add the user field to configure the user.
    # If set to false, all connection data can be used and shared by anyone!!!
    enable = false
    # If enabled authentication, add username and password with `user` field.
    # Supports repeatedly adding multiple `user` fields.
    user = username1:password1
    user = username2:password2

    [log]
    # Base log level
    level = INFO
    # Customize the log level of the specified path.
    levels = io.netty:INFO,io.grpc:INFO
    # Configure log storage directory.
    file = ./logs
    # Configure log file name.
    fileName = etcd-workbench
    # Configure the log file rolling size. When this size is exceeded, a new file will be created to store the log.
    # Unit MB
    fileLimitSize = 10
    # Support: `std` and `file`
    printers = std,file
