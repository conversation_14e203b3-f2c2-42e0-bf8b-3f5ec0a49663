# etcd-single.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: etcd-single
  namespace: dbv
  labels:
    app: etcd-single
spec:
  replicas: 1
  selector:
    matchLabels:
      app: etcd-single
  template:
    metadata:
      labels:
        app: etcd-single
    spec:
      containers:
      - name: etcd-single
        image: gcr.io/etcd-development/etcd:v3.4.25
        ports:
        - containerPort: 2379
        - containerPort: 2380
        command: ["/usr/local/bin/etcd"]
        args: ["--config-file", "/etc/etcd/etcd.yml"]
        env:
        - name: ETCDCTL_API
          value: "3"
        volumeMounts:
        - name: etcd-data
          mountPath: /var/lib/etcd
        - name: etcd-config-volume
          mountPath: /etc/etcd/etcd.yml
          subPath: etcd.yml
      volumes:
        - name: etcd-data
          persistentVolumeClaim:
            claimName: etcd-pvc
        - name: etcd-config-volume
          configMap:
            name: etcd-config

---
apiVersion: v1
kind: Service
metadata:
  name: etcd-single
  namespace: dbv
spec:
  type: LoadBalancer  # 修改为 LoadBalancer
  selector:
    app: etcd-single
  ports:
    - name: client
      protocol: TCP
      port: 2379
      targetPort: 2379
    - name: peer
      protocol: TCP
      port: 2380
      targetPort: 2380
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: etcd-pvc
  namespace: dbv
spec:
  storageClassName: "local-path"
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi # 可根据需求调整存储空间大小
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: etcd-config
  namespace: dbv
data:
  etcd.yml: |
    name: s1
    data-dir: /var/lib/etcd
    listen-client-urls: http://0.0.0.0:2379
    advertise-client-urls: http://0.0.0.0:2379
    listen-peer-urls: http://0.0.0.0:2380
    initial-advertise-peer-urls: http://0.0.0.0:2380
    initial-cluster: s1=http://0.0.0.0:2380
    initial-cluster-token: etcd-cluster-token
    initial-cluster-state: new
