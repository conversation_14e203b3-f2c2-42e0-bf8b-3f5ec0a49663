{{- if eq .Values.architecture "replication" }}
apiVersion: {{ include "common.capabilities.statefulset.apiVersion" . }}
kind: StatefulSet
metadata:
  name: {{ include "mysql.secondary.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: secondary
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.secondary.podLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.secondary.podLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.secondary.replicaCount }}
  selector:
    matchLabels: {{ include "common.labels.matchLabels" . | nindent 6 }}
      app.kubernetes.io/component: secondary
  serviceName: {{ include "mysql.secondary.fullname" . }}
  updateStrategy:
    type: {{ .Values.secondary.updateStrategy }}
    {{- if (eq "Recreate" .Values.secondary.updateStrategy) }}
    rollingUpdate: null
    {{- else if .Values.secondary.rollingUpdatePartition }}
    rollingUpdate:
      partition: {{ .Values.secondary.rollingUpdatePartition }}
    {{- end }}
  template:
    metadata:
      annotations:
        {{- if (include "mysql.secondary.createConfigmap" .) }}
        checksum/configuration: {{ include (print $.Template.BasePath "/secondary/configmap.yaml") . | sha256sum }}
        {{- end }}
        {{- if .Values.secondary.podAnnotations }}
        {{- include "common.tplvalues.render" (dict "value" .Values.secondary.podAnnotations "context" $) | nindent 8 }}
        {{- end }}
      labels: {{- include "common.labels.standard" . | nindent 8 }}
        app.kubernetes.io/component: secondary
        {{- if .Values.commonLabels }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 8 }}
        {{- end }}
        {{- if .Values.secondary.podLabels }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.secondary.podLabels "context" $ ) | nindent 8 }}
        {{- end }}
    spec:
      {{- include "mysql.imagePullSecrets" . | nindent 6 }}
      {{- if .Values.secondary.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.secondary.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.schedulerName }}
      schedulerName: {{ .Values.schedulerName | quote }}
      {{- end }}
      serviceAccountName: {{ include "mysql.serviceAccountName" . }}
      {{- if .Values.secondary.affinity }}
      affinity: {{- include "common.tplvalues.render" (dict "value" .Values.secondary.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.secondary.podAffinityPreset "component" "secondary" "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.secondary.podAntiAffinityPreset "component" "secondary" "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.secondary.nodeAffinityPreset.type "key" .Values.secondary.nodeAffinityPreset.key "values" .Values.secondary.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.secondary.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" (dict "value" .Values.secondary.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.secondary.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.secondary.tolerations "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.priorityClassName }}
      priorityClassName: {{ .Values.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.secondary.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.secondary.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      {{- if or .Values.secondary.initContainers (and .Values.secondary.podSecurityContext.enabled .Values.volumePermissions.enabled .Values.secondary.persistence.enabled) }}
      initContainers:
        {{- if .Values.secondary.initContainers }}
        {{- include "common.tplvalues.render" (dict "value" .Values.secondary.initContainers "context" $) | nindent 8 }}
        {{- end }}
        {{- if and .Values.secondary.podSecurityContext.enabled .Values.volumePermissions.enabled .Values.secondary.persistence.enabled }}
        - name: volume-permissions
          image: {{ include "mysql.volumePermissions.image" . }}
          imagePullPolicy: {{ .Values.volumePermissions.image.pullPolicy | quote }}
          command:
            - /bin/bash
            - -ec
            - |
              chown -R {{ .Values.secondary.containerSecurityContext.runAsUser }}:{{ .Values.secondary.podSecurityContext.fsGroup }} /bitnami/mysql
          securityContext:
            runAsUser: 0
          {{- if .Values.volumePermissions.resources }}
          resources: {{- toYaml .Values.volumePermissions.resources | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: data
              mountPath: /bitnami/mysql
        {{- end }}
      {{- end }}
      containers:
        - name: mysql
          image: {{ include "mysql.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
          {{- if .Values.secondary.containerSecurityContext.enabled }}
          securityContext: {{- omit .Values.secondary.containerSecurityContext "enabled" | toYaml | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 12 }}
          {{- else if .Values.secondary.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.secondary.command "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 12 }}
          {{- else if .Values.secondary.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.secondary.args "context" $) | nindent 12 }}
          {{- end }}
          env:
            - name: BITNAMI_DEBUG
              value: {{ ternary "true" "false" (or .Values.image.debug .Values.diagnosticMode.enabled) | quote }}
            - name: MYSQL_REPLICATION_MODE
              value: "slave"
            - name: MYSQL_MASTER_HOST
              value: {{ include "mysql.primary.fullname" . }}
            - name: MYSQL_MASTER_PORT_NUMBER
              value: {{ .Values.primary.service.port | quote }}
            - name: MYSQL_MASTER_ROOT_USER
              value: "root"
            - name: MYSQL_REPLICATION_USER
              value: {{ .Values.auth.replicationUser | quote }}
            {{- if .Values.auth.usePasswordFiles }}
            - name: MYSQL_MASTER_ROOT_PASSWORD_FILE
              value: {{ default "/opt/bitnami/mysql/secrets/mysql-root-password" .Values.auth.customPasswordFiles.root }}
            - name: MYSQL_REPLICATION_PASSWORD_FILE
              value: {{ default "/opt/bitnami/mysql/secrets/mysql-replication-password" .Values.auth.customPasswordFiles.replicator }}
            {{- else }}
            - name: MYSQL_MASTER_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ template "mysql.secretName" . }}
                  key: mysql-root-password
            - name: MYSQL_REPLICATION_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ template "mysql.secretName" . }}
                  key: mysql-replication-password
            {{- end }}
            {{- if .Values.secondary.extraFlags }}
            - name: MYSQL_EXTRA_FLAGS
              value: "{{ .Values.secondary.extraFlags }}"
            {{- end }}
            {{- if .Values.secondary.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.secondary.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          {{- if or .Values.secondary.extraEnvVarsCM .Values.secondary.extraEnvVarsSecret }}
          envFrom:
            {{- if .Values.secondary.extraEnvVarsCM }}
            - configMapRef:
                name: {{ .Values.secondary.extraEnvVarsCM }}
            {{- end }}
            {{- if .Values.secondary.extraEnvVarsSecret }}
            - secretRef:
                name: {{ .Values.secondary.extraEnvVarsSecret }}
            {{- end }}
          {{- end }}
          ports:
            - name: mysql
              containerPort: 3306
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.secondary.livenessProbe.enabled }}
          livenessProbe: {{- omit .Values.secondary.livenessProbe "enabled" | toYaml | nindent 12 }}
            exec:
              command:
                - /bin/bash
                - -ec
                - |
                  password_aux="${MYSQL_MASTER_ROOT_PASSWORD:-}"
                  if [[ -f "${MYSQL_MASTER_ROOT_PASSWORD_FILE:-}" ]]; then
                      password_aux=$(cat "$MYSQL_MASTER_ROOT_PASSWORD_FILE")
                  fi
                  mysqladmin status -uroot -p"${password_aux}"
          {{- else if .Values.secondary.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.secondary.customLivenessProbe "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.secondary.readinessProbe.enabled }}
          readinessProbe: {{- omit .Values.secondary.readinessProbe "enabled" | toYaml | nindent 12 }}
            exec:
              command:
                - /bin/bash
                - -ec
                - |
                  password_aux="${MYSQL_MASTER_ROOT_PASSWORD:-}"
                  if [[ -f "${MYSQL_MASTER_ROOT_PASSWORD_FILE:-}" ]]; then
                      password_aux=$(cat "$MYSQL_MASTER_ROOT_PASSWORD_FILE")
                  fi
                  mysqladmin status -uroot -p"${password_aux}"
          {{- else if .Values.secondary.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.secondary.customReadinessProbe "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.secondary.startupProbe.enabled }}
          startupProbe: {{- omit .Values.secondary.startupProbe "enabled" | toYaml | nindent 12 }}
            exec:
              command:
                - /bin/bash
                - -ec
                - |
                  password_aux="${MYSQL_MASTER_ROOT_PASSWORD:-}"
                  if [[ -f "${MYSQL_MASTER_ROOT_PASSWORD_FILE:-}" ]]; then
                      password_aux=$(cat "$MYSQL_MASTER_ROOT_PASSWORD_FILE")
                  fi
                  mysqladmin status -uroot -p"${password_aux}"
          {{- else if .Values.secondary.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.secondary.customStartupProbe "context" $) | nindent 12 }}
          {{- end }}
          {{- end }}
          {{- if .Values.secondary.resources }}
          resources: {{ toYaml .Values.secondary.resources | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: data
              mountPath: /bitnami/mysql
            {{- if or .Values.secondary.configuration .Values.secondary.existingConfigmap }}
            - name: config
              mountPath: /opt/bitnami/mysql/conf/my.cnf
              subPath: my.cnf
            {{- end }}
            {{- if and .Values.auth.usePasswordFiles (not .Values.auth.customPasswordFiles) }}
            - name: mysql-credentials
              mountPath: /opt/bitnami/mysql/secrets/
            {{- end }}
            {{- if .Values.secondary.extraVolumeMounts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.secondary.extraVolumeMounts "context" $) | nindent 12 }}
            {{- end }}
        {{- if .Values.metrics.enabled }}
        - name: metrics
          image: {{ include "mysql.metrics.image" . }}
          imagePullPolicy: {{ .Values.metrics.image.pullPolicy | quote }}
          env:
            {{- if .Values.auth.usePasswordFiles }}
            - name: MYSQL_ROOT_PASSWORD_FILE
              value: {{ default "/opt/bitnami/mysqld-exporter/secrets/mysql-root-password" .Values.auth.customPasswordFiles.root }}
            {{- else }}
            - name: MYSQL_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ template "mysql.secretName" . }}
                  key: mysql-root-password
            {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 12 }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 12 }}
          {{- else }}
          command:
            - /bin/bash
            - -ec
            - |
              password_aux="${MYSQL_ROOT_PASSWORD:-}"
              if [[ -f "${MYSQL_ROOT_PASSWORD_FILE:-}" ]]; then
                  password_aux=$(cat "$MYSQL_ROOT_PASSWORD_FILE")
              fi
              DATA_SOURCE_NAME="root:${password_aux}@(localhost:3306)/" /bin/mysqld_exporter {{- range .Values.metrics.extraArgs.secondary }} {{ . }} {{- end }}
          {{- end }}
          ports:
            - name: metrics
              containerPort: 9104
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.metrics.livenessProbe.enabled }}
          livenessProbe: {{- omit .Values.metrics.livenessProbe "enabled" | toYaml | nindent 12 }}
            httpGet:
              path: /metrics
              port: metrics
          {{- end }}
          {{- if .Values.metrics.readinessProbe.enabled }}
          readinessProbe: {{- omit .Values.metrics.readinessProbe "enabled" | toYaml | nindent 12 }}
            httpGet:
              path: /metrics
              port: metrics
          {{- end }}
          {{- end }}
          {{- if .Values.metrics.resources }}
          resources: {{- toYaml .Values.metrics.resources | nindent 12 }}
          {{- end }}
          {{- if and .Values.auth.usePasswordFiles (not .Values.auth.customPasswordFiles) }}
          volumeMounts:
            - name: mysql-credentials
              mountPath: /opt/bitnami/mysqld-exporter/secrets/
          {{- end }}
        {{- end }}
        {{- if .Values.secondary.sidecars }}
        {{- include "common.tplvalues.render" (dict "value" .Values.secondary.sidecars "context" $) | nindent 8 }}
        {{- end }}
      volumes:
        {{- if or .Values.secondary.configuration .Values.secondary.existingConfigmap }}
        - name: config
          configMap:
            name: {{ include "mysql.secondary.configmapName" . }}
        {{- end }}
        {{- if and .Values.auth.usePasswordFiles (not .Values.auth.customPasswordFiles) }}
        - name: mysql-credentials
          secret:
            secretName: {{ template "mysql.secretName" . }}
            items:
              - key: mysql-root-password
                path: mysql-root-password
              - key: mysql-replication-password
                path: mysql-replication-password
        {{- end }}
        {{- if .Values.secondary.extraVolumes }}
        {{- include "common.tplvalues.render" (dict "value" .Values.secondary.extraVolumes "context" $) | nindent 8 }}
        {{- end }}
  {{- if not .Values.secondary.persistence.enabled }}
        - name: data
          emptyDir: {}
  {{- else }}
  volumeClaimTemplates:
    - metadata:
        name: data
        labels: {{ include "common.labels.matchLabels" . | nindent 10 }}
          app.kubernetes.io/component: secondary
      {{- if .Values.secondary.persistence.annotations }}
        annotations:
          {{- toYaml .Values.secondary.persistence.annotations | nindent 10 }}
      {{- end }}
      spec:
        accessModes:
          {{- range .Values.secondary.persistence.accessModes }}
          - {{ . | quote }}
          {{- end }}
        resources:
          requests:
            storage: {{ .Values.secondary.persistence.size | quote }}
        {{ include "common.storage.class" (dict "persistence" .Values.secondary.persistence "global" .Values.global) }}
        {{- if .Values.secondary.persistence.selector }}
        selector: {{- include "common.tplvalues.render" (dict "value" .Values.secondary.persistence.selector "context" $) | nindent 10 }}
        {{- end -}}
  {{- end }}
{{- end }}
