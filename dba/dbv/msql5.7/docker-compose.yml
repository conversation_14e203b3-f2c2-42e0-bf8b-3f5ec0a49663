version: '3.3'
services:
  database:
    image: mysql:5.7
    container_name: mysql
    restart: always
    environment:
      MYSQL_ROOT_HOST: '%'
      MYSQL_ROOT_PASSWORD: '733d9011-4259-45ae-bcd2-dfcafa28e3b1'
    command:
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
      --max_allowed_packet=1024M;
    expose:
      - '3306'
    ports:
      - '3306:3306'
    volumes:
      - './.mysql-data/db:/var/lib/mysql'
