server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /var/run/positions.yaml

clients:
  - url: http://*************:3100/loki/api/v1/push

scrape_configs:
- job_name: application
  static_configs:
  - targets:
      - localhost
    labels:
      apps: sigma-payment
      env: prod
      __path__: /root/logs/sigma-payment/*log

  - targets:
      - localhost
    labels:
      apps: sigma-web-app
      env: prod
      __path__: /root/logs/sigma-web-app/*log

  - targets:
      - localhost
    labels:
      apps: sigma-product
      env: prod
      __path__: /root/logs/sigma-product/*log

  - targets:
      - localhost
    labels:
      apps: sigma-rights
      env: prod
      __path__: /root/logs/sigma-rights/*log

  - targets:
      - localhost
    labels:
      apps: sigma-third
      env: prod
      __path__: /root/logs/sigma-third/*log

  - targets:
      - localhost
    labels:
      apps: sigma-tool
      env: prod
      __path__: /root/logs/sigma-tool/*log

  - targets:
      - localhost
    labels:
      apps: sigma-user
      env: prod
      __path__: /root/logs/sigma-user/*log
