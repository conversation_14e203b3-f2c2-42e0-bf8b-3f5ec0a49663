version: "3"

networks:
  loki:

services:
  loki:
    image: grafana/loki:2.4.0
    ports:
      - "3100:3100"
    command: -config.file=/etc/loki/local-config.yaml
    volumes:
      - ./data/loki-data:/loki
      - ./local-config.yaml:/etc/loki/local-config.yaml
    environment:
      - TZ=Asia/Shanghai
    networks:
      - loki

  promtail:
    image: grafana/promtail:2.4.0
    privileged: true
    volumes:
      - /var/log:/var/log
    command: -config.file=/etc/promtail/config.yml
    networks:
      - loki

  grafana:
    image: grafana/grafana:latest
    volumes:
      - ./data/grafana-data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=cawURWXT
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    networks:
      - loki
