{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    {{- include "kube-state-metrics.labels" . | indent 4 }}
  name: {{ template "kube-state-metrics.serviceAccountName" . }}
  namespace: {{ template "kube-state-metrics.namespace" . }}
{{- if .Values.serviceAccount.annotations }}
  annotations:
{{ toYaml .Values.serviceAccount.annotations | indent 4 }}
{{- end }}
imagePullSecrets:
  {{- include "kube-state-metrics.imagePullSecrets" (dict "Values" .Values "imagePullSecrets" .Values.serviceAccount.imagePullSecrets) | indent 2 }}
{{- end -}}
