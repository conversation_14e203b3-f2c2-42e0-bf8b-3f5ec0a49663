{{- if .Values.kubeRBACProxy.enabled}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "kube-state-metrics.fullname" . }}-rbac-config
data:
  config-file.yaml: |+
    authorization:
      resourceAttributes:
        namespace: {{ template "kube-state-metrics.namespace" . }}
        apiVersion: v1
        resource: services
        subresource: {{ template "kube-state-metrics.fullname" . }}
        name: {{ template "kube-state-metrics.fullname" . }}
{{- end }}