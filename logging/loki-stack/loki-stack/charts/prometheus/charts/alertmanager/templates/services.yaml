apiVersion: v1
kind: Service
metadata:
  name: {{ include "alertmanager.fullname" . }}
  labels:
    {{- include "alertmanager.labels" . | nindent 4 }}
  {{- with .Values.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  {{- with .Values.service.loadBalancerIP }}
  loadBalancerIP: {{ . }}
  {{- end }}
  {{- with .Values.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
  {{- range $cidr := . }}
    - {{ $cidr }}
  {{- end }}
  {{- end }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
      {{- if (and (eq .Values.service.type "NodePort") .Values.service.nodePort) }}
      nodePort: {{ .Values.service.nodePort }}
      {{- end }}
  selector:
    {{- include "alertmanager.selectorLabels" . | nindent 4 }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "alertmanager.fullname" . }}-headless
  labels:
    {{- include "alertmanager.labels" . | nindent 4 }}
spec:
  clusterIP: None
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
    {{- if or (gt (int .Values.replicaCount) 1) (.Values.additionalPeers) }}
    - port: {{ .Values.service.clusterPort }}
      targetPort: {{ .Values.service.clusterPort }}
      protocol: TCP
      name: cluster-tcp
    - port: {{ .Values.service.clusterPort }}
      targetPort: {{ .Values.service.clusterPort }}
      protocol: UDP
      name: cluster-udp
    {{- end }}
  selector:
    {{- include "alertmanager.selectorLabels" . | nindent 4 }}
