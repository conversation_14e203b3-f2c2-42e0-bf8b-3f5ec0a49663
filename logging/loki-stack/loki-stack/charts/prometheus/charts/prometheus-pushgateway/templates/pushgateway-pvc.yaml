{{- if and (not .Values.runAsStatefulSet) .Values.persistentVolume.enabled (not .Values.persistentVolume.existingClaim) }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  {{- with .Values.persistentVolume.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    {{- include "prometheus-pushgateway.defaultLabels" . | nindent 4 }}
    {{- with .Values.persistentVolumeLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ include "prometheus-pushgateway.fullname" . }}
spec:
  accessModes:
    {{- toYaml .Values.persistentVolume.accessModes | nindent 4 }}
  {{- if .Values.persistentVolume.storageClass }}
  {{- if (eq "-" .Values.persistentVolume.storageClass) }}
  storageClassName: ""
  {{- else }}
  storageClassName: "{{ .Values.persistentVolume.storageClass }}"
  {{- end }}
  {{- end }}
  resources:
    requests:
      storage: "{{ .Values.persistentVolume.size }}"
{{- end }}
