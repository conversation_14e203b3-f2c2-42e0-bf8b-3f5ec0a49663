{{- if .Values.podDisruptionBudget }}
apiVersion: {{ include "prometheus-pushgateway.pdb.apiVersion" . }}
kind: PodDisruptionBudget
metadata:
  labels:
    {{- include "prometheus-pushgateway.defaultLabels" . | nindent 4 }}
  name: {{ include "prometheus-pushgateway.fullname" . }}
spec:
  selector:
    matchLabels:
      {{- include "prometheus-pushgateway.selectorLabels" . | nindent 6 }}
  {{- toYaml .Values.podDisruptionBudget | nindent 2 }}
{{- end }}
