{{- if and .Values.rbac.create .Values.rbac.pspEnabled (.Capabilities.APIVersions.Has "policy/v1beta1/PodSecurityPolicy") }}
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: psp-{{ include "prometheus-node-exporter.fullname" . }}
  labels: 
    {{- include "prometheus-node-exporter.labels" . | nindent 4 }}
rules:
- apiGroups: ['extensions']
  resources: ['podsecuritypolicies']
  verbs:     ['use']
  resourceNames:
    - {{ include "prometheus-node-exporter.fullname" . }}
{{- end }}
