{{- if .Values.server.ingress.enabled -}}
{{- $ingressApiIsStable := eq (include "ingress.isStable" .) "true" -}}
{{- $ingressSupportsIngressClassName := eq (include "ingress.supportsIngressClassName" .) "true" -}}
{{- $ingressSupportsPathType := eq (include "ingress.supportsPathType" .) "true" -}}
{{- $releaseName := .Release.Name -}}
{{- $serviceName := include "prometheus.server.fullname" . }}
{{- $servicePort := .Values.server.service.servicePort -}}
{{- $ingressPath := .Values.server.ingress.path -}}
{{- $ingressPathType := .Values.server.ingress.pathType -}}
{{- $extraPaths := .Values.server.ingress.extraPaths -}}
apiVersion: {{ template "ingress.apiVersion" . }}
kind: Ingress
metadata:
{{- if .Values.server.ingress.annotations }}
  annotations:
{{ toYaml .Values.server.ingress.annotations | indent 4 }}
{{- end }}
  labels:
    {{- include "prometheus.server.labels" . | nindent 4 }}
{{- range $key, $value := .Values.server.ingress.extraLabels }}
    {{ $key }}: {{ $value }}
{{- end }}
  name: {{ template "prometheus.server.fullname" . }}
{{ include "prometheus.namespace" . | indent 2 }}
spec:
  {{- if and $ingressSupportsIngressClassName .Values.server.ingress.ingressClassName }}
  ingressClassName: {{ .Values.server.ingress.ingressClassName }}
  {{- end }}
  rules:
  {{- range .Values.server.ingress.hosts }}
    {{- $url := splitList "/" . }}
    - host: {{ first $url }}
      http:
        paths:
{{ if $extraPaths }}
{{ toYaml $extraPaths | indent 10 }}
{{- end }}
          - path: {{ $ingressPath }}
            {{- if $ingressSupportsPathType }}
            pathType: {{ $ingressPathType }}
            {{- end }}
            backend:
              {{- if $ingressApiIsStable }}
              service:
                name: {{ $serviceName }}
                port:
                  number: {{ $servicePort }}
              {{- else }}
              serviceName: {{ $serviceName }}
              servicePort: {{ $servicePort }}
              {{- end }}
  {{- end -}}
{{- if .Values.server.ingress.tls }}
  tls:
{{ toYaml .Values.server.ingress.tls | indent 4 }}
  {{- end -}}
{{- end -}}
