{{- if .Values.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ template "fluent-bit-loki.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "fluent-bit-loki.name" . }}
    chart: {{ template "fluent-bit-loki.chart" . }}
    heritage: {{ .Release.Service }}
    release: {{ .Release.Name }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ template "fluent-bit-loki.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ template "fluent-bit-loki.serviceAccountName" . }}
{{- end }}
