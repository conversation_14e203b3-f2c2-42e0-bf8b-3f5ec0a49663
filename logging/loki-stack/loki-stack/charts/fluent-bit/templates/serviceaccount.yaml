{{- if .Values.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app: {{ template "fluent-bit-loki.name" . }}
    chart: {{ template "fluent-bit-loki.chart" . }}
    heritage: {{ .Release.Service }}
    release: {{ .Release.Name }}
  name: {{ template "fluent-bit-loki.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
automountServiceAccountToken: {{ .Values.automountServiceAccountToken }}
{{- end }}
