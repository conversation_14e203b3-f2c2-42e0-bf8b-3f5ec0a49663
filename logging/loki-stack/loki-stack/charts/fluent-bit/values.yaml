---
loki:
  serviceName: ""  # Defaults to "${RELEASE}-loki" if not set
  servicePort: 3100
  serviceScheme: http
  servicePath: /api/prom/push
  # user: user
  # password: pass
config:
  port: 2020
  tenantID: '""'
  batchWait: 1
  batchSize: 1048576
  loglevel: warn
  lineFormat: json
  k8sLoggingExclude: "Off"
  k8sLoggingParser: "Off"
  memBufLimit: "5MB"
  bufChunkSize: "32k"
  bufMaxSize: "32k"
  removeKeys:
    - kubernetes
    - stream
  autoKubernetesLabels: false
  labels: '{job="fluent-bit"}'
  labelMap:
    kubernetes:
      namespace_name: namespace
      labels:
        app: app
        release: release
      host: node
      container_name: container
      pod_name: instance
    stream: stream
  # parsers: # Allow to define custom parsers. The key here is the same as the one in the [PARSER] section of parsers.conf file.
  #  - Name: json
  #    Format: json
  #    Time_Key: time
  #    Time_Format: "%d/%b/%Y:%H:%M:%S %z"

  # extraOutputs: # Allow to define extra outputs in addition to the one automatically created
  #   - Name: stdout
  #     Format: json
  #     json_date_format: time

# -- hostAliases to add
hostAliases: []
#  - ip: *******
#    hostnames:
  #      - domain.tld

affinity: {}

annotations: {}

automountServiceAccountToken: true

deploymentStrategy: RollingUpdate

image:
  repository: grafana/fluent-bit-plugin-loki
  tag: 2.1.0-amd64
  pullPolicy: IfNotPresent
  ## Optionally specify an array of imagePullSecrets.
  ## Secrets must be present in the namespace.
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
  # pullSecrets:
  #   - myRegistrKeySecretName

nameOverride: fluent-bit-loki

## Node labels for pod assignment
## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/
nodeSelector: {}

## Pod Labels
podLabels: {}

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "2020"
  prometheus.io/path: /api/v1/metrics/prometheus

## Assign a PriorityClassName to pods if set
# priorityClassName:

rbac:
  create: true
  pspEnabled: true

resources:
  limits:
    memory: 100Mi
  requests:
    cpu: 100m
    memory: 100Mi

serviceAccount:
  create: true
  name:


podSecurityContext: {}

securityContext: {}

## Tolerations for pod assignment
## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
tolerations:
- key: node-role.kubernetes.io/master
  effect: NoSchedule

# Extra volumes to scrape logs from
volumes:
- name: varlog
  hostPath:
    path: /var/log
- name: varlibdockercontainers
  hostPath:
    path: /var/lib/docker/containers

volumeMounts:
- name: varlog
  mountPath: /var/log
- name: varlibdockercontainers
  mountPath: /var/lib/docker/containers
  readOnly: true

serviceMonitor:
  enabled: false
  interval: ""
  additionalLabels: {}
  annotations: {}
  # scrapeTimeout: 10s
