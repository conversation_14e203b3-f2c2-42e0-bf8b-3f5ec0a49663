filebeatConfig:
  filebeat.yml: |
    filebeat.inputs:
    - type: container
      paths:
        - /var/log/containers/*.log
      processors:
      - add_kubernetes_metadata: 
          host: ${NODE_NAME}
          matchers:
          - logs_path:
              logs_path: "/var/log/containers/"

    output.elasticsearch:
      username: '${ELASTICSEARCH_USERNAME}'
      password: '${ELASTIC<PERSON>ARCH_PASSWORD}'
      protocol: https
      hosts: ["security-master:9200"]
      ssl.certificate_authorities:
        - /usr/share/filebeat/config/certs/elastic-certificate.pem

secretMounts:
  - name: elastic-certificate-pem
    secretName: elastic-certificate-pem
    path: /usr/share/filebeat/config/certs

extraEnvs:
  - name: 'ELASTICSEARCH_USERNAME'
    valueFrom:
      secretKeyRef:
        name: elastic-credentials
        key: username
  - name: '<PERSON>LAST<PERSON><PERSON>ARCH_PASSWORD'
    valueFrom:
      secretKeyRef:
        name: elastic-credentials
        key: password
