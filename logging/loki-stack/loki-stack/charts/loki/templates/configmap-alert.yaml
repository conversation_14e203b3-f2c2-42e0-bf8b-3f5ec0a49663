{{- if or (.Values.useExistingAlertingGroup.enabled) (gt (len .Values.alerting_groups) 0) }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "loki.fullname" . }}-alerting-rules
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "loki.labels" . | nindent 4 }}
data:
  {{ template "loki.fullname" . }}-alerting-rules.yaml: |-
    groups:
    {{- toYaml .Values.alerting_groups | nindent 6 }}
{{- end }}