{{- if .Values.rbac.create }}
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ include "promtail.fullname" . }}
  labels:
    {{- include "promtail.labels" . | nindent 4 }}
subjects:
  - kind: ServiceAccount
    name: {{ include "promtail.serviceAccountName" . }}
    namespace: {{ include "promtail.namespaceName" . }}
roleRef:
  kind: ClusterRole
  name: {{ include "promtail.fullname" . }}
  apiGroup: rbac.authorization.k8s.io
{{- end }}
