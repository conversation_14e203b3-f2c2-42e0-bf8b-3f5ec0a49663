{{- if and (.Capabilities.APIVersions.Has "autoscaling.k8s.io/v1") .Values.daemonset.enabled .Values.daemonset.autoscaling.enabled }}
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: {{ include "promtail.fullname" . }}
  namespace: {{ include "promtail.namespaceName" . }}
  labels:
    {{- include "promtail.labels" . | nindent 4 }}
spec:
  {{- with .Values.daemonset.autoscaling.recommenders }}
  recommenders:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  resourcePolicy:
    containerPolicies:
    - containerName: promtail
      {{- with .Values.daemonset.autoscaling.controlledResources }}
      controlledResources:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.daemonset.autoscaling.controlledValues }}
      controlledValues: {{ .Values.daemonset.autoscaling.controlledValues }}
      {{- end }}
      {{- if .Values.daemonset.autoscaling.maxAllowed }}
      maxAllowed:
        {{ toYaml .Values.daemonset.autoscaling.maxAllowed | nindent 8 }}
      {{- end }}
      {{- if .Values.daemonset.autoscaling.minAllowed }}
      minAllowed:
        {{ toYaml .Values.daemonset.autoscaling.minAllowed | nindent 8 }}
      {{- end }}
  targetRef:
    apiVersion: apps/v1
    kind: DaemonSet
    name: {{ include "promtail.fullname" . }}
  {{- with .Values.daemonset.autoscaling.updatePolicy }}
  updatePolicy:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
