{{- if .Values.rbac.create -}}
{{- $fullName := include "logstash.fullname" . -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ $fullName | quote }}
  labels:
    app: "{{ template "logstash.fullname" . }}"
    chart: "{{ .Chart.Name }}"
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
rules:
  - apiGroups:
      - extensions
    resources:
      - podsecuritypolicies
    resourceNames:
      {{- if eq .Values.podSecurityPolicy.name "" }}
      - {{ $fullName | quote }}
      {{- else }}
      - {{ .Values.podSecurityPolicy.name | quote }}
      {{- end }}
    verbs:
      - use
{{- end -}}
