{{- if .Values.rbac.create -}}
{{- $fullName := include "elasticsearch.uname" . -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ $fullName | quote }}
  labels:
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    app: {{ $fullName | quote }}
subjects:
  - kind: ServiceAccount
    {{- if eq .Values.rbac.serviceAccountName "" }}
    name: {{ $fullName | quote }}
    {{- else }}
    name: {{ .Values.rbac.serviceAccountName | quote }}
    {{- end }}
    namespace: {{ .Release.Namespace | quote }}
roleRef:
  kind: Role
  name: {{ $fullName | quote }}
  apiGroup: rbac.authorization.k8s.io
{{- end -}}
