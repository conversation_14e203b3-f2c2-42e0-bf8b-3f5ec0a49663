{{- if .Values.rbac.create -}}
{{- $fullName := include "elasticsearch.uname" . -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  {{- if eq .Values.rbac.serviceAccountName "" }}
  name: {{ $fullName | quote }}
  {{- else }}
  name: {{ .Values.rbac.serviceAccountName | quote }}
  {{- end }}
  annotations:
    {{- with .Values.rbac.serviceAccountAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  labels:
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    app: {{ $fullName | quote }}
{{- end -}}
