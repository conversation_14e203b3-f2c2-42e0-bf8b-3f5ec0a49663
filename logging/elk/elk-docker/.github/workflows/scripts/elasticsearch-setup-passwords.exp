#!/usr/bin/expect -f

# List of expected users with dummy password
set user "(elastic|apm_system|kibana|logstash_system|beats_system|remote_monitoring_user)"
set password "testpasswd"

# Find elasticsearch container id
set MODE [lindex $argv 0]
if { [string match "swarm" $MODE] } {
	set cid [exec docker ps -q -f label=com.docker.swarm.service.name=elk_elasticsearch]
} else {
	set cid [exec docker ps -q -f label=com.docker.compose.service=elasticsearch]
}

set cmd "docker exec -it $cid bin/elasticsearch-setup-passwords interactive -s -b -u http://localhost:9200"

spawn {*}$cmd

expect {
	-re "(E|Ree)nter password for \\\[$user\\\]: " {
		send "$password\r"
		exp_continue
	}
	eof
}

lassign [wait] pid spawnid os_error_flag value
exit $value
