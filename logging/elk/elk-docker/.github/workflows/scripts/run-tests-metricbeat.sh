#!/usr/bin/env bash

set -eu
set -o pipefail


source "$(dirname ${BASH_SOURCE[0]})/lib/testing.sh"


cid_es="$(container_id elasticsearch)"
cid_mb="$(container_id metricbeat)"

ip_es="$(service_ip elasticsearch)"
ip_mb="$(service_ip metricbeat)"

log 'Waiting for readiness of Elasticsearch'
poll_ready "$cid_es" "http://${ip_es}:9200/" -u 'elastic:testpasswd'

log 'Waiting for readiness of Metricbeat'
poll_ready "$cid_mb" "http://${ip_mb}:5066/?pretty"

# We expect to find monitoring entries for the 'elasticsearch' Compose service
# using the following query:
#
#   event.dataset:"docker.container"
#   AND docker.container.name:"docker-elk-elasticsearch-1"
#
log 'Searching a document generated by Metricbeat'

declare response
declare -i count

declare -i was_retried=0

# retry for max 60s (30*2s)
for _ in $(seq 1 30); do
	response="$(curl "http://${ip_es}:9200/metricbeat-*/_search?q=event.dataset:%22docker.container%22%20AND%20docker.container.name:%22docker-elk-elasticsearch-1%22&pretty" -s -u elastic:testpasswd)"

	set +u  # prevent "unbound variable" if assigned value is not an integer
	count="$(jq -rn --argjson data "${response}" '$data.hits.total')"
	set -u

	if (( count > 0 )); then
		break
	fi

	was_retried=1
	echo -n 'x' >&2
	sleep 2
done
if ((was_retried)); then
	# flush stderr, important in non-interactive environments (CI)
	echo >&2
fi

echo "$response"
# Metricbeat buffers metrics until Elasticsearch becomes ready, so we tolerate
# multiple results
if (( count == 0 )); then
	echo 'Expected at least 1 document'
	exit 1
fi
