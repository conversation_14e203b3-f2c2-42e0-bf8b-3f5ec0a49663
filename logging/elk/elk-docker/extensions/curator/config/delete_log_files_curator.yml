actions:
  1:
    action: delete_indices
    description: >-
      Delete indices. Find which to delete by first limiting the list to
      logstash- prefixed indices. Then further filter those to prevent deletion
      of anything less than the number of days specified by unit_count.
      Ignore the error if the filter does not result in an actionable list of
      indices (ignore_empty_list) and exit cleanly.
    options:
      ignore_empty_list: True
      disable_action: False
    filters:
    - filtertype: pattern
      kind: prefix
      value: logstash-
    - filtertype: age
      source: creation_date
      direction: older
      unit: days
      unit_count: 2
