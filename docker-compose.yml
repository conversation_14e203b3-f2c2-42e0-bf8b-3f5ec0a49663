version: '3.9'

x-base_service: &base_service
    ports:
      - "${WEBUI_PORT:-7860}:7860"
    volumes:
      - &v1 ./data:/data
      - &v2 ./output:/output
    stop_signal: SIGKILL
    tty: true
    deploy:
      resources:
        reservations:
          devices:
              - driver: nvidia
                device_ids: ['0']
                capabilities: [compute, utility]

name: webui-docker

services:
  download:
    build: ./services/download/
    profiles: ["download"]
    volumes:
      - *v1

  auto: &automatic
    <<: *base_service
    profiles: ["auto"]
    build: ./services/AUTOMATIC1111
    image: sd-auto:61
    environment:
      - CLI_ARGS=--allow-code --medvram --xformers --enable-insecure-extension-access --api

  auto-cpu:
    <<: *automatic
    profiles: ["auto-cpu"]
    deploy: {}
    environment:
      - CLI_ARGS=--no-half --precision full --allow-code --enable-insecure-extension-access --api

  invoke: &invoke
    <<: *base_service
    profiles: ["invoke"]
    build: ./services/invoke/
    image: sd-invoke:30
    environment:
      - PRELOAD=true
      - CLI_ARGS=--xformers

  # invoke-cpu:
  #   <<: *invoke
  #   profiles: ["invoke-cpu"]
  #   environment:
  #     - PRELOAD=true
  #     - CLI_ARGS=--always_use_cpu

  comfy: &comfy
    <<: *base_service
    profiles: ["comfy"]
    build: ./services/comfy/
    image: sd-comfy:3
    environment:
      - CLI_ARGS=


  comfy-cpu:
    <<: *comfy
    profiles: ["comfy-cpu"]
    deploy: {}
    environment:
      - CLI_ARGS=--cpu
