/*
Ain't got time for using CSS the right way. !important everything!
*/

[data-theme="light"] {
    background-color: #E1E1E1 !important;
}

[data-theme="light"] .card {
    background-color: white !important;
    color: #000;
}

[data-theme="light"] .bg-white {
    background-color: rgb(221, 215, 215) !important;
}

[data-theme="light"] .bg-dark {
    background-color: #ecefff !important;
}

[data-theme="light"] .github-corner > svg{
    fill: #151513 !important;
    color: #fff !important;
}

[data-theme="light"] .rainbow {
    color: #000;
}

[data-theme="light"] .prompt-sign {
    position: absolute;
    top: 25px;
    left: 40px;
    pointer-events: none;
    font-size: 1em;
    color: #2e3e86;
}

[data-theme="light"] .input-group-text {
    background-color: #c0c8f1;
    color: #000;
}

[data-theme="light"] .btn {
    background-color: #1e63b8;
    color: #fff;
    border: none;
}

[data-theme="light"] .highlighted-parameter {
    color: #7223b5;
    font-weight: bold;
    font-size: 1em;
}

[data-theme="light"] .highlighted-warning {
    color: red;
    font-weight: bold;
}

[data-theme="light"] .custom-switch label {
    cursor: pointer;
    user-select: none;
}

[data-theme="light"] .custom-control-input:checked~.custom-control-label::before {
    background-color: #7223b5;
}

[data-theme="light"] #listener-command {
    border: none !important;
    border-radius: 5px;
    /*box-shadow: 10px 10px 20px 0px rgba(209, 209, 209, 0.75); original*/
    box-shadow: rgba(153, 153, 153, 0.16) 0px 3px 6px, rgba(123, 122, 122, 0.23) 0px 3px 6px;
    background-color: rgb(45, 139, 135);
    color: #000;
}

[data-theme="light"] #reverse-shell-command {
    border: none !important;
    border-radius: 5px;
    /*box-shadow: 10px 10px 20px 0px rgba(209, 209, 209, 0.75); original*/
    box-shadow: rgba(153, 153, 153, 0.16) 0px 3px 6px, rgba(123, 122, 122, 0.23) 0px 3px 6px;
    background-color: rgb(45, 139, 135);
    color: #000;
    max-height: 20rem;
}

[data-theme="light"] #bind-shell-command {
    border: none !important;
    border-radius: 5px;
    /*box-shadow: 10px 10px 20px 0px rgba(209, 209, 209, 0.75); original*/
    box-shadow: rgba(153, 153, 153, 0.16) 0px 3px 6px, rgba(123, 122, 122, 0.23) 0px 3px 6px;
    background-color: rgb(45, 139, 135);
    color: #000;
    max-height: 20rem;
}

[data-theme="light"] #msfvenom-command {
    border: none !important;
    border-radius: 5px;
    /*box-shadow: 10px 10px 20px 0px rgba(209, 209, 209, 0.75); original*/
    box-shadow: rgba(153, 153, 153, 0.16) 0px 3px 6px, rgba(123, 122, 122, 0.23) 0px 3px 6px;
    background-color: rgb(45, 139, 135);
    color: #000;
    max-height: 20rem;
}

[data-theme="light"] #hoaxshell-command {
    border: none !important;
    border-radius: 5px;
    /*box-shadow: 10px 10px 20px 0px rgba(209, 209, 209, 0.75); original*/
    box-shadow: rgba(153, 153, 153, 0.16) 0px 3px 6px, rgba(123, 122, 122, 0.23) 0px 3px 6px;
    background-color: rgb(45, 139, 135);
    color: #000;
    max-height: 20rem;
}

[data-theme="light"] .custom-select {
    background-color: #f2f2f2;
    color: #000;
    border-color: #e4e3e2;
}

[data-theme="light"] .nav-link {
    color: #000;
    background: transparent;
    box-shadow: 5px 5px 5px 0px rgba(209, 209, 209, 0.75);
    border: none;
}

[data-theme="light"] .nav-link:hover {
    background-color: #c0c8f1;
}

[data-theme="light"] .nav-link.active {
    background-color: #1e63b8;
    color:#fff;
}

[data-theme="light"] .custom-control-input:checked {
    color: #000;
}

[data-theme="light"] a {
    background-color: #f1c6ce;
    color: #000;
}

[data-theme="light"] .list-group-item {
    background-color: #ecefff;
    color: #000;
    border-color: #AAA;
}

[data-theme="light"] .list-group-item.active {
    background-color: #586edd;
    border-color: #444;
}

[data-theme="light"] .list-group-item:hover {
    background-color: #c0c8f1;
}

[data-theme="light"] .list-group-item.hover {
    background-color: #c0c8f1;
}

[data-theme="light"] .container {
    padding: 20px;
    border-radius: 20px;
    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
    height: 100% !important;
    background: #fff;
    margin: 20px auto;
}

[data-theme="light"] .card-title {
    color: #000 !important;
}

[data-theme="light"] .custom-control-label {
    color: black;
}

[data-theme="light"] h2 {
    color: white;
    text-align: center;
}

[data-theme="light"] .pre-wrap {
    white-space: pre-wrap;
}

[data-theme="light"] .card-body {
    max-height: 40rem;
}
[data-theme="light"] .download-svg {
    filter: none;
}
[data-theme="light"] .download-svg:hover {
    filter: opacity(50%);
}
@font-face {
    font-family: "Comic Mono";
    src: url(../assets/ComicMono.ttf);
}

.shadow {
	margin-bottom: 0px !important;
}

a[href*="t3l3machus"] {
/* Fixes a minor style bug of the "Download Listener" button */
  background: none;
}
