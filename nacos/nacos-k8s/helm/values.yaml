# Default values for nacos.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

global:
  mode: standalone
#  mode: cluster

############################nacos###########################
nacos:
  image:
    repository: nacos/nacos-server
    tag: latest
    pullPolicy: IfNotPresent
  plugin:
    enable: true
    image:
      repository: nacos/nacos-peer-finder-plugin
      tag: 1.1
  replicaCount: 1
  domainName: cluster.local
  preferhostmode: hostname
  serverPort: 8848
  health:
    enabled: false
  storage:
    type: embedded
#    type: mysql
#    db:
#      host: localhost
#      name: nacos
#      port: 3306
#      username: usernmae
#      password: password
#      param: characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useSSL=false

persistence:
  enabled: false
  data:
    accessModes:
      - ReadWriteOnce
    storageClassName: manual
    resources:
      requests:
        storage: 5Gi


service:
  #type: ClusterIP
  type: NodePort
  port: 8848
  nodePort: 30000


ingress:
  enabled: false
  annotations: { }
    # kubernetes.io/ingress.class: nginx
  # kubernetes.io/tls-acme: "true"
  hosts:
    - host: nacos.example.com
      paths: [ ]

  tls: [ ]
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  requests:
    cpu: 500m
    memory: 2Gi
annotations: { }

nodeSelector: { }

tolerations: [ ]

affinity: { }



