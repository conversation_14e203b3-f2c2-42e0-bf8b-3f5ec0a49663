apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "nacos.fullname" . }}
  annotations:
  {{- toYaml .Values.annotations | indent 4 }}
spec:
  {{- if eq .Values.global.mode "cluster" }}
  serviceName: nacos-hs
  {{- else }}
  serviceName: nacos-cs
  {{- end }}
  replicas: {{ .Values.nacos.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "nacos.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "nacos.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      {{- with .Values.nodeSelector }}
      nodeSelector:
      {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
      {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
      {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if and (eq .Values.global.mode "cluster") (.Values.nacos.plugin.enable) }}
      initContainers:
        - name: peer-finder-plugin-install
          image: {{.Values.nacos.plugin.image.repository}}:{{.Values.nacos.plugin.image.tag}}
          imagePullPolicy: Always
          volumeMounts:
            - mountPath: /home/<USER>/plugins/peer-finder
              name: data
              subPath: peer-finder
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.nacos.image.repository }}:{{ .Values.nacos.image.tag }}"
          imagePullPolicy: {{ .Values.nacos.image.pullPolicy }}
          startupProbe:
            initialDelaySeconds: 180
            periodSeconds: 5
            timeoutSeconds: 10
            httpGet:
              scheme: HTTP
              port: {{ .Values.nacos.serverPort }}
              path: /nacos/v1/console/health/readiness
          livenessProbe:
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 10
            httpGet:
              scheme: HTTP
              port: {{ .Values.nacos.serverPort }}
              path: /nacos/v1/console/health/liveness
          ports:
            - name: http
              containerPort: {{ .Values.nacos.serverPort }}
              protocol: TCP
            - containerPort: {{ add .Values.nacos.serverPort 1000}}
              name: client-rpc
            - containerPort: {{ add .Values.nacos.serverPort 1001 }}
              name: raft-rpc
            - containerPort: 7848
              name: old-raft-rpc
          resources:
          {{- toYaml .Values.resources | nindent 12 }}
          env:
            - name: NACOS_SERVER_PORT
              value: {{ .Values.nacos.serverPort | quote }}
            - name: NACOS_APPLICATION_PORT
              value: {{ .Values.nacos.serverPort | quote }}
            - name: PREFER_HOST_MODE
              value: {{ .Values.nacos.preferhostmode | quote }}
            {{- if eq .Values.global.mode "standalone" }}
            - name: MODE
              value: "standalone"

            {{- else if eq .Values.global.mode "cluster" }}
            - name: SERVICE_NAME
              value: "nacos-hs"
            - name: DOMAIN_NAME
              value: {{ .Values.nacos.domainName | quote }}
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
            {{- end }}
            {{- if eq .Values.nacos.storage.type "mysql" }}
            - name: SPRING_DATASOURCE_PLATFORM
              value: "mysql"
            - name: MYSQL_SERVICE_HOST
              valueFrom:
                configMapKeyRef:
                  name: nacos-cm
                  key: mysql.db.host
            - name: MYSQL_SERVICE_DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: nacos-cm
                  key: mysql.db.name
            - name: MYSQL_SERVICE_PORT
              valueFrom:
                configMapKeyRef:
                  name: nacos-cm
                  key: mysql.port
            - name: MYSQL_SERVICE_USER
              valueFrom:
                configMapKeyRef:
                  name: nacos-cm
                  key: mysql.user
            - name: MYSQL_SERVICE_PASSWORD
              valueFrom:
                configMapKeyRef:
                  name: nacos-cm
                  key: mysql.password
            - name: MYSQL_SERVICE_DB_PARAM
              valueFrom:
                configMapKeyRef:
                  name: nacos-cm
                  key: mysql.param
            {{else}}
            - name: EMBEDDED_STORAGE
              value: embedded
          {{end}}
          volumeMounts:
            - name: data
              mountPath: /home/<USER>/plugins/peer-finder
              subPath: peer-finder
            - name: data
              mountPath: /home/<USER>/data
              subPath: data
            - name: data
              mountPath: /home/<USER>/logs
              subPath: logs
      {{- if not .Values.persistence.enabled  }}
      volumes:
        - name: data
          emptyDir: { }
  {{- end }}
  {{- if .Values.persistence.enabled }}
  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
  {{- toYaml .Values.persistence.data | nindent 8 }}
  {{- end }}

