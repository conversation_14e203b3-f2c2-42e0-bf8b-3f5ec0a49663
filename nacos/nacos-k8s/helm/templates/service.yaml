{{- if and (eq .Values.global.mode "cluster") }}
apiVersion: v1
kind: Service
metadata:
  name: nacos-hs
  annotations:
    service.alpha.kubernetes.io/tolerate-unready-endpoints: "true"
spec:
  clusterIP: None
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.nacos.serverPort }}
      protocol: TCP
      name: http
    - port: {{ add .Values.service.port 1000}}
      name: client-rpc
      targetPort: {{add .Values.nacos.serverPort 1000}}
    - port: {{add .Values.service.port 1001}}
      name: raft-rpc
      targetPort: {{add .Values.nacos.serverPort 1001}}
    ## 兼容1.4.x版本的选举端口
    - port: 7848
      name: old-raft-rpc
      targetPort: 7848
      protocol: TCP
  selector:
    app.kubernetes.io/name: {{ include "nacos.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
  {{- end }}
---
apiVersion: v1
kind: Service
metadata:
  name: nacos-cs
  labels:
  {{- toYaml .Values.service.labels | nindent 4 }}
  annotations:
  {{- toYaml .Values.service.annotations | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.nacos.serverPort }}
      protocol: TCP
      name: http
    - port: {{ add .Values.service.port 1000}}
      name: client-rpc
      targetPort: {{add .Values.nacos.serverPort 1000}}
    - port: {{add .Values.service.port 1001}}
      name: raft-rpc
      targetPort: {{add .Values.nacos.serverPort 1001}}
    ## 兼容1.4.x版本的选举端口
    - port: 7848
      name: old-raft-rpc
      targetPort: 7848
      protocol: TCP
      {{- if eq .Values.service.type "NodePort" }}
      nodePort: {{ .Values.service.nodePort }}
  {{- end }}
  selector:
    app.kubernetes.io/name: {{ include "nacos.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}