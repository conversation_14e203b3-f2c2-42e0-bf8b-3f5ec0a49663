{{- if and .Values.podSecurityPolicy.enabled .Values.defaultBackend.enabled -}}
apiVersion: {{ template "podSecurityPolicy.apiVersion" . }}
kind: PodSecurityPolicy
metadata:
  name: {{ template "nginx-ingress.fullname" . }}-backend
  labels:
    app: {{ template "nginx-ingress.name" . }}
    chart: {{ template "nginx-ingress.chart" . }}
    heritage: {{ .Release.Service }}
    release: {{ template "nginx-ingress.releaseLabel" . }}
spec:
  allowPrivilegeEscalation: false
  fsGroup:
    ranges:
    - max: 65535
      min: 1
    rule: MustRunAs
  requiredDropCapabilities:
  - ALL
  runAsUser:
    rule: MustRunAsNonRoot
  seLinux:
    rule: RunAsAny
  supplementalGroups:
    ranges:
    - max: 65535
      min: 1
    rule: MustRunAs
  volumes:
  - configMap
  - emptyDir
  - projected
  - secret
  - downwardAPI
{{- end -}}
