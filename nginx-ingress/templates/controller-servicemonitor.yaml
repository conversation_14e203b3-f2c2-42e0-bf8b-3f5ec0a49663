{{- if and .Values.controller.metrics.enabled .Values.controller.metrics.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "nginx-ingress.controller.fullname" . }}
  {{- if .Values.controller.metrics.serviceMonitor.namespace }}
  namespace: {{ .Values.controller.metrics.serviceMonitor.namespace }}
  {{- end }}
  labels:
    app: {{ template "nginx-ingress.name" . }}
    chart: {{ template "nginx-ingress.chart" . }}
    component: "{{ .Values.controller.name }}"
    heritage: {{ .Release.Service }}
    release: {{ template "nginx-ingress.releaseLabel" . }}
    {{- if .Values.controller.metrics.serviceMonitor.additionalLabels }}
{{ toYaml .Values.controller.metrics.serviceMonitor.additionalLabels | indent 4 }}
    {{- end }}
spec:
  endpoints:
    - port: metrics
      interval: {{ .Values.controller.metrics.serviceMonitor.scrapeInterval }}
      {{- if .Values.controller.metrics.serviceMonitor.honorLabels }}
      honorLabels: true
      {{- end }}
  {{- if .Values.controller.metrics.serviceMonitor.namespaceSelector }}
  namespaceSelector:
{{ toYaml .Values.controller.metrics.serviceMonitor.namespaceSelector | indent 4 -}}
  {{ else }}
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
  {{- end }}
  selector:
    matchLabels:
      app: {{ template "nginx-ingress.name" . }}
      component: "{{ .Values.controller.name }}"
      release: {{ template "nginx-ingress.releaseLabel" . }}
{{- end }}
