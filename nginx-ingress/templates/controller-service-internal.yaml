{{- if and .Values.controller.service.enabled .Values.controller.service.internal.enabled .Values.controller.service.internal.annotations}}
apiVersion: v1
kind: Service
metadata:
  annotations:
  {{- range $key, $value := .Values.controller.service.internal.annotations }}
    {{ $key }}: {{ $value | quote }}
  {{- end }}
  labels:
{{- if .Values.controller.service.labels }}
{{ toYaml .Values.controller.service.labels | indent 4 }}
{{- end }}
    app: {{ template "nginx-ingress.name" . }}
    chart: {{ template "nginx-ingress.chart" . }}
    component: "{{ .Values.controller.name }}"
    heritage: {{ .Release.Service }}
    release: {{ template "nginx-ingress.releaseLabel" . }}
  name: {{ template "nginx-ingress.controller.fullname" . }}-internal
spec:
  ports:
    {{- $setNodePorts := (or (eq .Values.controller.service.type "NodePort") (eq .Values.controller.service.type "LoadBalancer")) }}
    {{- if .Values.controller.service.enableHttp }}
    - name: http
      port: {{ .Values.controller.service.ports.http }}
      protocol: TCP
      targetPort: {{ .Values.controller.service.targetPorts.http }}
      {{- if (and $setNodePorts (not (empty .Values.controller.service.nodePorts.http))) }}
      nodePort: {{ .Values.controller.service.nodePorts.http }}
      {{- end }}
    {{- end }}
    {{- if .Values.controller.service.enableHttps }}
    - name: https
      port: {{ .Values.controller.service.ports.https }}
      protocol: TCP
      targetPort: {{ .Values.controller.service.targetPorts.https }}
      {{- if (and $setNodePorts (not (empty .Values.controller.service.nodePorts.https))) }}
      nodePort: {{ .Values.controller.service.nodePorts.https }}
      {{- end }}
    {{- end }}
  selector:
    app: {{ template "nginx-ingress.name" . }}
    release: {{ template "nginx-ingress.releaseLabel" . }}
    {{ .Values.controller.componentLabelKeyOverride | default "app.kubernetes.io/component" }}: controller
  type: "{{ .Values.controller.service.type }}"
{{- end }}
