{{- if .Values.podSecurityPolicy.enabled}}
apiVersion: {{ template "podSecurityPolicy.apiVersion" . }}
kind: PodSecurityPolicy
metadata:
  name: {{ template "nginx-ingress.fullname" . }}
  labels:
    app: {{ template "nginx-ingress.name" . }}
    chart: {{ template "nginx-ingress.chart" . }}
    heritage: {{ .Release.Service }}
    release: {{ template "nginx-ingress.releaseLabel" . }}
spec:
  allowedCapabilities:
    - NET_BIND_SERVICE
  privileged: false
  allowPrivilegeEscalation: true
  # Allow core volume types.
  volumes:
    - 'configMap'
    #- 'emptyDir'
    - 'projected'
    - 'secret'
    #- 'downwardAPI'
  hostNetwork: {{ .Values.controller.hostNetwork }}
{{- if or .Values.controller.hostNetwork .Values.controller.daemonset.useHostPort }}
  hostPorts:
{{- if .Values.controller.hostNetwork }}
{{- range $key, $value := .Values.controller.containerPort }}
  # {{ $key }}
  - min: {{ $value }}
    max: {{ $value }}
{{- end }}
{{- else if .Values.controller.daemonset.useHostPort }}
{{- range $key, $value := .Values.controller.daemonset.hostPorts }}
  # {{ $key }}
  - min: {{ $value }}
    max: {{ $value }}
{{- end }}
{{- end }}
{{- if .Values.controller.metrics.enabled }}
  # metrics
  - min: {{ .Values.controller.metrics.port }}
    max: {{ .Values.controller.metrics.port }}
{{- end }}
{{- if .Values.controller.admissionWebhooks.enabled }}
  # admission webhooks
  - min: {{ .Values.controller.admissionWebhooks.port }}
    max: {{ .Values.controller.admissionWebhooks.port }}
{{- end }}
{{- range $key, $value := .Values.tcp }}
  # {{ $key }}-tcp
  - min: {{ $key }}
    max: {{ $key }}
{{- end }}
{{- range $key, $value := .Values.udp }}
  # {{ $key }}-udp
  - min: {{ $key }}
    max: {{ $key }}
{{- end }}
{{- end }}
  hostIPC: false
  hostPID: false
  runAsUser:
    # Require the container to run without root privileges.
    rule: 'MustRunAsNonRoot'
  supplementalGroups:
    rule: 'MustRunAs'
    ranges:
      # Forbid adding the root group.
      - min: 1
        max: 65535
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      # Forbid adding the root group.
      - min: 1
        max: 65535
  readOnlyRootFilesystem: false
  seLinux:
    rule: 'RunAsAny'
{{- end }}
