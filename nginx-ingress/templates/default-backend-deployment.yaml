{{- if .Values.defaultBackend.enabled }}
apiVersion: {{ template "deployment.apiVersion" . }}
kind: Deployment
metadata:
  labels:
    app: {{ template "nginx-ingress.name" . }}
    chart: {{ template "nginx-ingress.chart" . }}
    heritage: {{ .Release.Service }}
    release: {{ template "nginx-ingress.releaseLabel" . }}
    {{ .Values.defaultBackend.componentLabelKeyOverride | default "app.kubernetes.io/component" }}: default-backend
    {{- if .Values.defaultBackend.deploymentLabels }}
{{ toYaml .Values.defaultBackend.deploymentLabels | indent 4 }}
    {{- end }}
  name: {{ template "nginx-ingress.defaultBackend.fullname" . }}
spec:
  selector:
    matchLabels:
      app: {{ template "nginx-ingress.name" . }}
      release: {{ template "nginx-ingress.releaseLabel" . }}
    {{- if .Values.defaultBackend.useComponentLabel }}
      {{ .Values.defaultBackend.componentLabelKeyOverride | default "app.kubernetes.io/component" }}: default-backend
    {{- end }}
{{- if not .Values.defaultBackend.autoscaling.enabled }}
  replicas: {{ .Values.defaultBackend.replicaCount }}
{{- end }}
  revisionHistoryLimit: {{ .Values.revisionHistoryLimit }}
  template:
    metadata:
    {{- if .Values.defaultBackend.podAnnotations }}
      annotations:
      {{- range $key, $value := .Values.defaultBackend.podAnnotations }}
        {{ $key }}: {{ $value | quote }}
      {{- end }}
    {{- end }}
      labels:
        app: {{ template "nginx-ingress.name" . }}
        release: {{ template "nginx-ingress.releaseLabel" . }}
        {{ .Values.defaultBackend.componentLabelKeyOverride | default "app.kubernetes.io/component" }}: default-backend
        {{- if .Values.defaultBackend.podLabels }}
{{ toYaml .Values.defaultBackend.podLabels | indent 8 }}
        {{- end }}
    spec:
      {{- if .Values.imagePullSecrets }}
      imagePullSecrets:
{{ toYaml .Values.imagePullSecrets | indent 8 }}
      {{- end }}
{{- if .Values.defaultBackend.priorityClassName }}
      priorityClassName: "{{ .Values.defaultBackend.priorityClassName }}"
{{- end }}
      {{- if .Values.defaultBackend.podSecurityContext }}
      securityContext:
{{ toYaml .Values.defaultBackend.podSecurityContext | indent 8 }}
      {{- end }}
      containers:
        - name: {{ template "nginx-ingress.name" . }}-{{ .Values.defaultBackend.name }}
          {{- with .Values.defaultBackend.image }}
          image: "{{.repository}}{{- if (.digest) -}} @{{.digest}} {{- else -}} :{{ .tag }} {{- end -}}"
          {{- end }}
          imagePullPolicy: "{{ .Values.defaultBackend.image.pullPolicy }}"
          args:
          {{- range $key, $value := .Values.defaultBackend.extraArgs }}
            {{- if $value }}
            - --{{ $key }}={{ $value }}
            {{- else }}
            - --{{ $key }}
            {{- end }}
          {{- end }}
          securityContext:
            runAsUser: {{ .Values.defaultBackend.image.runAsUser }}
          {{- if .Values.defaultBackend.extraEnvs }}
          env:
{{ toYaml .Values.defaultBackend.extraEnvs | indent 12 }}
          {{- end }}
          livenessProbe:
            httpGet:
              path: /healthz
              port: {{ .Values.defaultBackend.port }}
              scheme: HTTP
            initialDelaySeconds: {{ .Values.defaultBackend.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.defaultBackend.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.defaultBackend.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.defaultBackend.livenessProbe.successThreshold }}
            failureThreshold: {{ .Values.defaultBackend.livenessProbe.failureThreshold }}
          readinessProbe:
            httpGet:
              path: /healthz
              port: {{ .Values.defaultBackend.port }}
              scheme: HTTP
            initialDelaySeconds: {{ .Values.defaultBackend.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.defaultBackend.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.defaultBackend.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.defaultBackend.readinessProbe.successThreshold }}
            failureThreshold: {{ .Values.defaultBackend.readinessProbe.failureThreshold }}
          ports:
            - name: http
              containerPort: {{ .Values.defaultBackend.port }}
              protocol: TCP
          resources:
{{ toYaml .Values.defaultBackend.resources | indent 12 }}
    {{- if .Values.defaultBackend.nodeSelector }}
      nodeSelector:
{{ toYaml .Values.defaultBackend.nodeSelector | indent 8 }}
    {{- end }}
      serviceAccountName: {{ template "nginx-ingress.defaultBackend.serviceAccountName" . }}
    {{- if .Values.defaultBackend.tolerations }}
      tolerations:
{{ toYaml .Values.defaultBackend.tolerations | indent 8 }}
    {{- end }}
    {{- if .Values.defaultBackend.affinity }}
      affinity:
{{ toYaml .Values.defaultBackend.affinity | indent 8 }}
    {{- end }}
      terminationGracePeriodSeconds: 60
{{- end }}
