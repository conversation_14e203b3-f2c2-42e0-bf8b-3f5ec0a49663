{{- if and (.Values.rbac.create) (not .Values.rbac.scope) -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app: {{ template "nginx-ingress.name" . }}
    chart: {{ template "nginx-ingress.chart" . }}
    heritage: {{ .Release.Service }}
    release: {{ template "nginx-ingress.releaseLabel" . }}
  name: {{ template "nginx-ingress.fullname" . }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ template "nginx-ingress.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ template "nginx-ingress.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
{{- end -}}
